import React, { createContext, useContext, useState, useEffect, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

type ThemeMode = 'light' | 'dark';

interface ThemeContextType {
  mode: ThemeMode;
  toggleTheme: () => void;
  setThemeMode: (mode: ThemeMode) => void;
  accentColor: string;
  setAccentColor: (color: string) => void;
  fontSize: 'small' | 'medium' | 'large';
  setFontSize: (size: 'small' | 'medium' | 'large') => void;
  animations: boolean;
  setAnimations: (enabled: boolean) => void;
  compactMode: boolean;
  setCompactMode: (compact: boolean) => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

const accentColors = {
  blue: '#2196F3',
  purple: '#9C27B0',
  green: '#4CAF50',
  orange: '#FF9800',
  red: '#F44336',
  teal: '#009688',
  indigo: '#3F51B5',
  pink: '#E91E63',
};

const fontSizeClasses = {
  small: 'text-sm',
  medium: 'text-base',
  large: 'text-lg',
};

interface ThemeProviderProps {
  children: React.ReactNode;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const [mode, setMode] = useState<ThemeMode>(() => {
    const saved = localStorage.getItem('theme-mode');
    return (saved as ThemeMode) || 'dark';
  });

  const [accentColor, setAccentColorState] = useState(() => {
    return localStorage.getItem('theme-accent') || accentColors.blue;
  });

  const [fontSize, setFontSizeState] = useState<'small' | 'medium' | 'large'>(() => {
    const saved = localStorage.getItem('theme-font-size');
    return (saved as 'small' | 'medium' | 'large') || 'medium';
  });

  const [animations, setAnimationsState] = useState(() => {
    const saved = localStorage.getItem('theme-animations');
    return saved !== 'false';
  });

  const [compactMode, setCompactModeState] = useState(() => {
    const saved = localStorage.getItem('theme-compact');
    return saved === 'true';
  });

  const toggleTheme = () => {
    setMode((prevMode) => (prevMode === 'light' ? 'dark' : 'light'));
  };

  const setThemeMode = (newMode: ThemeMode) => {
    setMode(newMode);
  };

  const setAccentColor = (color: string) => {
    setAccentColorState(color);
  };

  const setFontSize = (size: 'small' | 'medium' | 'large') => {
    setFontSizeState(size);
  };

  const setAnimations = (enabled: boolean) => {
    setAnimationsState(enabled);
  };

  const setCompactMode = (compact: boolean) => {
    setCompactModeState(compact);
  };

  // Save preferences to localStorage
  useEffect(() => {
    localStorage.setItem('theme-mode', mode);
  }, [mode]);

  useEffect(() => {
    localStorage.setItem('theme-accent', accentColor);
  }, [accentColor]);

  useEffect(() => {
    localStorage.setItem('theme-font-size', fontSize);
  }, [fontSize]);

  useEffect(() => {
    localStorage.setItem('theme-animations', animations.toString());
  }, [animations]);

  useEffect(() => {
    localStorage.setItem('theme-compact', compactMode.toString());
  }, [compactMode]);

  // Apply theme variables to root
  useEffect(() => {
    const root = document.documentElement;
    
    // Apply mode
    if (mode === 'dark') {
      root.classList.add('dark');
    } else {
      root.classList.remove('dark');
    }

    // Apply accent color as CSS variables
    root.style.setProperty('--accent-color', accentColor);
    
    // Parse hex to RGB for alpha support
    const hex = accentColor.replace('#', '');
    const r = parseInt(hex.substr(0, 2), 16);
    const g = parseInt(hex.substr(2, 2), 16);
    const b = parseInt(hex.substr(4, 2), 16);
    root.style.setProperty('--accent-rgb', `${r}, ${g}, ${b}`);

    // Apply font size
    root.setAttribute('data-font-size', fontSize);
    
    // Apply animations
    root.setAttribute('data-animations', animations.toString());
    
    // Apply compact mode
    root.setAttribute('data-compact', compactMode.toString());

    // Set color scheme for native elements
    root.style.colorScheme = mode;
  }, [mode, accentColor, fontSize, animations, compactMode]);

  const contextValue = useMemo(
    () => ({
      mode,
      toggleTheme,
      setThemeMode,
      accentColor,
      setAccentColor,
      fontSize,
      setFontSize,
      animations,
      setAnimations,
      compactMode,
      setCompactMode,
    }),
    [mode, accentColor, fontSize, animations, compactMode]
  );

  return (
    <ThemeContext.Provider value={contextValue}>
      <AnimatePresence mode="wait">
        <motion.div
          key={mode}
          initial={animations ? { opacity: 0 } : false}
          animate={{ opacity: 1 }}
          exit={animations ? { opacity: 0 } : false}
          transition={{ duration: 0.2 }}
          className="h-full"
        >
          {children}
        </motion.div>
      </AnimatePresence>
    </ThemeContext.Provider>
  );
};

// Export preset themes for quick selection
export const themePresets = {
  default: {
    mode: 'dark' as ThemeMode,
    accentColor: accentColors.blue,
    fontSize: 'medium' as const,
    animations: true,
    compactMode: false,
  },
  light: {
    mode: 'light' as ThemeMode,
    accentColor: accentColors.indigo,
    fontSize: 'medium' as const,
    animations: true,
    compactMode: false,
  },
  highContrast: {
    mode: 'dark' as ThemeMode,
    accentColor: accentColors.orange,
    fontSize: 'large' as const,
    animations: false,
    compactMode: false,
  },
  compact: {
    mode: 'dark' as ThemeMode,
    accentColor: accentColors.teal,
    fontSize: 'small' as const,
    animations: true,
    compactMode: true,
  },
  vibrant: {
    mode: 'dark' as ThemeMode,
    accentColor: accentColors.pink,
    fontSize: 'medium' as const,
    animations: true,
    compactMode: false,
  },
};

export { accentColors };
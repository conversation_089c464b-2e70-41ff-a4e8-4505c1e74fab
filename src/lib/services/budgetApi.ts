import { invoke } from '@tauri-apps/api/core';
import * as mappers from './budgetApiMappers';

// ============= Core Budget Types =============
export interface Budget {
  id: number;
  year: number;
  total_amount: number;
  spent_amount: number;
  created_at: string;
  updated_at: string;
}

export interface Stats {
  year: number;
  total: number;
  spent: number;
  remaining: number;
  count_pending: number;
  count_approved: number;
  count_rejected: number;
  by_category: CategoryTotal[];
}

export interface CategoryTotal {
  category: string;
  total: number;
}

export interface BudgetRequest {
  id: number;
  title: string;
  description?: string;
  category: string;
  amount: number;
  year: number;
  status: string;
  comment?: string;
  created_at: string;
  updated_at: string;
}

export interface RequestFilter {
  status?: string;
  category?: string;
  year?: number;
}

export interface NewRequest {
  title: string;
  description?: string;
  category: string;
  amount: number;
  year: number;
}

export interface UpdateRequest {
  title?: string;
  description?: string;
  category?: string;
  amount?: number;
  year?: number;
  status?: string;
  comment?: string;
}

// ============= Extended Budget Types =============
export interface QuarterlyAllocation {
  quarter: number;
  allocated: number;
  spent: number;
  year: number;
}

export interface DepartmentAllocation {
  id: string;
  name: string;
  allocation: number;
  spent: number;
  year: number;
}

export interface CategoryLimit {
  id: string;
  category: string;
  limit: number;
  spent: number;
  year: number;
}

export interface Expense {
  id: string;
  title: string;
  description: string;
  amount: number;
  category: string;
  department_id: string | null;
  date: string;
  status: 'draft' | 'submitted' | 'paid' | 'reimbursed';
  receipt_url: string | null;
  recurrence: string; // 'none', 'monthly', 'quarterly', 'annual'
  recurrence_end_date: string | null;
  created_at: string;
  updated_at: string;
}

export interface AllocationRule {
  id: string;
  name: string;
  description: string;
  rule_type: string;
  conditions: Record<string, any>;
  actions: Record<string, any>;
  priority: number;
  enabled: boolean;
  created_at: string;
  updated_at: string;
}

export interface BudgetAnalytics {
  year: number;
  total_budget: number;
  total_spent: number;
  total_remaining: number;
  spending_trend: Array<{ month: number; amount: number }>;
  category_breakdown: Array<{ category: string; amount: number; percentage: number }>;
  department_breakdown: Array<{ department: string; amount: number; percentage: number }>;
  forecast: {
    projected_year_end: number;
    projected_variance: number;
    risk_level: 'low' | 'medium' | 'high';
  };
}

export interface BudgetTemplate {
  id: string;
  name: string;
  description: string;
  allocations: Record<string, any>;
  created_at: string;
  updated_at: string;
}

// ============= Core Budget API Functions =============

/**
 * Get budget for a specific year
 */
export async function getBudget(year: number): Promise<Budget> {
  try {
    return await invoke<Budget>('get_budget', { year });
  } catch (error) {
    console.error('Failed to get budget:', error);
    throw error;
  }
}

/**
 * Set budget for a specific year
 */
export async function setBudget(year: number, totalAmount: number): Promise<Budget> {
  try {
    return await invoke<Budget>('set_budget', { year, totalAmount });
  } catch (error) {
    console.error('Failed to set budget:', error);
    throw error;
  }
}

/**
 * Get budget statistics for a specific year
 */
export async function getStats(year: number): Promise<Stats> {
  try {
    return await invoke<Stats>('get_stats', { year });
  } catch (error) {
    console.error('Failed to get stats:', error);
    throw error;
  }
}

/**
 * List budget requests with optional filtering
 */
export async function listRequests(filter?: RequestFilter): Promise<BudgetRequest[]> {
  try {
    return await invoke<BudgetRequest[]>('list_requests', { filter });
  } catch (error) {
    console.error('Failed to list requests:', error);
    throw error;
  }
}

/**
 * Create a new budget request
 */
export async function createRequest(payload: NewRequest): Promise<BudgetRequest> {
  try {
    return await invoke<BudgetRequest>('create_request', { payload });
  } catch (error) {
    console.error('Failed to create request:', error);
    throw error;
  }
}

/**
 * Update an existing budget request
 */
export async function updateRequest(id: number, payload: UpdateRequest): Promise<BudgetRequest> {
  try {
    return await invoke<BudgetRequest>('update_request', { id, payload });
  } catch (error) {
    console.error('Failed to update request:', error);
    throw error;
  }
}

/**
 * Delete a budget request
 */
export async function deleteRequest(id: number): Promise<boolean> {
  try {
    return await invoke<boolean>('delete_request', { id });
  } catch (error) {
    console.error('Failed to delete request:', error);
    throw error;
  }
}

/**
 * Approve a budget request
 */
export async function approveRequest(id: number, comment?: string): Promise<BudgetRequest> {
  try {
    return await invoke<BudgetRequest>('approve_request', { id, comment });
  } catch (error) {
    console.error('Failed to approve request:', error);
    throw error;
  }
}

/**
 * Reject a budget request
 */
export async function rejectRequest(id: number, comment?: string): Promise<BudgetRequest> {
  try {
    return await invoke<BudgetRequest>('reject_request', { id, comment });
  } catch (error) {
    console.error('Failed to reject request:', error);
    throw error;
  }
}

// ============= Extended Budget API Functions =============

/**
 * Get quarterly allocations
 */
export async function getQuarterlyAllocations(year: number): Promise<mappers.QuarterlyAllocation[]> {
  try {
    const result = await invoke<QuarterlyAllocation[]>('get_quarterly_allocations', { year });
    return result.map(mappers.mapQuarterlyAllocation);
  } catch (error) {
    console.error('Failed to get quarterly allocations:', error);
    throw error;
  }
}

/**
 * Update quarterly allocations
 */
export async function updateQuarterlyAllocations(year: number, allocations: QuarterlyAllocation[]): Promise<boolean> {
  try {
    return await invoke<boolean>('update_quarterly_allocations', { year, allocations });
  } catch (error) {
    console.error('Failed to update quarterly allocations:', error);
    throw error;
  }
}

/**
 * Get department allocations
 */
export async function getDepartmentAllocations(year: number): Promise<mappers.DepartmentAllocation[]> {
  try {
    const result = await invoke<DepartmentAllocation[]>('get_department_allocations', { year });
    return result.map(mappers.mapDepartmentAllocation);
  } catch (error) {
    console.error('Failed to get department allocations:', error);
    throw error;
  }
}

/**
 * Update department allocation
 */
export async function updateDepartmentAllocation(id: string, allocation: number): Promise<boolean> {
  try {
    return await invoke<boolean>('update_department_allocation', { id, allocation });
  } catch (error) {
    console.error('Failed to update department allocation:', error);
    throw error;
  }
}

/**
 * Add a new department
 */
export async function addDepartment(name: string, allocation: number): Promise<mappers.DepartmentAllocation> {
  try {
    const result = await invoke<DepartmentAllocation>('add_department', { name, allocation });
    return mappers.mapDepartmentAllocation(result);
  } catch (error) {
    console.error('Failed to add department:', error);
    throw error;
  }
}

/**
 * Remove a department
 */
export async function removeDepartment(id: string): Promise<boolean> {
  try {
    return await invoke<boolean>('remove_department', { id });
  } catch (error) {
    console.error('Failed to remove department:', error);
    throw error;
  }
}

/**
 * Get category limits
 */
export async function getCategoryLimits(year: number): Promise<mappers.CategoryLimit[]> {
  try {
    const result = await invoke<CategoryLimit[]>('get_category_limits', { year });
    return result.map(mappers.mapCategoryLimit);
  } catch (error) {
    console.error('Failed to get category limits:', error);
    throw error;
  }
}

/**
 * Set category limit
 */
export async function setCategoryLimit(category: string, limit: number): Promise<mappers.CategoryLimit> {
  try {
    const result = await invoke<CategoryLimit>('set_category_limit', { category, limit });
    return mappers.mapCategoryLimit(result);
  } catch (error) {
    console.error('Failed to set category limit:', error);
    throw error;
  }
}

/**
 * Remove category limit
 */
export async function removeCategoryLimit(id: string): Promise<boolean> {
  try {
    return await invoke<boolean>('remove_category_limit', { id });
  } catch (error) {
    console.error('Failed to remove category limit:', error);
    throw error;
  }
}

/**
 * Get expenses
 */
export async function getExpenses(year: number): Promise<Expense[]> {
  try {
    return await invoke<Expense[]>('get_expenses', { year });
  } catch (error) {
    console.error('Failed to get expenses:', error);
    throw error;
  }
}

/**
 * Add expense
 */
export async function addExpense(expense: Omit<Expense, 'id' | 'created_at' | 'updated_at'>): Promise<Expense> {
  try {
    // Backend expects all fields including id (even though it generates a new one)
    const expenseWithDefaults = {
      id: '', // Backend will replace this with generated ID
      title: expense.title,
      description: expense.description || '',
      amount: expense.amount,
      category: expense.category,
      department_id: expense.department_id || null,
      date: expense.date,
      status: expense.status,
      receipt_url: expense.receipt_url || null,
      recurrence: expense.recurrence || 'none',
      recurrence_end_date: null,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
    return await invoke<Expense>('add_expense', { expense: expenseWithDefaults });
  } catch (error) {
    console.error('Failed to add expense:', error);
    throw error;
  }
}

/**
 * Update expense
 */
export async function updateExpense(id: string, updates: Partial<Expense> | Expense): Promise<Expense> {
  try {
    // Ensure all required fields are present for the Rust backend
    const expense: Expense = {
      id,
      title: updates.title || '',
      description: updates.description !== undefined ? updates.description : '', // Handle empty descriptions
      amount: updates.amount || 0,
      category: updates.category || '',
      department_id: updates.department_id || null,
      status: updates.status || 'draft',
      date: updates.date || new Date().toISOString().split('T')[0],
      receipt_url: updates.receipt_url || null,
      recurrence: updates.recurrence || 'none',
      recurrence_end_date: updates.recurrence_end_date || null,
      created_at: updates.created_at || new Date().toISOString(),
      updated_at: updates.updated_at || new Date().toISOString()
    };
    
    return await invoke<Expense>('update_expense', { id, expense });
  } catch (error) {
    console.error('Failed to update expense:', error);
    throw error;
  }
}

/**
 * Delete expense
 */
export async function deleteExpense(id: string): Promise<boolean> {
  try {
    return await invoke<boolean>('delete_expense', { id });
  } catch (error) {
    console.error('Failed to delete expense:', error);
    throw error;
  }
}

/**
 * Update expense status
 */
export async function updateExpenseStatus(id: string, status: Expense['status']): Promise<Expense> {
  try {
    return await invoke<Expense>('update_expense_status', { id, status });
  } catch (error) {
    console.error('Failed to update expense status:', error);
    throw error;
  }
}

/**
 * Get allocation rules
 */
export async function getAllocationRules(): Promise<mappers.AllocationRule[]> {
  try {
    const result = await invoke<AllocationRule[]>('get_allocation_rules');
    return result.map(mappers.mapAllocationRule);
  } catch (error) {
    console.error('Failed to get allocation rules:', error);
    throw error;
  }
}

/**
 * Add allocation rule
 */
export async function addAllocationRule(rule: Omit<AllocationRule, 'id' | 'created_at' | 'updated_at'>): Promise<AllocationRule> {
  try {
    return await invoke<AllocationRule>('add_allocation_rule', { rule });
  } catch (error) {
    console.error('Failed to add allocation rule:', error);
    throw error;
  }
}

/**
 * Update allocation rule
 */
export async function updateAllocationRule(id: string, updates: Partial<AllocationRule>): Promise<AllocationRule> {
  try {
    return await invoke<AllocationRule>('update_allocation_rule', { id, updates });
  } catch (error) {
    console.error('Failed to update allocation rule:', error);
    throw error;
  }
}

/**
 * Delete allocation rule
 */
export async function deleteAllocationRule(id: string): Promise<boolean> {
  try {
    return await invoke<boolean>('delete_allocation_rule', { id });
  } catch (error) {
    console.error('Failed to delete allocation rule:', error);
    throw error;
  }
}

/**
 * Toggle allocation rule
 */
export async function toggleAllocationRule(id: string, enabled: boolean): Promise<AllocationRule> {
  try {
    return await invoke<AllocationRule>('toggle_allocation_rule', { id, enabled });
  } catch (error) {
    console.error('Failed to toggle allocation rule:', error);
    throw error;
  }
}

/**
 * Execute allocation rule
 */
export async function executeAllocationRule(id: string): Promise<boolean> {
  try {
    return await invoke<boolean>('execute_allocation_rule', { id });
  } catch (error) {
    console.error('Failed to execute allocation rule:', error);
    throw error;
  }
}

/**
 * Get budget analytics
 */
export async function getBudgetAnalytics(year: number): Promise<BudgetAnalytics> {
  try {
    return await invoke<BudgetAnalytics>('get_budget_analytics', { year });
  } catch (error) {
    console.error('Failed to get budget analytics:', error);
    throw error;
  }
}

/**
 * Get budget templates
 */
export async function getBudgetTemplates(): Promise<BudgetTemplate[]> {
  try {
    return await invoke<BudgetTemplate[]>('get_budget_templates');
  } catch (error) {
    console.error('Failed to get budget templates:', error);
    throw error;
  }
}

/**
 * Save budget template
 */
export async function saveBudgetTemplate(template: Omit<BudgetTemplate, 'id' | 'created_at' | 'updated_at'>): Promise<BudgetTemplate> {
  try {
    return await invoke<BudgetTemplate>('save_budget_template', { template });
  } catch (error) {
    console.error('Failed to save budget template:', error);
    throw error;
  }
}

/**
 * Apply budget template
 */
export async function applyBudgetTemplate(templateId: string, year: number): Promise<boolean> {
  try {
    return await invoke<boolean>('apply_budget_template', { templateId, year });
  } catch (error) {
    console.error('Failed to apply budget template:', error);
    throw error;
  }
}

/**
 * Delete budget template
 */
export async function deleteBudgetTemplate(id: string): Promise<boolean> {
  try {
    return await invoke<boolean>('delete_budget_template', { id });
  } catch (error) {
    console.error('Failed to delete budget template:', error);
    throw error;
  }
}

/**
 * Export analytics
 */
export async function exportAnalytics(year: number, format: 'csv' | 'json' | 'pdf'): Promise<string> {
  try {
    return await invoke<string>('export_analytics', { year, format });
  } catch (error) {
    console.error('Failed to export analytics:', error);
    throw error;
  }
}
// Type mappers to ensure consistency between backend and frontend types

import type { 
  QuarterlyAllocation as BackendQuarterlyAllocation,
  DepartmentAllocation as BackendDepartmentAllocation,
  CategoryLimit as BackendCategoryLimit,
  AllocationRule as BackendAllocationRule 
} from './budgetApi';

// Frontend types (what the store expects)
export interface QuarterlyAllocation {
  quarter: number;
  allocated: number;
  spent: number;
  year: number;
}

export interface DepartmentAllocation {
  id: string;
  name: string;
  amount: number;
  spent: number;
  year: number;
}

export interface CategoryLimit {
  id: string;
  category: string;
  limit: number;
  spent: number;
  year: number;
}

export interface AllocationRule {
  id: string;
  name: string;
  type: 'percentage' | 'fixed' | 'dynamic';
  value: number;
  target: string;
  targetId?: string;
  enabled: boolean;
  conditions: any[];
  actions: any[];
}

// Mappers
export function mapQuarterlyAllocation(backend: BackendQuarterlyAllocation): QuarterlyAllocation {
  return {
    quarter: backend.quarter,
    allocated: backend.allocated,
    spent: backend.spent,
    year: backend.year
  };
}

export function mapDepartmentAllocation(backend: BackendDepartmentAllocation): DepartmentAllocation {
  return {
    id: backend.id,
    name: backend.name,
    amount: backend.allocation || (backend as any).allocated_amount || 0,
    spent: backend.spent || (backend as any).spent_amount || 0,
    year: backend.year
  };
}

export function mapCategoryLimit(backend: BackendCategoryLimit): CategoryLimit {
  return {
    id: backend.id,
    category: backend.category,
    limit: backend.limit || (backend as any).limit_amount || 0,
    spent: backend.spent || (backend as any).spent_amount || 0,
    year: backend.year
  };
}

export function mapAllocationRule(backend: BackendAllocationRule): AllocationRule {
  // Parse conditions and actions if they're strings
  let conditions = backend.conditions || [];
  let actions = backend.actions || [];
  
  if (typeof conditions === 'string') {
    try {
      conditions = JSON.parse(conditions);
    } catch {
      conditions = [];
    }
  }
  
  if (typeof actions === 'string') {
    try {
      actions = JSON.parse(actions);
    } catch {
      actions = [];
    }
  }
  
  return {
    id: backend.id,
    name: backend.name,
    type: backend.rule_type as any || 'fixed',
    value: backend.value || 0,
    target: backend.target || '',
    targetId: backend.target_id,
    enabled: backend.enabled,
    conditions,
    actions
  };
}
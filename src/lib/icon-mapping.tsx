import {
  Activity,
  AlertCircle,
  AlertTriangle,
  Archive,
  ArrowLeft,
  ArrowRight,
  Banknote,
  BarChart,
  BarChart2,
  BarChart3,
  Bot,
  Brain,
  Bug,
  Calculator,
  Check,
  CheckCircle,
  ChevronDown,
  ChevronLeft,
  ChevronRight,
  ChevronUp,
  Circle,
  Clock,
  Cloud,
  CloudDownload,
  Code,
  Copy,
  Database,
  Download,
  Edit as EditIcon2,
  ExternalLink,
  Eye,
  FileText,
  Filter,
  Folder,
  FolderOpen,
  Github,
  Globe,
  Grid3x3,
  HardDrive,
  Hash,
  Heart,
  HelpCircle,
  Home as HomeIcon2,
  Info as InfoIcon2,
  Key,
  Layers,
  LayoutDashboard,
  Link,
  List,
  Lock as LockIcon2,
  LogOut,
  Mail,
  Menu,
  MessageCircle,
  MessageSquare,
  Minus,
  Monitor,
  Moon,
  MoreHorizontal,
  MoreVertical,
  Network,
  Package,
  Palette,
  Pause as PauseIcon2,
  PenTool,
  Percent,
  Pie<PERSON>hart as PieChartIcon2,
  Play,
  Plus,
  Power,
  RefreshCw,
  RotateCcw,
  Save,
  Search,
  Server,
  Settings,
  Share as ShareIcon2,
  Shield,
  ShoppingBag,
  ShoppingCart,
  Sparkles,
  Square,
  Star,
  Sun,
  Tag,
  Target,
  Terminal,
  ThumbsUp,
  ToggleLeft,
  ToggleRight,
  Trash,
  TrendingDown as TrendingDownIcon2,
  TrendingUp,
  Unlock,
  Upload,
  User,
  UserCheck,
  UserPlus,
  Users,
  Wand2,
  X,
  XCircle,
  Zap,
  School,
  GraduationCap,
  Bell,
  BellOff,
  Keyboard,
  HelpCircle as QuestionMark,
  PlayCircle,
  ChevronRight as NavigateNext,
  ChevronLeft as NavigateBefore,
  X as Close,
  Sun as LightMode,
  Moon as DarkMode,
  Palette as PaletteIcon,
  Activity as Speed,
  Shield as Security,
  Cloud as CloudSync,
  Github as GitHubIcon,
  Terminal as TerminalIcon,
  Grid3x3 as Extension,
  Bell as Notifications,
  Keyboard as KeyboardIcon,
  HelpCircle as Help,
  GraduationCap as Tutorial,
  Play as StartIcon,
  UserCircle as PersonIcon,
  Settings as SettingsIcon,
  Code as CodeIcon,
  Bot as AgentIcon,
  CheckCircle as CheckIcon,
  ChevronRight as NextIcon,
  ChevronLeft as BackIcon,
  X as CloseIcon,
  Sun as LightIcon,
  Moon as DarkIcon,
  Activity as SpeedIcon,
  Shield as SecurityIcon,
  Cloud as CloudIcon,
  Folder as FolderIcon,
  Grid3x3 as ExtensionIcon,
  Bell as NotificationsIcon,
  HelpCircle as HelpIcon,
  GraduationCap as TutorialIcon,
  Search as SearchIcon,
  Filter as FilterIcon,
  Download as InstallIcon,
  RefreshCw as UpdateIcon,
  Star as StarIcon,
  TrendingUp as TrendingIcon,
  CheckCircle as VerifiedIcon,
  Banknote as MonetizationOn,
  Banknote as PaidIcon,
  CloudDownload as DownloadIcon,
  MessageSquare as ReviewIcon,
  Grid3x3 as CategoryIcon,
  User as AuthorIcon,
  CheckCircle as InstalledIcon,
  Sparkles as NewIcon,
  Tag as LocalOffer,
  Tag as PriceIcon,
  Activity as PerformanceIcon,
  AlertTriangle as Error,
  AlertCircle as Warning,
  Brain as Memory,
  HardDrive as Storage,
  Network as NetworkCheck,
  Users as Group,
  Eye as Visibility,
  ThumbsUp as ThumbUp,
  MessageSquare as Comment,
  User as Person,
  Grid3x3 as Category,
  Sparkles as NewReleases,
  Banknote as AttachMoney,
  Play as PlayArrow,
  Square as Stop,
  Download as GetApp,
  Trash as Delete,
  Plus as Add,
  ChevronDown as ExpandMore,
  ChevronUp as ExpandLess,
  Target as Task,
  BarChart3 as Reviews,
  BarChart2 as BarChartOutlined,
  Maximize as Fullscreen,
  Minimize as FullscreenExit,
  Home as Welcome,
  User as PersonOutline,
  Check as CheckCircleOutline,
  ChevronDown as ArrowDropDown,
  ChevronUp as ArrowDropUp,
  Copy as ContentCopy,
  Heart as Favorite,
  Heart as FavoriteBorder,
  UserCircle as AccountCircle,
  LogOut as ExitToApp,
  Unlock as LockOpen,
  BellOff as NotificationsOff,
  Sun as Brightness4,
  Sun as Brightness7,
  Globe as Language,
  Globe as Translate,
  HelpCircle as HelpOutline,
  MessageSquare as ContactSupport,
  MessageSquare as Feedback,
  Bug as BugReport,
  Wrench as Build,
  Package as Widgets,
  Clock as Timeline,
  BarChart as Analytics,
  LineChart as ShowChart,
  Circle as BubbleChart,
  BarChart2 as InsertChart,
  BarChart3 as Assessment,
  BarChart2 as Poll,
  Calculator as Functions,
  Calculator as Calculate,
  Database as DataUsage,
  LayoutDashboard as Dashboard,
  Bot as SmartToy,
  ShoppingBag as Store,
  Network as Hub,
} from "lucide-react"

export const iconMapping = {
  Dashboard: LayoutDashboard,
  SmartToy: Bot,
  Store: ShoppingBag,
  Hub: Network,
  Settings,
  PlayArrow: Play,
  Stop: Square,
  Pause: PauseIcon2,
  GetApp: Download,
  CloudDownload,
  Update: RefreshCw,
  Delete: Trash,
  Star,
  Error: AlertTriangle,
  Warning: AlertCircle,
  Memory: Brain,
  Code,
  Cloud,
  Security: Shield,
  Add: Plus,
  Edit: EditIcon2,
  Info: InfoIcon2,
  ExpandMore: ChevronDown,
  ExpandLess: ChevronUp,
  Speed: Activity,
  CheckCircle,
  Task: Target,
  Storage: HardDrive,
  NetworkCheck: Network,
  TrendingUp,
  Group: Users,
  Download,
  Visibility: Eye,
  ThumbUp: ThumbsUp,
  Comment: MessageSquare,
  Person: User,
  Category: Grid3x3,
  NewReleases: Sparkles,
  AttachMoney: Banknote,
  LocalOffer: Tag,
  Search,
  FilterList: Filter,
  Sort: Filter,
  Close: X,
  Clear: X,
  Check,
  ChevronLeft,
  ChevronRight,
  ArrowBack: ArrowLeft,
  ArrowForward: ArrowRight,
  Refresh: RefreshCw,
  MoreVert: MoreVertical,
  MoreHoriz: MoreHorizontal,
  Launch: ExternalLink,
  OpenInNew: ExternalLink,
  FileCopy: Copy,
  ContentCopy: Copy,
  Share: ShareIcon2,
  Favorite: Heart,
  FavoriteBorder: Heart,
  Home: HomeIcon2,
  AccountCircle: UserCircle,
  ExitToApp: LogOut,
  LockOpen: Unlock,
  Lock: LockIcon2,
  Notifications: Bell,
  NotificationsOff: BellOff,
  Brightness4: Sun,
  Brightness7: Sun,
  Language: Globe,
  Translate: Globe,
  Help: HelpCircle,
  HelpOutline: HelpCircle,
  ContactSupport: MessageSquare,
  Feedback: MessageSquare,
  BugReport: Bug,
  Build: Wrench,
  Extension: Grid3x3,
  Widgets: Package,
  Timeline: Clock,
  TrendingDown: TrendingDownIcon2,
  TrendingFlat: TrendingUp,
  Analytics: BarChart,
  BarChart,
  ShowChart: LineChart,
  PieChart: PieChartIcon2,
  BubbleChart: Circle,
  InsertChart: BarChart,
  Assessment: BarChart,
  Poll: BarChart,
  Functions: Calculator,
  Calculate: Calculator,
  DataUsage: Database,
  Verified: CheckCircle,
  MonetizationOn: Banknote,
  Reviews: BarChart3,
  BarChartOutlined: BarChart2,
  Fullscreen: Maximize,
  FullscreenExit: Minimize,
  Welcome: HomeIcon2,
  PersonIcon: User,
  SettingsIcon: Settings,
  CodeIcon: Code,
  AgentIcon: Bot,
  CheckIcon: CheckCircle,
  NextIcon: ChevronRight,
  BackIcon: ChevronLeft,
  CloseIcon: X,
  LightIcon: Sun,
  DarkIcon: Moon,
  PaletteIcon: Palette,
  SpeedIcon: Activity,
  SecurityIcon: Shield,
  CloudIcon: Cloud,
  FolderIcon: Folder,
  GitHubIcon: Github,
  TerminalIcon: Terminal,
  ExtensionIcon: Grid3x3,
  NotificationsIcon: Bell,
  KeyboardIcon: Keyboard,
  HelpIcon: HelpCircle,
  TutorialIcon: GraduationCap,
  StartIcon: Play,
  SearchIcon: Search,
  FilterIcon: Filter,
  InstallIcon: Download,
  UpdateIcon: RefreshCw,
  StarIcon: Star,
  TrendingIcon: TrendingUp,
  VerifiedIcon: CheckCircle,
  PaidIcon: Banknote,
  DownloadIcon: CloudDownload,
  ReviewIcon: MessageSquare,
  CategoryIcon: Grid3x3,
  AuthorIcon: User,
  InstalledIcon: CheckCircle,
  NewIcon: Sparkles,
  PriceIcon: Tag,
  PerformanceIcon: Activity,
  School: GraduationCap,
}

import { Wrench, UserCircle, LineChart, Maximize, Minimize } from "lucide-react"

export type IconName = keyof typeof iconMapping

export const getIcon = (name: IconName) => {
  return iconMapping[name] || HelpCircle
}
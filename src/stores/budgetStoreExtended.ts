import { create } from 'zustand';
import { 
  Budget, 
  Stats, 
  Expense, 
  CategoryLimit, 
  DepartmentAllocation, 
  QuarterlyAllocation,
  useBudgetStore 
} from './budgetStore';
import * as budgetApi from '@/lib/services/budgetApi';
import * as trainingApi from '@/lib/services/trainingApi';

export interface AllocationRule {
  id: string;
  name: string;
  type: 'percentage' | 'fixed' | 'dynamic';
  value: number;
  target: string;
  targetId?: string;
  enabled: boolean;
  conditions: any[];
  actions: any[];
}
import { ROIMetric, AnalyticsData } from '@/components/budget/types/analytics.types';
import { BudgetTemplate } from '@/components/budget/types/planning.types';

// Extended state interface
interface ExtendedBudgetState {
  // Core state from base store
  budget: Budget | null;
  stats: Stats | null;
  loading: boolean;
  year: number;
  expenses: Expense[];
  expensesLoading: boolean;
  
  // Additional state for refactored components
  quarterlyAllocations: QuarterlyAllocation[];
  departmentAllocations: DepartmentAllocation[];
  categoryLimits: CategoryLimit[];
  allocationRules: AllocationRule[];
  analytics: AnalyticsData | null;
  analyticsLoading: boolean;
  rulesLoading: boolean;
  roiMetrics: ROIMetric[];
  templates: BudgetTemplate[];
  reserveFunds: any[];
  reserveTransactions: any[];
  carryoverRules: any[];
}

// Extended actions interface
interface ExtendedBudgetActions {
  // Core actions from base store
  setYear: (y: number) => void;
  fetchBudget: (y?: number) => Promise<void>;
  setBudget: (total: number, y?: number) => Promise<void>;
  fetchStats: (y?: number) => Promise<void>;
  fetchExpenses: (y?: number) => Promise<void>;
  addExpense: (expense: any) => Promise<void>;
  updateExpense: (id: string, updates: any) => Promise<void>;
  deleteExpense: (id: string) => Promise<void>;
  updateExpenseStatus: (id: string, status: string) => Promise<void>;
  
  // Quarterly allocations
  fetchQuarterlyAllocations: (year: number) => Promise<void>;
  updateQuarterlyAllocations: (allocations: any) => Promise<void>;
  
  // Department allocations
  fetchDepartmentAllocations: (year: number) => Promise<void>;
  updateDepartmentAllocation: (departmentId: string, amount: number) => Promise<void>;
  addDepartment: (name: string, amount: number) => Promise<void>;
  removeDepartment: (departmentId: string) => Promise<void>;
  
  // Category limits
  fetchCategoryLimits: (year: number) => Promise<void>;
  setCategoryLimit: (category: string, limit: number) => Promise<void>;
  removeCategoryLimit: (category: string) => Promise<void>;
  
  // Allocation rules
  fetchAllocationRules: () => Promise<void>;
  addAllocationRule: (rule: any) => Promise<void>;
  updateAllocationRule: (id: string, updates: any) => Promise<void>;
  deleteAllocationRule: (id: string) => Promise<void>;
  toggleAllocationRule: (id: string, enabled: boolean) => Promise<void>;
  executeAllocationRule: (id: string) => Promise<void>;
  
  // Analytics
  fetchAnalytics: (year: number) => Promise<void>;
  
  // ROI Metrics
  addROIMetric: (metric: ROIMetric) => void;
  updateROIMetric: (id: string, updates: Partial<ROIMetric>) => void;
  
  // Templates
  saveTemplate: (template: any) => Promise<void>;
  applyTemplate: (id: string) => Promise<void>;
  updateTemplate: (id: string, updates: any) => Promise<void>;
  deleteTemplate: (id: string) => Promise<void>;
  exportTemplate: (id: string) => Promise<void>;
  importTemplate: (file: File) => Promise<void>;
  
  // Import/Export
  importExpenses: (expenses: any[]) => Promise<void>;
  importBudgetConfig: (config: any) => Promise<void>;
}

// Create the extended store
export const useBudgetStoreExtended = create<ExtendedBudgetState & ExtendedBudgetActions>((set, get) => {
  // Get base store instance
  const baseStore = useBudgetStore.getState();
  
  return {
    // Initial state from base store
    budget: baseStore.budget,
    stats: baseStore.stats,
    loading: baseStore.loading,
    year: baseStore.year,
    expenses: baseStore.expenses,
    expensesLoading: baseStore.expensesLoading,
    
    // Additional state
    quarterlyAllocations: [],
    departmentAllocations: [],
    categoryLimits: [],
    allocationRules: [],
    analytics: null,
    analyticsLoading: false,
    rulesLoading: false,
    roiMetrics: [],
    templates: [],
    reserveFunds: [],
    reserveTransactions: [],
    carryoverRules: [],
    
    // Core actions from base store
    setYear: (y) => {
      useBudgetStore.getState().setYear(y);
      set({ year: y });
    },
    
    fetchBudget: async (y) => {
      await useBudgetStore.getState().fetchBudget(y);
      set({ budget: useBudgetStore.getState().budget });
    },
    
    setBudget: async (total, y) => {
      await useBudgetStore.getState().setBudget(total, y);
      set({ budget: useBudgetStore.getState().budget });
    },
    
    fetchStats: async (y) => {
      await useBudgetStore.getState().fetchStats(y);
      set({ stats: useBudgetStore.getState().stats });
    },
    
    fetchExpenses: async (y) => {
      await useBudgetStore.getState().fetchExpenses(y);
      set({ expenses: useBudgetStore.getState().expenses });
    },
    
    addExpense: async (expense) => {
      await useBudgetStore.getState().addExpense(expense);
      set({ expenses: useBudgetStore.getState().expenses });
    },
    
    updateExpense: async (id, updates) => {
      await useBudgetStore.getState().updateExpense(id, updates);
      set({ expenses: useBudgetStore.getState().expenses });
    },
    
    deleteExpense: async (id) => {
      await useBudgetStore.getState().deleteExpense(id);
      set({ expenses: useBudgetStore.getState().expenses });
    },
    
    updateExpenseStatus: async (id, status) => {
      try {
        const updated = await budgetApi.updateExpenseStatus(id, status as any);
        const expenses = get().expenses;
        const updatedExpenses = expenses.map(exp => 
          exp.id === id ? updated : exp
        );
        set({ expenses: updatedExpenses });
      } catch (error) {
        console.error('Failed to update expense status:', error);
        // Fallback to local update
        const expenses = get().expenses;
        const updatedExpenses = expenses.map(exp => 
          exp.id === id ? { ...exp, status: status as any } : exp
        );
        set({ expenses: updatedExpenses });
      }
    },
    
    // Quarterly allocations
    fetchQuarterlyAllocations: async (year) => {
      set({ loading: true });
      try {
        const allocations = await budgetApi.getQuarterlyAllocations(year);
        // Ensure the data has the correct structure
        const normalizedAllocations = allocations.map(a => ({
          quarter: a.quarter,
          allocated: a.allocated || (a as any).amount || 0,
          spent: a.spent || 0,
          year: a.year || year
        }));
        set({ quarterlyAllocations: normalizedAllocations });
      } catch (error) {
        console.error('Failed to fetch quarterly allocations:', error);
        // Note: With real backend implementation, this fallback should not be needed
        // If we reach here, it means the backend returned an error
        set({ quarterlyAllocations: [] });
      } finally {
        set({ loading: false });
      }
    },
    
    updateQuarterlyAllocations: async (allocations) => {
      try {
        const year = get().year;
        await budgetApi.updateQuarterlyAllocations(year, allocations as any);
        set({ quarterlyAllocations: allocations });
      } catch (error) {
        console.error('Failed to update quarterly allocations:', error);
        set({ quarterlyAllocations: allocations });
      }
    },
    
    // Department allocations
    fetchDepartmentAllocations: async (year) => {
      set({ loading: true });
      try {
        const departments = await budgetApi.getDepartmentAllocations(year);
        set({ departmentAllocations: departments as any });
      } catch (error) {
        console.error('Failed to fetch department allocations:', error);
        // With real backend, no fallback needed
        set({ departmentAllocations: [] });
      } finally {
        set({ loading: false });
      }
    },
    
    updateDepartmentAllocation: async (departmentId, amount) => {
      try {
        await budgetApi.updateDepartmentAllocation(departmentId, amount);
        const departments = get().departmentAllocations;
        const updated = departments.map(dept => 
          dept.id === departmentId ? { ...dept, amount } : dept
        );
        set({ departmentAllocations: updated });
      } catch (error) {
        console.error('Failed to update department allocation:', error);
        const departments = get().departmentAllocations;
        const updated = departments.map(dept => 
          dept.id === departmentId ? { ...dept, amount } : dept
        );
        set({ departmentAllocations: updated });
      }
    },
    
    addDepartment: async (name, amount) => {
      try {
        const newDept = await budgetApi.addDepartment(name, amount);
        const departments = get().departmentAllocations;
        set({ departmentAllocations: [...departments, newDept as any] });
      } catch (error) {
        console.error('Failed to add department:', error);
        const departments = get().departmentAllocations;
        const newDept: DepartmentAllocation = {
          id: Date.now().toString(),
          name,
          amount,
          spent: 0,
          year: get().year
        };
        set({ departmentAllocations: [...departments, newDept] });
      }
    },
    
    removeDepartment: async (departmentId) => {
      try {
        await budgetApi.removeDepartment(departmentId);
        const departments = get().departmentAllocations;
        set({ departmentAllocations: departments.filter(d => d.id !== departmentId) });
      } catch (error) {
        console.error('Failed to remove department:', error);
        const departments = get().departmentAllocations;
        set({ departmentAllocations: departments.filter(d => d.id !== departmentId) });
      }
    },
    
    // Category limits
    fetchCategoryLimits: async (year) => {
      set({ loading: true });
      try {
        const limits = await budgetApi.getCategoryLimits(year);
        set({ categoryLimits: limits as any });
      } catch (error) {
        console.error('Failed to fetch category limits:', error);
        set({ categoryLimits: [] });
      } finally {
        set({ loading: false });
      }
    },
    
    setCategoryLimit: async (category, limit) => {
      const limits = get().categoryLimits;
      const existing = limits.find(l => l.category === category);
      
      if (existing) {
        const updated = limits.map(l => 
          l.category === category ? { ...l, limit } : l
        );
        set({ categoryLimits: updated });
      } else {
        const newLimit: CategoryLimit = {
          id: Date.now().toString(),
          category,
          limit,
          spent: 0,
          year: get().year
        };
        set({ categoryLimits: [...limits, newLimit] });
      }
    },
    
    removeCategoryLimit: async (category) => {
      const limits = get().categoryLimits;
      set({ categoryLimits: limits.filter(l => l.category !== category) });
    },
    
    // Allocation rules
    fetchAllocationRules: async () => {
      set({ rulesLoading: true });
      try {
        const rules = await budgetApi.getAllocationRules();
        set({ allocationRules: rules as any });
      } catch (error) {
        console.error('Failed to fetch allocation rules:', error);
        set({ allocationRules: [] });
      } finally {
        set({ rulesLoading: false });
      }
    },
    
    addAllocationRule: async (rule) => {
      const rules = get().allocationRules;
      const newRule = { ...rule, id: Date.now().toString() };
      set({ allocationRules: [...rules, newRule] });
    },
    
    updateAllocationRule: async (id, updates) => {
      const rules = get().allocationRules;
      const updated = rules.map(rule => 
        rule.id === id ? { ...rule, ...updates } : rule
      );
      set({ allocationRules: updated });
    },
    
    deleteAllocationRule: async (id) => {
      const rules = get().allocationRules;
      set({ allocationRules: rules.filter(r => r.id !== id) });
    },
    
    toggleAllocationRule: async (id, enabled) => {
      const rules = get().allocationRules;
      const updated = rules.map(rule => 
        rule.id === id ? { ...rule, enabled } : rule
      );
      set({ allocationRules: updated });
    },
    
    executeAllocationRule: async (id) => {
      console.log('Executing allocation rule:', id);
      // Implementation would apply the rule to current allocations
    },
    
    // Analytics
    fetchAnalytics: async (year) => {
      set({ analyticsLoading: true });
      try {
        // Mock analytics data
        const analytics: AnalyticsData = {
          trends: {
            monthly: [
              { month: 'Jan', spent: 8000, budget: 8333, categories: {} },
              { month: 'Feb', spent: 7500, budget: 8333, categories: {} },
              { month: 'Mar', spent: 9000, budget: 8333, categories: {} },
            ],
            yearly: [
              { year: year - 1, budget: 80000, spent: 75000, growth: 0 },
              { year: year, budget: 100000, spent: 25000, growth: 25 },
            ]
          },
          insights: {
            top_categories: [
              { category: 'Technical Training', amount: 12000, percentage: 48 },
              { category: 'Leadership Development', amount: 8000, percentage: 32 },
              { category: 'Certifications', amount: 5000, percentage: 20 },
            ],
            efficiency_score: 85,
            saving_opportunities: 15000
          }
        };
        set({ analytics });
      } finally {
        set({ analyticsLoading: false });
      }
    },
    
    // ROI Metrics
    addROIMetric: (metric) => {
      const metrics = get().roiMetrics;
      set({ roiMetrics: [...metrics, metric] });
    },
    
    updateROIMetric: (id, updates) => {
      const metrics = get().roiMetrics;
      const updated = metrics.map(m => 
        m.id === id ? { ...m, ...updates } : m
      );
      set({ roiMetrics: updated });
    },
    
    // Templates
    saveTemplate: async (template) => {
      const templates = get().templates;
      const newTemplate = { ...template, id: Date.now().toString() };
      set({ templates: [...templates, newTemplate] });
    },
    
    applyTemplate: async (id) => {
      console.log('Applying template:', id);
      // Would apply template settings to current budget
    },
    
    updateTemplate: async (id, updates) => {
      const templates = get().templates;
      const updated = templates.map(t => 
        t.id === id ? { ...t, ...updates } : t
      );
      set({ templates: updated });
    },
    
    deleteTemplate: async (id) => {
      const templates = get().templates;
      set({ templates: templates.filter(t => t.id !== id) });
    },
    
    exportTemplate: async (id) => {
      console.log('Exporting template:', id);
      // Would export template to file
    },
    
    importTemplate: async (file) => {
      console.log('Importing template from file:', file.name);
      // Would import template from file
    },
    
    // Import/Export
    importExpenses: async (expenses) => {
      const currentExpenses = get().expenses;
      set({ expenses: [...currentExpenses, ...expenses] });
    },
    
    importBudgetConfig: async (config) => {
      console.log('Importing budget config:', config);
      // Would import budget configuration
    },
  };
});

// Helper to sync with base store
export const syncWithBaseStore = () => {
  const baseState = useBudgetStore.getState();
  useBudgetStoreExtended.setState({
    budget: baseState.budget,
    stats: baseState.stats,
    loading: baseState.loading,
    year: baseState.year,
    expenses: baseState.expenses,
    expensesLoading: baseState.expensesLoading,
  });
};

// Subscribe to base store changes
useBudgetStore.subscribe((state) => {
  useBudgetStoreExtended.setState({
    budget: state.budget,
    stats: state.stats,
    loading: state.loading,
    year: state.year,
    expenses: state.expenses,
    expensesLoading: state.expensesLoading,
  });
});
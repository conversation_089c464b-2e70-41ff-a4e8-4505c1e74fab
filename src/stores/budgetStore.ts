import { create } from 'zustand'
import * as budgetApi from '@/lib/services/budgetApi'

// Base types
export type Budget = { 
  id: number; 
  year: number; 
  total_amount: number; 
  spent_amount: number; 
  created_at: string; 
  updated_at: string;
}

export type CategoryTotal = { 
  category: string; 
  total: number;
  amount: number;
  budget: number;
  percentage: number;
}

export type Stats = { 
  year: number; 
  total: number; 
  spent: number; 
  remaining: number; 
  count_pending: number; 
  count_approved: number; 
  count_rejected: number; 
  by_category: CategoryTotal[];
  committed: number;
  utilization: number;
  trend: number;
}

export type ExpenseStatus = 'draft' | 'submitted' | 'paid' | 'reimbursed';

export type Expense = {
  id: string;
  title: string;
  description: string;
  amount: number;
  category: string;
  status: ExpenseStatus;
  date: string;
  created_at: string;
  updated_at: string;
  department_id: string | null;
  recurrence: string; // 'none' | 'weekly' | 'monthly' | 'quarterly' | 'yearly'
  recurrence_end_date: string | null;
  receipt_url: string | null;
}

export type ExpenseRecurrence = 'none' | 'weekly' | 'monthly' | 'quarterly' | 'yearly';

export type CategoryLimit = {
  id: string;
  category: string;
  limit: number;
  spent: number;
  year: number;
}

export type DepartmentAllocation = {
  id: string;
  name: string;
  amount: number;
  spent: number;
  year: number;
}

export type QuarterlyAllocation = {
  quarter: number;
  allocated: number;
  spent: number;
  year: number;
}

// Store state
type State = { 
  budget: Budget | null;
  stats: Stats | null;
  loading: boolean;
  year: number;
  expenses: Expense[];
  expensesLoading: boolean;
}

// Store actions
type Actions = {
  setYear: (y: number) => void;
  fetchBudget: (y?: number) => Promise<void>;
  setBudget: (total: number, y?: number) => Promise<void>;
  fetchStats: (y?: number) => Promise<void>;
  
  // Expense actions
  fetchExpenses: (y?: number) => Promise<void>;
  addExpense: (expense: Omit<Expense, 'id' | 'created_at' | 'updated_at'>) => Promise<void>;
  updateExpense: (id: string, updates: Partial<Expense>) => Promise<void>;
  deleteExpense: (id: string) => Promise<void>;
}

export const useBudgetStore = create<State & Actions>((set, get) => ({
  // Initial state
  budget: null,
  stats: null,
  loading: false,
  year: new Date().getFullYear(),
  expenses: [],
  expensesLoading: false,
  
  // Existing actions
  setYear: (y) => set({ year: y }),
  
  fetchBudget: async (y) => {
    const year = y ?? get().year
    set({ loading: true })
    try {
      const res = await budgetApi.getBudget(year)
      set({ budget: res })
    } catch (error) {
      console.warn('get_budget error:', error)
      // Set a default budget for demo purposes
      set({ 
        budget: {
          id: 1,
          year: year,
          total_amount: 100000,
          spent_amount: 0,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }
      })
    } finally {
      set({ loading: false })
    }
  },
  
  setBudget: async (total, y) => {
    const year = y ?? get().year
    set({ loading: true })
    try {
      const res = await budgetApi.setBudget(year, total)
      set({ budget: res })
      await get().fetchStats(year)
    } catch (error) {
      console.warn('set_budget error:', error)
      // Update the local budget state
      set({ 
        budget: {
          ...get().budget!,
          total_amount: total,
          updated_at: new Date().toISOString()
        }
      })
    } finally {
      set({ loading: false })
    }
  },
  
  fetchStats: async (y) => {
    const year = y ?? get().year
    try {
      const res = await budgetApi.getStats(year)
      set({ stats: res })
    } catch (error) {
      console.warn('get_stats error:', error)
      // Set default stats for demo purposes
      set({
        stats: {
          year: year,
          total: 100000,
          spent: 25000,
          remaining: 75000,
          count_pending: 5,
          count_approved: 10,
          count_rejected: 2,
          by_category: [
            { category: 'Technical Training', total: 30000, amount: 30000, budget: 30000, percentage: 30 },
            { category: 'Leadership Development', total: 25000, amount: 25000, budget: 25000, percentage: 25 },
            { category: 'Certifications', total: 20000, amount: 20000, budget: 20000, percentage: 20 },
            { category: 'Conferences', total: 15000, amount: 15000, budget: 15000, percentage: 15 },
            { category: 'Online Courses', total: 10000, amount: 10000, budget: 10000, percentage: 10 }
          ],
          committed: 5000,
          utilization: 25,
          trend: 0
        }
      })
    }
  },
  
  // Expense actions
  fetchExpenses: async (y) => {
    const year = y ?? get().year
    set({ expensesLoading: true })
    try {
      const res = await budgetApi.getExpenses(year)
      set({ expenses: res })
    } catch (error) {
      console.warn('get_expenses not implemented:', error)
      set({ expenses: [] })
    } finally {
      set({ expensesLoading: false })
    }
  },
  
  addExpense: async (expense) => {
    try {
      const res = await budgetApi.addExpense(expense)
      set({ expenses: [...get().expenses, res] })
      await get().fetchStats()
    } catch (error) {
      console.warn('add_expense error:', error)
      // Add to local state with all required fields
      const newExpense: Expense = {
        id: Date.now().toString(),
        title: expense.title,
        description: expense.description || '',
        amount: expense.amount,
        category: expense.category,
        status: expense.status,
        date: expense.date,
        department_id: expense.department_id || null,
        recurrence: expense.recurrence || 'none',
        recurrence_end_date: null,
        receipt_url: expense.receipt_url || null,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
      set({ expenses: [...get().expenses, newExpense] })
    }
  },
  
  updateExpense: async (id, updates) => {
    try {
      // Find the existing expense to merge with updates
      const existingExpense = get().expenses.find(e => e.id === id)
      if (!existingExpense) {
        throw new Error(`Expense with id ${id} not found`)
      }
      
      // Merge updates with existing expense data
      const fullExpenseData = {
        ...existingExpense,
        ...updates,
        updated_at: new Date().toISOString()
      }
      
      await budgetApi.updateExpense(id, fullExpenseData)
      await get().fetchExpenses()
      await get().fetchStats()
    } catch (error) {
      console.warn('update_expense error:', error)
      // Update local state
      set({
        expenses: get().expenses.map(e =>
          e.id === id ? { ...e, ...updates, updated_at: new Date().toISOString() } : e
        )
      })
    }
  },
  
  deleteExpense: async (id) => {
    try {
      await budgetApi.deleteExpense(id)
      set({ expenses: get().expenses.filter(e => e.id !== id) })
      await get().fetchStats()
    } catch (error) {
      console.warn('delete_expense error:', error)
      // Remove from local state
      set({ expenses: get().expenses.filter(e => e.id !== id) })
    }
  },
}))
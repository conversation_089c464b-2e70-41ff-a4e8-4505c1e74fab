import { useCallback } from 'react';
import { api, type Session } from '@/lib/api';

export const useSystemHealth = () => {
  const fetchSystemHealth = useCallback(async () => {
    const claudeStatus = await api.checkClaudeVersion();
    const startTime = Date.now();
    const processes = await api.listProcesses();
    const responseTime = Date.now() - startTime;
    
    const diskSpace = {
      used: 45.2,
      total: 100,
      percentage: 45.2
    };
    
    const healthScore = claudeStatus.is_installed ? 85 : 50;
    
    return {
      claudeCodeStatus: claudeStatus.is_installed ? 'connected' : 'disconnected',
      claudeVersion: claudeStatus.version,
      apiConnection: responseTime < 100 ? 'online' : responseTime < 500 ? 'slow' : 'offline',
      responseTime,
      diskSpace,
      activeProcesses: processes.length,
      healthScore
    };
  }, []);

  return fetchSystemHealth;
};

export const useWelcomeData = () => {
  const fetchRecentSessions = useCallback(async () => {
    const projects = await api.listProjects();
    const allSessions: Session[] = [];
    
    for (const project of projects.slice(0, 5)) {
      const sessions = await api.getProjectSessions(project.id);
      allSessions.push(...sessions);
    }
    
    return allSessions
      .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
      .slice(0, 5);
  }, []);

  return { fetchRecentSessions };
};
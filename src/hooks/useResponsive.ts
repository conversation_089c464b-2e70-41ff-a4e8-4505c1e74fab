import { useState, useEffect, useCallback, useMemo } from 'react';

// Breakpoint definitions
export const breakpoints = {
  xs: 0,
  sm: 600,
  md: 900,
  lg: 1200,
  xl: 1536,
  xxl: 1920,
} as const;

export type Breakpoint = keyof typeof breakpoints;

// Device type detection
export type DeviceType = 'mobile' | 'tablet' | 'desktop' | 'wide';

// Orientation detection
export type Orientation = 'portrait' | 'landscape';

// Platform detection
export type Platform = 'ios' | 'android' | 'windows' | 'macos' | 'linux' | 'unknown';

interface ResponsiveConfig {
  breakpoint: Breakpoint;
  width: number;
  height: number;
  deviceType: DeviceType;
  orientation: Orientation;
  platform: Platform;
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  isTouch: boolean;
  isRetina: boolean;
  pixelRatio: number;
}

interface ResponsiveValue<T> {
  xs?: T;
  sm?: T;
  md?: T;
  lg?: T;
  xl?: T;
  xxl?: T;
}

// Detect platform
const detectPlatform = (): Platform => {
  const userAgent = navigator.userAgent.toLowerCase();
  
  if (/iphone|ipad|ipod/.test(userAgent)) return 'ios';
  if (/android/.test(userAgent)) return 'android';
  if (/win/.test(userAgent)) return 'windows';
  if (/mac/.test(userAgent)) return 'macos';
  if (/linux/.test(userAgent)) return 'linux';
  
  return 'unknown';
};

// Detect touch capability
const detectTouch = (): boolean => {
  return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
};

// Detect retina display
const detectRetina = (): boolean => {
  return window.devicePixelRatio > 1;
};

// Hook for media queries without MUI dependency
const useMediaQuery = (query: string): boolean => {
  const [matches, setMatches] = useState(() => {
    if (typeof window !== 'undefined') {
      return window.matchMedia(query).matches;
    }
    return false;
  });

  useEffect(() => {
    if (typeof window === 'undefined') return;
    
    const mediaQuery = window.matchMedia(query);
    const handleChange = () => setMatches(mediaQuery.matches);
    
    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, [query]);

  return matches;
};

// Main responsive hook
export const useResponsive = (): ResponsiveConfig => {
  const [dimensions, setDimensions] = useState({
    width: window.innerWidth,
    height: window.innerHeight,
  });

  // Breakpoint queries using custom media query hook
  const isXs = useMediaQuery(`(min-width: ${breakpoints.xs}px) and (max-width: ${breakpoints.sm - 1}px)`);
  const isSm = useMediaQuery(`(min-width: ${breakpoints.sm}px) and (max-width: ${breakpoints.md - 1}px)`);
  const isMd = useMediaQuery(`(min-width: ${breakpoints.md}px) and (max-width: ${breakpoints.lg - 1}px)`);
  const isLg = useMediaQuery(`(min-width: ${breakpoints.lg}px) and (max-width: ${breakpoints.xl - 1}px)`);
  const isXl = useMediaQuery(`(min-width: ${breakpoints.xl}px)`);
  
  const isMobileDown = useMediaQuery(`(max-width: ${breakpoints.sm - 1}px)`);
  const isTabletDown = useMediaQuery(`(max-width: ${breakpoints.md - 1}px)`);
  const isDesktopUp = useMediaQuery(`(min-width: ${breakpoints.lg}px)`);

  // Update dimensions on resize
  useEffect(() => {
    const handleResize = () => {
      setDimensions({
        width: window.innerWidth,
        height: window.innerHeight,
      });
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Determine current breakpoint
  const getCurrentBreakpoint = (): Breakpoint => {
    if (isXs) return 'xs';
    if (isSm) return 'sm';
    if (isMd) return 'md';
    if (isLg) return 'lg';
    if (isXl) return 'xl';
    return 'xxl';
  };

  // Determine device type
  const getDeviceType = (): DeviceType => {
    if (isMobileDown) return 'mobile';
    if (isTabletDown) return 'tablet';
    if (dimensions.width >= breakpoints.xxl) return 'wide';
    return 'desktop';
  };

  // Determine orientation
  const getOrientation = (): Orientation => {
    return dimensions.width > dimensions.height ? 'landscape' : 'portrait';
  };

  return {
    breakpoint: getCurrentBreakpoint(),
    width: dimensions.width,
    height: dimensions.height,
    deviceType: getDeviceType(),
    orientation: getOrientation(),
    platform: detectPlatform(),
    isMobile: isMobileDown,
    isTablet: isTabletDown && !isMobileDown,
    isDesktop: isDesktopUp,
    isTouch: detectTouch(),
    isRetina: detectRetina(),
    pixelRatio: window.devicePixelRatio || 1,
  };
};

// Hook for responsive values
export const useResponsiveValue = <T,>(values: ResponsiveValue<T>): T | undefined => {
  const { breakpoint } = useResponsive();
  
  // Get value for current breakpoint or fall back to smaller breakpoints
  const breakpointOrder: Breakpoint[] = ['xs', 'sm', 'md', 'lg', 'xl', 'xxl'];
  const currentIndex = breakpointOrder.indexOf(breakpoint);
  
  for (let i = currentIndex; i >= 0; i--) {
    const bp = breakpointOrder[i];
    if (values[bp] !== undefined) {
      return values[bp];
    }
  }
  
  // If no smaller breakpoint value, try larger ones
  for (let i = currentIndex + 1; i < breakpointOrder.length; i++) {
    const bp = breakpointOrder[i];
    if (values[bp] !== undefined) {
      return values[bp];
    }
  }
  
  return undefined;
};

// Hook for conditional rendering based on breakpoints
export const useBreakpointValue = <T,>(
  values: ResponsiveValue<T>,
  defaultValue: T
): T => {
  const value = useResponsiveValue(values);
  return value !== undefined ? value : defaultValue;
};

// Hook for responsive styles
export const useResponsiveStyles = <T extends Record<string, any>>(
  styles: ResponsiveValue<T>
): T => {
  const { breakpoint } = useResponsive();
  
  return useMemo(() => {
    const breakpointOrder: Breakpoint[] = ['xs', 'sm', 'md', 'lg', 'xl', 'xxl'];
    const currentIndex = breakpointOrder.indexOf(breakpoint);
    
    let mergedStyles = {} as T;
    
    // Merge styles from smallest to current breakpoint
    for (let i = 0; i <= currentIndex; i++) {
      const bp = breakpointOrder[i];
      if (styles[bp]) {
        mergedStyles = { ...mergedStyles, ...styles[bp] };
      }
    }
    
    return mergedStyles;
  }, [breakpoint, styles]);
};

// Hook for responsive grid columns
export const useGridColumns = (): number => {
  return useBreakpointValue(
    {
      xs: 4,
      sm: 8,
      md: 12,
      lg: 12,
      xl: 12,
      xxl: 12,
    },
    12
  );
};

// Hook for responsive spacing
export const useSpacing = (): number => {
  return useBreakpointValue(
    {
      xs: 1,
      sm: 2,
      md: 3,
      lg: 3,
      xl: 4,
      xxl: 4,
    },
    3
  );
};

// Hook for responsive font sizes
export const useResponsiveFontSize = (
  base: number,
  scale?: ResponsiveValue<number>
): number => {
  const defaultScale = {
    xs: 0.875,
    sm: 0.9375,
    md: 1,
    lg: 1,
    xl: 1.0625,
    xxl: 1.125,
  };
  
  const scaleValue = useBreakpointValue(scale || defaultScale, 1);
  return base * scaleValue;
};

// Hook for responsive container width
export const useContainerWidth = (): string | number => {
  return useBreakpointValue(
    {
      xs: '100%',
      sm: '100%',
      md: 900,
      lg: 1200,
      xl: 1536,
      xxl: 1920,
    },
    '100%'
  );
};

// Hook for responsive drawer width
export const useDrawerWidth = (): number => {
  return useBreakpointValue(
    {
      xs: 240,
      sm: 240,
      md: 280,
      lg: 320,
      xl: 360,
      xxl: 400,
    },
    280
  );
};

// Hook for responsive sidebar behavior
export const useSidebarMode = (): 'permanent' | 'temporary' | 'persistent' => {
  const { isMobile, isTablet } = useResponsive();
  
  if (isMobile) return 'temporary';
  if (isTablet) return 'persistent';
  return 'permanent';
};

// Hook for responsive dialog size
export const useDialogSize = (): 'xs' | 'sm' | 'md' | 'lg' | 'xl' | false => {
  return useBreakpointValue(
    {
      xs: 'xs',
      sm: 'sm',
      md: 'md',
      lg: 'lg',
      xl: 'xl',
    },
    'md'
  );
};

// Hook for responsive image sizes
export const useImageSize = (
  baseWidth: number,
  baseHeight: number
): { width: number; height: number } => {
  const scale = useBreakpointValue(
    {
      xs: 0.5,
      sm: 0.75,
      md: 1,
      lg: 1,
      xl: 1.25,
      xxl: 1.5,
    },
    1
  );
  
  return {
    width: Math.round(baseWidth * scale),
    height: Math.round(baseHeight * scale),
  };
};

// Hook for responsive padding
export const useResponsivePadding = (): {
  xs: number;
  sm: number;
  md: number;
  lg: number;
} => {
  return useBreakpointValue(
    {
      xs: { xs: 1, sm: 1, md: 2, lg: 2 },
      sm: { xs: 1, sm: 2, md: 2, lg: 3 },
      md: { xs: 2, sm: 2, md: 3, lg: 4 },
      lg: { xs: 2, sm: 3, md: 4, lg: 5 },
      xl: { xs: 3, sm: 4, md: 5, lg: 6 },
    },
    { xs: 2, sm: 2, md: 3, lg: 4 }
  );
};

// Hook for responsive list columns
export const useListColumns = (): number => {
  return useBreakpointValue(
    {
      xs: 1,
      sm: 1,
      md: 2,
      lg: 3,
      xl: 4,
      xxl: 5,
    },
    2
  );
};

// Hook for detecting orientation changes
export const useOrientationChange = (callback: (orientation: Orientation) => void) => {
  const { orientation } = useResponsive();
  
  useEffect(() => {
    callback(orientation);
  }, [orientation, callback]);
};

// Hook for detecting breakpoint changes
export const useBreakpointChange = (callback: (breakpoint: Breakpoint) => void) => {
  const { breakpoint } = useResponsive();
  
  useEffect(() => {
    callback(breakpoint);
  }, [breakpoint, callback]);
};

// Utility function to create responsive sx props
export const responsiveSx = <T extends Record<string, any>>(
  values: ResponsiveValue<T>
): any => {
  const result: any = {};
  
  Object.entries(values).forEach(([breakpoint, styles]) => {
    if (styles) {
      Object.entries(styles).forEach(([key, value]) => {
        if (!result[key]) {
          result[key] = {};
        }
        result[key][breakpoint] = value;
      });
    }
  });
  
  return result;
};

// Utility function to clamp values based on screen size
export const clampResponsive = (
  value: number,
  min: ResponsiveValue<number>,
  max: ResponsiveValue<number>
): number => {
  const minValue = useResponsiveValue(min) || 0;
  const maxValue = useResponsiveValue(max) || Infinity;
  
  return Math.max(minValue, Math.min(value, maxValue));
};
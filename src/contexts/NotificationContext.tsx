import React, { createContext, useContext, useState, useCallback, useRef } from 'react';
import {
  X,
  CheckCircle,
  AlertCircle,
  AlertTriangle,
  Info,
  Bell,
  ChevronDown,
  ChevronUp,
} from 'lucide-react';
import * as Toast from '@radix-ui/react-toast';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';

export type NotificationType = 'success' | 'error' | 'warning' | 'info';

export interface Notification {
  id: string;
  type: NotificationType;
  title: string;
  message?: string;
  duration?: number;
  persistent?: boolean;
  action?: {
    label: string;
    onClick: () => void;
  };
  progress?: {
    value: number;
    label?: string;
  };
  timestamp: Date;
  read?: boolean;
}

interface NotificationContextType {
  notifications: Notification[];
  showNotification: (notification: Omit<Notification, 'id' | 'timestamp'>) => void;
  removeNotification: (id: string) => void;
  clearAllNotifications: () => void;
  markAsRead: (id: string) => void;
  markAllAsRead: () => void;
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

export const useNotification = () => {
  const context = useContext(NotificationContext);
  if (!context) {
    throw new Error('useNotification must be used within a NotificationProvider');
  }
  return context;
};

const getIcon = (type: NotificationType) => {
  switch (type) {
    case 'success':
      return <CheckCircle className="text-green-500" size={20} />;
    case 'error':
      return <AlertCircle className="text-red-500" size={20} />;
    case 'warning':
      return <AlertTriangle className="text-yellow-500" size={20} />;
    case 'info':
      return <Info className="text-blue-500" size={20} />;
  }
};

const getColorClasses = (type: NotificationType) => {
  switch (type) {
    case 'success':
      return 'bg-green-50 border-green-200 text-green-800';
    case 'error':
      return 'bg-red-50 border-red-200 text-red-800';
    case 'warning':
      return 'bg-yellow-50 border-yellow-200 text-yellow-800';
    case 'info':
      return 'bg-blue-50 border-blue-200 text-blue-800';
  }
};

interface NotificationProviderProps {
  children: React.ReactNode;
  maxNotifications?: number;
  position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right' | 'top-center' | 'bottom-center';
  defaultDuration?: number;
}

export const NotificationProvider: React.FC<NotificationProviderProps> = ({
  children,
  maxNotifications = 5,
  position = 'top-right',
  defaultDuration = 5000,
}) => {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [openToasts, setOpenToasts] = useState<Set<string>>(new Set());
  const [expandedNotifications, setExpandedNotifications] = useState<Set<string>>(new Set());
  const notificationIdCounter = useRef(0);

  const showNotification = useCallback((notification: Omit<Notification, 'id' | 'timestamp'>) => {
    const id = `notification-${++notificationIdCounter.current}`;
    const newNotification: Notification = {
      ...notification,
      id,
      timestamp: new Date(),
      duration: notification.duration ?? defaultDuration,
    };

    setNotifications(prev => {
      const updated = [newNotification, ...prev];
      if (updated.length > maxNotifications) {
        return updated.slice(0, maxNotifications);
      }
      return updated;
    });

    setOpenToasts(prev => new Set(prev).add(id));
  }, [defaultDuration, maxNotifications]);

  const removeNotification = useCallback((id: string) => {
    setOpenToasts(prev => {
      const next = new Set(prev);
      next.delete(id);
      return next;
    });
    setTimeout(() => {
      setNotifications(prev => prev.filter(n => n.id !== id));
    }, 200);
  }, []);

  const clearAllNotifications = useCallback(() => {
    setOpenToasts(new Set());
    setTimeout(() => {
      setNotifications([]);
    }, 200);
  }, []);

  const markAsRead = useCallback((id: string) => {
    setNotifications(prev =>
      prev.map(n => (n.id === id ? { ...n, read: true } : n))
    );
  }, []);

  const markAllAsRead = useCallback(() => {
    setNotifications(prev => prev.map(n => ({ ...n, read: true })));
  }, []);

  const toggleExpanded = useCallback((id: string) => {
    setExpandedNotifications(prev => {
      const next = new Set(prev);
      if (next.has(id)) {
        next.delete(id);
      } else {
        next.add(id);
      }
      return next;
    });
  }, []);

  const getPositionClasses = () => {
    switch (position) {
      case 'top-left':
        return 'top-4 left-4';
      case 'top-right':
        return 'top-4 right-4';
      case 'bottom-left':
        return 'bottom-4 left-4';
      case 'bottom-right':
        return 'bottom-4 right-4';
      case 'top-center':
        return 'top-4 left-1/2 -translate-x-1/2';
      case 'bottom-center':
        return 'bottom-4 left-1/2 -translate-x-1/2';
      default:
        return 'top-4 right-4';
    }
  };

  return (
    <NotificationContext.Provider
      value={{
        notifications,
        showNotification,
        removeNotification,
        clearAllNotifications,
        markAsRead,
        markAllAsRead,
      }}
    >
      {children}
      <Toast.Provider swipeDirection="right">
        <Toast.Viewport className={cn('fixed z-50 flex flex-col gap-2 max-w-sm', getPositionClasses())} />
        {notifications.map(notification => (
          <Toast.Root
            key={notification.id}
            open={openToasts.has(notification.id)}
            onOpenChange={(open) => {
              if (!open) {
                removeNotification(notification.id);
              }
            }}
            duration={notification.persistent ? Infinity : notification.duration}
            className="group"
          >
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.2 }}
            >
              <div
                className={cn(
                  'relative overflow-hidden rounded-lg border p-4 shadow-lg backdrop-blur-sm',
                  getColorClasses(notification.type),
                  !notification.read && 'font-medium'
                )}
              >
                {notification.progress && (
                  <div className="absolute bottom-0 left-0 right-0 h-1 bg-gray-200">
                    <div
                      className="h-full bg-blue-500 transition-all duration-300"
                      style={{ width: `${notification.progress.value}%` }}
                    />
                  </div>
                )}

                <div className="flex gap-3">
                  <div className="flex-shrink-0 mt-0.5">
                    {getIcon(notification.type)}
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <Toast.Title className="text-sm font-semibold">
                      {notification.title}
                    </Toast.Title>
                    
                    {notification.message && (
                      <Toast.Description
                        className={cn(
                          'text-sm mt-1 transition-all duration-200',
                          expandedNotifications.has(notification.id)
                            ? 'line-clamp-none'
                            : 'line-clamp-2'
                        )}
                      >
                        {notification.message}
                      </Toast.Description>
                    )}

                    {notification.message && notification.message.length > 100 && (
                      <button
                        onClick={() => toggleExpanded(notification.id)}
                        className="text-xs text-gray-600 hover:text-gray-800 mt-1 flex items-center gap-1"
                      >
                        {expandedNotifications.has(notification.id) ? (
                          <>
                            Show less <ChevronUp size={12} />
                          </>
                        ) : (
                          <>
                            Show more <ChevronDown size={12} />
                          </>
                        )}
                      </button>
                    )}

                    {notification.progress?.label && (
                      <p className="text-xs text-gray-600 mt-2">
                        {notification.progress.label}
                      </p>
                    )}

                    {notification.action && (
                      <Toast.Action asChild altText={notification.action.label}>
                        <button
                          onClick={notification.action.onClick}
                          className="mt-2 text-sm font-medium text-blue-600 hover:text-blue-700"
                        >
                          {notification.action.label}
                        </button>
                      </Toast.Action>
                    )}
                  </div>

                  <Toast.Close asChild>
                    <button
                      className="flex-shrink-0 p-1 hover:bg-white/20 rounded transition-colors"
                      onClick={() => {
                        markAsRead(notification.id);
                      }}
                    >
                      <X size={16} />
                    </button>
                  </Toast.Close>
                </div>
              </div>
            </motion.div>
          </Toast.Root>
        ))}
      </Toast.Provider>
    </NotificationContext.Provider>
  );
};

// Notification Center Component
export const NotificationCenter: React.FC = () => {
  const { notifications, clearAllNotifications, markAllAsRead, removeNotification } = useNotification();
  const [isOpen, setIsOpen] = useState(false);

  const unreadCount = notifications.filter(n => !n.read).length;

  return (
    <div className="relative">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="relative p-2 hover:bg-gray-100 rounded-lg transition-colors"
      >
        <Bell size={20} />
        {unreadCount > 0 && (
          <span className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
            {unreadCount > 9 ? '9+' : unreadCount}
          </span>
        )}
      </button>

      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 z-50"
          >
            <div className="p-4 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h3 className="font-semibold">Notifications</h3>
                <div className="flex gap-2">
                  {unreadCount > 0 && (
                    <button
                      onClick={markAllAsRead}
                      className="text-xs text-blue-600 hover:text-blue-700"
                    >
                      Mark all read
                    </button>
                  )}
                  {notifications.length > 0 && (
                    <button
                      onClick={clearAllNotifications}
                      className="text-xs text-red-600 hover:text-red-700"
                    >
                      Clear all
                    </button>
                  )}
                </div>
              </div>
            </div>

            <div className="max-h-96 overflow-y-auto">
              {notifications.length === 0 ? (
                <div className="p-8 text-center text-gray-500">
                  <Bell className="mx-auto mb-2 text-gray-300" size={32} />
                  <p className="text-sm">No notifications</p>
                </div>
              ) : (
                <div className="divide-y divide-gray-100">
                  {notifications.map(notification => (
                    <div
                      key={notification.id}
                      className={cn(
                        'p-4 hover:bg-gray-50 transition-colors',
                        !notification.read && 'bg-blue-50/50'
                      )}
                    >
                      <div className="flex gap-3">
                        <div className="flex-shrink-0 mt-0.5">
                          {getIcon(notification.type)}
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium">{notification.title}</p>
                          {notification.message && (
                            <p className="text-xs text-gray-600 mt-1 line-clamp-2">
                              {notification.message}
                            </p>
                          )}
                          <p className="text-xs text-gray-400 mt-1">
                            {new Date(notification.timestamp).toLocaleTimeString()}
                          </p>
                        </div>
                        <button
                          onClick={() => removeNotification(notification.id)}
                          className="flex-shrink-0 p-1 hover:bg-gray-200 rounded transition-colors"
                        >
                          <X size={14} />
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default NotificationProvider;
import { useState, useEffect } from "react";
import { OutputCacheProvider } from "@/lib/outputCache";
import { TabProvider } from "@/contexts/TabContext";
import { ThemeProvider } from "@/contexts/ThemeContext";
import { Topbar } from "@/components/navigation/Topbar";
import { NFOCredits } from "@/components/display/NFOCredits";
import { ClaudeBinaryDialog } from "@/components/settings/ClaudeBinaryDialog";
import { Toast, ToastContainer } from "@/components/ui/toast";
import { TabManager } from "@/components/navigation/TabManager";
import { TabContent } from "@/components/navigation/TabContent";
import { useTabState } from "@/hooks/useTabState";
import { AgentsModal } from "@/components/agents/AgentsModal";

/**
 * AppContent component - Contains the main app logic, wrapped by providers
 */
function AppContent() {
  const { createClaudeMdTab, createSettingsTab, createUsageTab, createMCPTab, createTrainingBudgetTab, createTrainingNeedsAnalysisTab, createTaskMasterTab } = useTabState();
  const [showNFO, setShowNFO] = useState(false);
  const [showClaudeBinaryDialog, setShowClaudeBinaryDialog] = useState(false);
  const [showAgentsModal, setShowAgentsModal] = useState(false);
  const [toast, setToast] = useState<{ message: string; type: "success" | "error" | "info" } | null>(null);
  
  // Handle toast events from child components
  useEffect(() => {
    const handleShowToast = (event: CustomEvent) => {
      const { message, type } = event.detail;
      setToast({ message, type });
    };

    window.addEventListener('show-toast', handleShowToast as EventListener);
    return () => {
      window.removeEventListener('show-toast', handleShowToast as EventListener);
    };
  }, []);

  // Keyboard shortcuts for tab navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      const isMac = navigator.platform.toUpperCase().indexOf('MAC') >= 0;
      const modKey = isMac ? e.metaKey : e.ctrlKey;
      
      if (modKey) {
        switch (e.key) {
          case 't':
            e.preventDefault();
            window.dispatchEvent(new CustomEvent('create-chat-tab'));
            break;
          case 'w':
            e.preventDefault();
            window.dispatchEvent(new CustomEvent('close-current-tab'));
            break;
          case 'Tab':
            e.preventDefault();
            if (e.shiftKey) {
              window.dispatchEvent(new CustomEvent('switch-to-previous-tab'));
            } else {
              window.dispatchEvent(new CustomEvent('switch-to-next-tab'));
            }
            break;
          default:
            // Handle number keys 1-9
            if (e.key >= '1' && e.key <= '9') {
              e.preventDefault();
              const index = parseInt(e.key) - 1;
              window.dispatchEvent(new CustomEvent('switch-to-tab-by-index', { detail: { index } }));
            }
            break;
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, []);

  // Listen for Claude not found events
  useEffect(() => {
    const handleClaudeNotFound = () => {
      setShowClaudeBinaryDialog(true);
    };

    window.addEventListener('claude-not-found', handleClaudeNotFound as EventListener);
    return () => {
      window.removeEventListener('claude-not-found', handleClaudeNotFound as EventListener);
    };
  }, []);


  return (
    <div className="h-screen bg-background flex flex-col">
      <Topbar
        onClaudeClick={() => createClaudeMdTab()}
        onSettingsClick={() => createSettingsTab()}
        onUsageClick={() => createUsageTab()}
        onMCPClick={() => createMCPTab()}
        onInfoClick={() => setShowNFO(true)}
        onAgentsClick={() => setShowAgentsModal(true)}
        onTrainingBudgetClick={() => createTrainingBudgetTab()}
        onTrainingNeedsAnalysisClick={() => createTrainingNeedsAnalysisTab()}
        onTaskMasterClick={() => createTaskMasterTab()}
      />
      
      {/* Tab Interface */}
      <TabManager />
      <div className="flex-1 overflow-hidden">
        <TabContent />
      </div>
      {showNFO && <NFOCredits onClose={() => setShowNFO(false)} />}
      <AgentsModal 
        open={showAgentsModal}
        onOpenChange={setShowAgentsModal}
      />
      <ClaudeBinaryDialog
        open={showClaudeBinaryDialog}
        onOpenChange={setShowClaudeBinaryDialog}
        onSuccess={() => { setToast({ message: "Claude binary path saved successfully", type: "success" }); window.location.reload(); }}
        onError={(message) => setToast({ message, type: "error" })}
      />
      <ToastContainer>
        {toast && (
          <Toast message={toast.message} type={toast.type} onDismiss={() => setToast(null)} />
        )}
      </ToastContainer>
    </div>
  );
}

/**
 * Main App component - Wraps the app with providers
 */
function App() {
  return (
    <ThemeProvider>
      <OutputCacheProvider>
        <TabProvider>
          <AppContent />
        </TabProvider>
      </OutputCacheProvider>
    </ThemeProvider>
  );
}

export default App;

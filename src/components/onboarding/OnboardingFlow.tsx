import React, { useState, useEffect } from 'react';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import { Stepper, Step, StepLabel } from '@/components/ui/stepper';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { FormControl, FormGroup, FormControlLabel, InputLabel } from '@/components/ui/form';
import { Checkbox } from '@/components/ui/checkbox';
import { Card, CardContent } from '@/components/ui/card';
import { Grid } from '@/components/ui/grid';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Chip } from '@/components/ui/chip';
import { LinearProgress } from '@/components/ui/linear-progress';
import { Alert } from '@/components/ui/alert';
import { Container } from '@/components/ui/container';
import { Paper } from '@/components/ui/paper';
import { List, ListItem, ListItemIcon, ListItemText } from '@/components/ui/list';
import { Divider } from '@/components/ui/divider';
import { ToggleButton, ToggleButtonGroup } from '@/components/ui/toggle-button';
import { Label } from '@/components/ui/label';
import { cn } from '@/lib/utils';
import {
  Home as WelcomeIcon,
  User as PersonIcon,
  Settings as SettingsIcon,
  Code as CodeIcon,
  Bot as AgentIcon,
  CheckCircle as CheckIcon,
  ChevronRight as NextIcon,
  ChevronLeft as BackIcon,
  X as CloseIcon,
  Sun as LightIcon,
  Moon as DarkIcon,
  Palette as PaletteIcon,
  Gauge as SpeedIcon,
  Shield as SecurityIcon,
  Cloud as CloudIcon,
  Folder as FolderIcon,
  Github as GitHubIcon,
  Terminal as TerminalIcon,
  Puzzle as ExtensionIcon,
  Bell as NotificationsIcon,
  Keyboard as KeyboardIcon,
  HelpCircle as HelpIcon,
  GraduationCap as TutorialIcon,
  Play as StartIcon,
  GraduationCap as School,
  Bot as SmartToy,
} from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { invoke } from '@tauri-apps/api/core';
import { useTheme } from '../../theme/ThemeProvider';
import { useNotification } from '../../contexts/NotificationContext';

interface OnboardingData {
  name: string;
  role: string;
  experience: string;
  preferences: {
    theme: 'light' | 'dark' | 'auto';
    accentColor: string;
    animations: boolean;
    compactMode: boolean;
    notifications: boolean;
    telemetry: boolean;
  };
  features: {
    fileManager: boolean;
    gitIntegration: boolean;
    workflowAutomation: boolean;
    agentSystem: boolean;
    mcpServers: boolean;
  };
  integrations: {
    github?: string;
    openai?: string;
    anthropic?: string;
  };
  workspace: string;
}

const steps = [
  'Welcome',
  'Personal Info',
  'Preferences',
  'Features',
  'Integrations',
  'Get Started',
];

const experienceLevels = [
  { value: 'beginner', label: 'Beginner', description: 'New to AI-powered development' },
  { value: 'intermediate', label: 'Intermediate', description: 'Some experience with AI tools' },
  { value: 'advanced', label: 'Advanced', description: 'Experienced AI developer' },
  { value: 'expert', label: 'Expert', description: 'AI/ML professional' },
];

const roles = [
  { value: 'developer', label: 'Software Developer', icon: <CodeIcon className="h-4 w-4" /> },
  { value: 'data-scientist', label: 'Data Scientist', icon: <AgentIcon className="h-4 w-4" /> },
  { value: 'researcher', label: 'Researcher', icon: <School className="h-4 w-4" /> },
  { value: 'student', label: 'Student', icon: <School className="h-4 w-4" /> },
  { value: 'other', label: 'Other', icon: <PersonIcon className="h-4 w-4" /> },
];

interface OnboardingFlowProps {
  open: boolean;
  onComplete: (data: OnboardingData) => void;
  onSkip?: () => void;
}

export const OnboardingFlow: React.FC<OnboardingFlowProps> = ({
  open,
  onComplete,
  onSkip,
}) => {
  const [activeStep, setActiveStep] = useState(0);
  const [data, setData] = useState<OnboardingData>({
    name: '',
    role: 'developer',
    experience: 'intermediate',
    preferences: {
      theme: 'dark',
      accentColor: '#2196F3',
      animations: true,
      compactMode: false,
      notifications: true,
      telemetry: false,
    },
    features: {
      fileManager: true,
      gitIntegration: true,
      workflowAutomation: true,
      agentSystem: true,
      mcpServers: true,
    },
    integrations: {},
    workspace: '',
  });
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  
  const theme = useTheme();
  const { showNotification } = useNotification();

  useEffect(() => {
    loadDefaultWorkspace();
  }, []);

  const loadDefaultWorkspace = async () => {
    try {
      const defaultPath = await invoke<string>('get_default_workspace');
      setData(prev => ({ ...prev, workspace: defaultPath }));
    } catch (error) {
      console.error('Failed to load default workspace:', error);
    }
  };

  const handleNext = () => {
    if (validateStep(activeStep)) {
      if (activeStep === steps.length - 1) {
        handleComplete();
      } else {
        setActiveStep((prev) => prev + 1);
      }
    }
  };

  const handleBack = () => {
    setActiveStep((prev) => prev - 1);
  };

  const validateStep = (step: number): boolean => {
    const newErrors: Record<string, string> = {};

    switch (step) {
      case 1: // Personal Info
        if (!data.name.trim()) {
          newErrors.name = 'Name is required';
        }
        break;
      case 4: // Integrations
        if (data.integrations.github && !isValidUrl(data.integrations.github)) {
          newErrors.github = 'Invalid GitHub URL';
        }
        break;
      case 5: // Get Started
        if (!data.workspace.trim()) {
          newErrors.workspace = 'Workspace path is required';
        }
        break;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const isValidUrl = (url: string): boolean => {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  };

  const handleComplete = async () => {
    setLoading(true);
    try {
      theme.setThemeMode(data.preferences.theme === 'auto' ? 'dark' : data.preferences.theme);
      theme.setAccentColor(data.preferences.accentColor);
      theme.setAnimations(data.preferences.animations);
      theme.setCompactMode(data.preferences.compactMode);

      await invoke('save_onboarding_data', { data });

      notifications.showNotification({
        type: 'success',
        title: 'Welcome to Claudia!',
        message: 'Your workspace has been configured successfully.',
        duration: 5000,
      });

      onComplete(data);
    } catch (error) {
      console.error('Failed to complete onboarding:', error);
      notifications.showNotification({
        type: 'error',
        title: 'Setup Failed',
        message: 'Failed to complete setup. Please try again.',
      });
    } finally {
      setLoading(false);
    }
  };

  const renderStepContent = (step: number) => {
    switch (step) {
      case 0: // Welcome
        return (
          <div className="fade-in">
            <div className="text-center py-4">
              <motion.div
                initial={{ scale: 0.5, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ duration: 0.5, ease: 'easeOut' }}
              >
                <Avatar className="w-30 h-30 mx-auto mb-6">
                  <AvatarFallback className="bg-primary text-primary-foreground">
                    <WelcomeIcon className="h-16 w-16" />
                  </AvatarFallback>
                </Avatar>
              </motion.div>
              
              <h2 className="text-3xl font-bold mb-2">Welcome to Claudia</h2>
              
              <p className="text-lg text-muted-foreground mb-6">
                Your AI-Powered Development Assistant
              </p>
              
              <div className="mt-8 mb-6">
                <p className="mb-4">
                  Let's get you set up in just a few steps. This wizard will help you:
                </p>
                
                <Grid container spacing={2} className="justify-center mt-4">
                  <Grid item xs={12} sm={6} md={3}>
                    <Card className="border">
                      <CardContent className="text-center p-4">
                        <PersonIcon className="h-10 w-10 text-primary mb-2" />
                        <p className="text-sm">Personalize your experience</p>
                      </CardContent>
                    </Card>
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <Card className="border">
                      <CardContent className="text-center p-4">
                        <SettingsIcon className="h-10 w-10 text-primary mb-2" />
                        <p className="text-sm">Configure preferences</p>
                      </CardContent>
                    </Card>
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <Card className="border">
                      <CardContent className="text-center p-4">
                        <ExtensionIcon className="h-10 w-10 text-primary mb-2" />
                        <p className="text-sm">Enable features</p>
                      </CardContent>
                    </Card>
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <Card className="border">
                      <CardContent className="text-center p-4">
                        <CloudIcon className="h-10 w-10 text-primary mb-2" />
                        <p className="text-sm">Connect integrations</p>
                      </CardContent>
                    </Card>
                  </Grid>
                </Grid>
              </div>
              
              <p className="text-sm text-muted-foreground">
                This will only take 2-3 minutes
              </p>
            </div>
          </div>
        );

      case 1: // Personal Info
        return (
          <div className="slide-in-left">
            <div className="py-2">
              <h3 className="text-xl font-semibold mb-2">Tell us about yourself</h3>
              <p className="text-sm text-muted-foreground mb-6">
                This helps us customize your experience
              </p>
              
              <div className="space-y-6">
                <div>
                  <Label htmlFor="name">Your Name</Label>
                  <Input
                    id="name"
                    value={data.name}
                    onChange={(e) => setData({ ...data, name: e.target.value })}
                    placeholder="John Doe"
                    className={errors.name ? 'border-destructive' : ''}
                  />
                  {errors.name && <p className="text-sm text-destructive mt-1">{errors.name}</p>}
                </div>
                
                <div>
                  <Label className="mb-2 block">What's your role?</Label>
                  <ToggleButtonGroup
                    value={data.role}
                    onValueChange={(value) => value && setData({ ...data, role: value })}
                    type="single"
                    className="flex-wrap"
                  >
                    {roles.map((role) => (
                      <ToggleButton key={role.value} value={role.value} className="flex-col gap-1">
                        {role.icon}
                        <span className="text-xs">{role.label}</span>
                      </ToggleButton>
                    ))}
                  </ToggleButtonGroup>
                </div>
                
                <div>
                  <Label className="mb-2 block">Experience Level</Label>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                    {experienceLevels.map((level) => (
                      <Card
                        key={level.value}
                        className={cn(
                          "cursor-pointer transition-all",
                          data.experience === level.value ? 'border-primary ring-2 ring-primary' : 'border hover:border-primary/50'
                        )}
                        onClick={() => setData({ ...data, experience: level.value })}
                      >
                        <CardContent className="p-4">
                          <div className="flex justify-between items-center">
                            <div>
                              <p className="font-medium">{level.label}</p>
                              <p className="text-xs text-muted-foreground">
                                {level.description}
                              </p>
                            </div>
                            {data.experience === level.value && (
                              <CheckIcon className="h-5 w-5 text-primary" />
                            )}
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        );

      case 2: // Preferences
        return (
          <div className="slide-in-left">
            <div className="py-2">
              <h3 className="text-xl font-semibold mb-2">Customize your experience</h3>
              <p className="text-sm text-muted-foreground mb-6">
                Choose your preferred settings
              </p>
              
              <div className="space-y-6">
                <div>
                  <Label className="mb-2 block">Theme Mode</Label>
                  <ToggleButtonGroup
                    value={data.preferences.theme}
                    onValueChange={(value) => value && setData({
                      ...data,
                      preferences: { ...data.preferences, theme: value as any },
                    })}
                    type="single"
                    className="w-full"
                  >
                    <ToggleButton value="light" className="flex-1">
                      <LightIcon className="h-4 w-4 mr-2" />
                      Light
                    </ToggleButton>
                    <ToggleButton value="dark" className="flex-1">
                      <DarkIcon className="h-4 w-4 mr-2" />
                      Dark
                    </ToggleButton>
                    <ToggleButton value="auto" className="flex-1">
                      <SettingsIcon className="h-4 w-4 mr-2" />
                      Auto
                    </ToggleButton>
                  </ToggleButtonGroup>
                </div>
                
                <div>
                  <Label className="mb-2 block">Accent Color</Label>
                  <div className="flex gap-2">
                    {['#2196F3', '#9C27B0', '#4CAF50', '#FF9800', '#F44336', '#009688'].map((color) => (
                      <button
                        key={color}
                        onClick={() => setData({
                          ...data,
                          preferences: { ...data.preferences, accentColor: color },
                        })}
                        className={cn(
                          "w-10 h-10 rounded-full border-2 transition-all",
                          data.preferences.accentColor === color ? 'border-background ring-2 ring-offset-2' : 'border-transparent'
                        )}
                        style={{ backgroundColor: color }}
                      >
                        {data.preferences.accentColor === color && (
                          <CheckIcon className="h-5 w-5 text-white mx-auto" />
                        )}
                      </button>
                    ))}
                  </div>
                </div>
                
                <div className="space-y-3">
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={data.preferences.animations}
                        onCheckedChange={(checked) => setData({
                          ...data,
                          preferences: { ...data.preferences, animations: checked as boolean },
                        })}
                      />
                    }
                    label="Enable animations and transitions"
                  />
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={data.preferences.compactMode}
                        onCheckedChange={(checked) => setData({
                          ...data,
                          preferences: { ...data.preferences, compactMode: checked as boolean },
                        })}
                      />
                    }
                    label="Compact mode (smaller UI elements)"
                  />
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={data.preferences.notifications}
                        onCheckedChange={(checked) => setData({
                          ...data,
                          preferences: { ...data.preferences, notifications: checked as boolean },
                        })}
                      />
                    }
                    label="Enable desktop notifications"
                  />
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={data.preferences.telemetry}
                        onCheckedChange={(checked) => setData({
                          ...data,
                          preferences: { ...data.preferences, telemetry: checked as boolean },
                        })}
                      />
                    }
                    label="Share anonymous usage data to help improve Claudia"
                  />
                </div>
              </div>
            </div>
          </div>
        );

      case 3: // Features
        return (
          <div className="slide-in-left">
            <div className="py-2">
              <h3 className="text-xl font-semibold mb-2">Enable features</h3>
              <p className="text-sm text-muted-foreground mb-6">
                Choose which features to enable (you can change these later)
              </p>
              
              <List>
                <ListItem className="px-0">
                  <ListItemIcon>
                    <Checkbox
                      checked={data.features.fileManager}
                      onCheckedChange={(checked) => setData({
                        ...data,
                        features: { ...data.features, fileManager: checked as boolean },
                      })}
                    />
                  </ListItemIcon>
                  <ListItemText
                    primary="File Manager"
                    secondary="Browse, edit, and manage your project files"
                  />
                  <Chip label="Essential" size="small" variant="default" />
                </ListItem>
                
                <ListItem className="px-0">
                  <ListItemIcon>
                    <Checkbox
                      checked={data.features.gitIntegration}
                      onCheckedChange={(checked) => setData({
                        ...data,
                        features: { ...data.features, gitIntegration: checked as boolean },
                      })}
                    />
                  </ListItemIcon>
                  <ListItemText
                    primary="Git Integration"
                    secondary="Version control and Git hooks management"
                  />
                  <Chip label="Recommended" size="small" variant="success" />
                </ListItem>
                
                <ListItem className="px-0">
                  <ListItemIcon>
                    <Checkbox
                      checked={data.features.workflowAutomation}
                      onCheckedChange={(checked) => setData({
                        ...data,
                        features: { ...data.features, workflowAutomation: checked as boolean },
                      })}
                    />
                  </ListItemIcon>
                  <ListItemText
                    primary="Workflow Automation"
                    secondary="Create and manage automated workflows"
                  />
                  <Chip label="Advanced" size="small" />
                </ListItem>
                
                <ListItem className="px-0">
                  <ListItemIcon>
                    <Checkbox
                      checked={data.features.agentSystem}
                      onCheckedChange={(checked) => setData({
                        ...data,
                        features: { ...data.features, agentSystem: checked as boolean },
                      })}
                    />
                  </ListItemIcon>
                  <ListItemText
                    primary="AI Agent System"
                    secondary="Create and orchestrate AI agents"
                  />
                  <Chip label="Pro" size="small" variant="secondary" />
                </ListItem>
                
                <ListItem className="px-0">
                  <ListItemIcon>
                    <Checkbox
                      checked={data.features.mcpServers}
                      onCheckedChange={(checked) => setData({
                        ...data,
                        features: { ...data.features, mcpServers: checked as boolean },
                      })}
                    />
                  </ListItemIcon>
                  <ListItemText
                    primary="MCP Servers"
                    secondary="Connect to Model Context Protocol servers"
                  />
                  <Chip label="Advanced" size="small" />
                </ListItem>
              </List>
            </div>
          </div>
        );

      case 4: // Integrations
        return (
          <div className="slide-in-left">
            <div className="py-2">
              <h3 className="text-xl font-semibold mb-2">Connect integrations</h3>
              <p className="text-sm text-muted-foreground mb-6">
                Optional: Connect your accounts (you can add these later)
              </p>
              
              <div className="space-y-4">
                <div>
                  <Label htmlFor="github">GitHub Token</Label>
                  <div className="relative">
                    <GitHubIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="github"
                      type="password"
                      value={data.integrations.github || ''}
                      onChange={(e) => setData({
                        ...data,
                        integrations: { ...data.integrations, github: e.target.value },
                      })}
                      className={cn("pl-10", errors.github ? 'border-destructive' : '')}
                      placeholder="ghp_..."
                    />
                  </div>
                  {errors.github ? (
                    <p className="text-sm text-destructive mt-1">{errors.github}</p>
                  ) : (
                    <p className="text-xs text-muted-foreground mt-1">For repository access and GitHub Copilot</p>
                  )}
                </div>
                
                <div>
                  <Label htmlFor="openai">OpenAI API Key</Label>
                  <div className="relative">
                    <SmartToy className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="openai"
                      type="password"
                      value={data.integrations.openai || ''}
                      onChange={(e) => setData({
                        ...data,
                        integrations: { ...data.integrations, openai: e.target.value },
                      })}
                      className="pl-10"
                      placeholder="sk-..."
                    />
                  </div>
                  <p className="text-xs text-muted-foreground mt-1">For GPT models</p>
                </div>
                
                <div>
                  <Label htmlFor="anthropic">Anthropic API Key</Label>
                  <div className="relative">
                    <SmartToy className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="anthropic"
                      type="password"
                      value={data.integrations.anthropic || ''}
                      onChange={(e) => setData({
                        ...data,
                        integrations: { ...data.integrations, anthropic: e.target.value },
                      })}
                      className="pl-10"
                      placeholder="sk-ant-..."
                    />
                  </div>
                  <p className="text-xs text-muted-foreground mt-1">For Claude models</p>
                </div>
                
                <Alert>
                  <p className="text-sm">Don't have API keys? You can add them later in Settings.</p>
                </Alert>
              </div>
            </div>
          </div>
        );

      case 5: // Get Started
        return (
          <div className="zoom-in">
            <div className="py-2">
              <div className="text-center mb-6">
                <motion.div
                  initial={{ scale: 0.5, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                  transition={{ duration: 0.5, ease: 'easeOut' }}
                >
                  <Avatar className="w-20 h-20 mx-auto mb-4">
                    <AvatarFallback className="bg-green-500 text-white">
                      <CheckIcon className="h-10 w-10" />
                    </AvatarFallback>
                  </Avatar>
                </motion.div>
                
                <h3 className="text-xl font-semibold mb-2">
                  You're all set, {data.name}!
                </h3>
                <p className="text-sm text-muted-foreground">
                  Choose your workspace folder to get started
                </p>
              </div>
              
              <div className="space-y-4">
                <div>
                  <Label htmlFor="workspace">Workspace Folder</Label>
                  <div className="flex gap-2">
                    <div className="relative flex-1">
                      <FolderIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                      <Input
                        id="workspace"
                        value={data.workspace}
                        onChange={(e) => setData({ ...data, workspace: e.target.value })}
                        className={cn("pl-10", errors.workspace ? 'border-destructive' : '')}
                        placeholder="/path/to/workspace"
                      />
                    </div>
                    <Button
                      variant="outline"
                      onClick={async () => {
                        try {
                          const path = await invoke<string>('select_folder');
                          setData({ ...data, workspace: path });
                        } catch (error) {
                          console.error('Failed to select folder:', error);
                        }
                      }}
                    >
                      Browse
                    </Button>
                  </div>
                  {errors.workspace ? (
                    <p className="text-sm text-destructive mt-1">{errors.workspace}</p>
                  ) : (
                    <p className="text-xs text-muted-foreground mt-1">Where your projects will be stored</p>
                  )}
                </div>
                
                <Paper variant="outlined" className="p-4">
                  <p className="font-medium mb-3">Quick Start Resources</p>
                  <List>
                    <ListItem className="px-0 py-1">
                      <ListItemIcon>
                        <TutorialIcon className="h-5 w-5 text-primary" />
                      </ListItemIcon>
                      <ListItemText
                        primary="Interactive Tutorial"
                        secondary="Learn the basics in 5 minutes"
                      />
                    </ListItem>
                    <ListItem className="px-0 py-1">
                      <ListItemIcon>
                        <HelpIcon className="h-5 w-5 text-primary" />
                      </ListItemIcon>
                      <ListItemText
                        primary="Documentation"
                        secondary="Comprehensive guides and API reference"
                      />
                    </ListItem>
                    <ListItem className="px-0 py-1">
                      <ListItemIcon>
                        <KeyboardIcon className="h-5 w-5 text-primary" />
                      </ListItemIcon>
                      <ListItemText
                        primary="Keyboard Shortcuts"
                        secondary="Press ? anytime to see shortcuts"
                      />
                    </ListItem>
                  </List>
                </Paper>
                
                <Alert className="bg-green-50 dark:bg-green-950 border-green-200 dark:border-green-800">
                  <p className="font-medium mb-2">Your configuration summary:</p>
                  <div className="flex gap-2 flex-wrap">
                    <Chip label={`Theme: ${data.preferences.theme}`} size="small" />
                    <Chip label={`Role: ${data.role}`} size="small" />
                    <Chip label={`Experience: ${data.experience}`} size="small" />
                    {data.features.gitIntegration && <Chip label="Git" size="small" />}
                    {data.features.workflowAutomation && <Chip label="Workflows" size="small" />}
                    {data.features.agentSystem && <Chip label="Agents" size="small" />}
                  </div>
                </Alert>
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <Dialog open={open}>
      <DialogContent className="max-w-3xl min-h-[600px] p-0">
        <div className="flex justify-between items-center p-6 pb-0">
          <h2 className="text-lg font-semibold">Setup Wizard</h2>
          {onSkip && (
            <Button variant="ghost" onClick={onSkip}>
              Skip Setup
            </Button>
          )}
        </div>
        
        <div className="p-6">
          <Stepper activeStep={activeStep} className="mb-8">
            {steps.map((label) => (
              <Step key={label}>
                <StepLabel>{label}</StepLabel>
              </Step>
            ))}
          </Stepper>
          
          <div className="min-h-[400px]">
            <AnimatePresence mode="wait">
              <motion.div
                key={activeStep}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.3 }}
              >
                {renderStepContent(activeStep)}
              </motion.div>
            </AnimatePresence>
          </div>
          
          <div className="flex justify-between mt-6">
            <Button
              disabled={activeStep === 0}
              onClick={handleBack}
              variant="outline"
            >
              <BackIcon className="h-4 w-4 mr-2" />
              Back
            </Button>
            
            <div className="flex gap-2">
              {activeStep === steps.length - 1 && (
                <Button
                  variant="outline"
                  onClick={() => setActiveStep(3)}
                >
                  Review Settings
                </Button>
              )}
              
              <Button
                onClick={handleNext}
                disabled={loading}
              >
                {activeStep === steps.length - 1 ? (
                  <>
                    Get Started
                    <StartIcon className="h-4 w-4 ml-2" />
                  </>
                ) : (
                  <>
                    Next
                    <NextIcon className="h-4 w-4 ml-2" />
                  </>
                )}
              </Button>
            </div>
          </div>
          
          {loading && <LinearProgress className="mt-4" />}
        </div>
      </DialogContent>
    </Dialog>
  );
};
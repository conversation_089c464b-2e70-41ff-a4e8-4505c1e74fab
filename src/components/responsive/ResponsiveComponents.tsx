import React, { useState } from 'react';
import * as Avatar from '@radix-ui/react-avatar';
import * as Collapsible from '@radix-ui/react-collapsible';
import {
  ChevronDown,
  ChevronUp,
  MoreVertical,
  Eye,
  Edit,
  Trash2,
  ChevronLeft,
  ChevronRight,
} from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  useResponsive,
  useBreakpointValue,
  useResponsiveValue,
} from '../../hooks/useResponsive';

// Responsive Card Component
interface ResponsiveCardProps {
  title: string;
  subtitle?: string;
  content: React.ReactNode;
  media?: {
    src: string;
    alt: string;
    height?: number | { xs?: number; sm?: number; md?: number; lg?: number; xl?: number };
  };
  actions?: React.ReactNode;
  avatar?: React.ReactNode;
  menu?: React.ReactNode;
  expandable?: boolean;
  expandedContent?: React.ReactNode;
  onClick?: () => void;
  selected?: boolean;
  elevation?: number;
  variant?: 'outlined' | 'elevation';
}

export const ResponsiveCard: React.FC<ResponsiveCardProps> = ({
  title,
  subtitle,
  content,
  media,
  actions,
  avatar,
  menu,
  expandable = false,
  expandedContent,
  onClick,
  selected = false,
  elevation = 1,
  variant = 'outlined',
}) => {
  const [expanded, setExpanded] = useState(false);
  const responsive = useResponsive();

  const mediaHeight = media?.height
    ? typeof media.height === 'number'
      ? media.height
      : useResponsiveValue(media.height) || 200
    : 200;

  const handleExpandClick = () => {
    setExpanded(!expanded);
  };

  const cardLayout = useBreakpointValue(
    {
      xs: 'vertical',
      sm: 'vertical',
      md: media ? 'horizontal' : 'vertical',
      lg: media ? 'horizontal' : 'vertical',
    },
    'vertical'
  );

  const cardClasses = `
    bg-white rounded-lg transition-all duration-300 ease-in-out
    ${variant === 'outlined' ? 'border border-gray-200' : 'shadow-md'}
    ${onClick ? 'cursor-pointer hover:shadow-lg hover:-translate-y-1' : 'cursor-default'}
    ${selected ? 'ring-2 ring-blue-500 border-blue-500' : ''}
    ${cardLayout === 'horizontal' ? 'flex' : 'block'}
    ${responsive.isMobile ? 'min-h-[200px]' : responsive.isTablet ? 'min-h-[250px]' : media ? 'min-h-[300px]' : 'min-h-[200px]'}
  `;

  return (
    <div className={cardClasses} onClick={onClick}>
      {/* Media Section */}
      {media && (
        <img
          src={media.src}
          alt={media.alt}
          className={`object-cover ${
            cardLayout === 'horizontal' 
              ? `h-full ${responsive.isMobile ? 'w-2/5' : 'w-1/3'}`
              : 'w-full'
          }`}
          style={{ height: cardLayout === 'horizontal' ? '100%' : mediaHeight }}
        />
      )}

      {/* Content Section */}
      <div className="flex flex-col flex-1">
        {/* Header */}
        <div className={`flex items-start justify-between p-${responsive.isMobile ? '4' : '6'} pb-${responsive.isMobile ? '2' : '4'}`}>
          <div className="flex items-start space-x-3 flex-1">
            {avatar && (
              <div className="flex-shrink-0">
                {avatar}
              </div>
            )}
            <div className="flex-1 min-w-0">
              <h3 className={`font-semibold text-gray-900 ${responsive.isMobile ? 'text-base' : 'text-lg'}`}>
                {title}
              </h3>
              {subtitle && (
                <p className="text-sm text-gray-600 mt-1">
                  {subtitle}
                </p>
              )}
            </div>
          </div>
          <div className="flex-shrink-0">
            {menu || (
              <button className="p-1 rounded-md hover:bg-gray-100">
                <MoreVertical className="h-4 w-4 text-gray-400" />
              </button>
            )}
          </div>
        </div>

        {/* Content */}
        <div className={`flex-1 px-${responsive.isMobile ? '4' : '6'} pb-${responsive.isMobile ? '2' : '4'}`}>
          {content}
        </div>

        {/* Actions */}
        {(actions || expandable) && (
          <div className={`flex ${actions && expandable ? 'justify-between' : 'justify-end'} items-center px-${responsive.isMobile ? '4' : '6'} pb-${responsive.isMobile ? '4' : '6'}`}>
            {actions && <div>{actions}</div>}
            {expandable && (
              <button
                onClick={handleExpandClick}
                className="p-1 rounded-md hover:bg-gray-100"
                aria-expanded={expanded}
                aria-label="show more"
              >
                {expanded ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
              </button>
            )}
          </div>
        )}

        {/* Expandable Content */}
        {expandable && (
          <Collapsible.Root open={expanded}>
            <Collapsible.Content className={`px-${responsive.isMobile ? '4' : '6'} pb-${responsive.isMobile ? '4' : '6'}`}>
              {expandedContent}
            </Collapsible.Content>
          </Collapsible.Root>
        )}
      </div>
    </div>
  );
};

// Responsive Table Component
interface ResponsiveTableColumn {
  id: string;
  label: string;
  minWidth?: number;
  align?: 'left' | 'center' | 'right';
  format?: (value: any) => string;
  hideOnMobile?: boolean;
  hideOnTablet?: boolean;
  priority?: number; // Lower number = higher priority
}

interface ResponsiveTableProps {
  columns: ResponsiveTableColumn[];
  rows: any[];
  onRowClick?: (row: any) => void;
  actions?: (row: any) => React.ReactNode;
  selectable?: boolean;
  selected?: string[];
  onSelectChange?: (selected: string[]) => void;
  pagination?: boolean;
  rowsPerPage?: number;
  expandable?: boolean;
  expandedContent?: (row: any) => React.ReactNode;
  stickyHeader?: boolean;
  loading?: boolean;
  emptyMessage?: string;
  cardViewThreshold?: 'sm' | 'md' | 'lg';
}

export const ResponsiveTable: React.FC<ResponsiveTableProps> = ({
  columns,
  rows,
  onRowClick,
  actions,
  selectable = false,
  selected = [],
  onSelectChange,
  pagination = true,
  rowsPerPage: initialRowsPerPage = 10,
  expandable = false,
  expandedContent,
  stickyHeader = true,
  loading = false,
  emptyMessage = 'No data available',
  cardViewThreshold = 'md',
}) => {
  const responsive = useResponsive();
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(initialRowsPerPage);
  const [expandedRows, setExpandedRows] = useState<Set<string>>(new Set());

  // Determine if we should show card view
  const showCardView = useBreakpointValue(
    {
      xs: true,
      sm: cardViewThreshold === 'sm',
      md: cardViewThreshold === 'md' || cardViewThreshold === 'sm',
      lg: cardViewThreshold === 'lg',
      xl: false,
    },
    false
  );

  // Filter columns based on responsive settings
  const visibleColumns = columns.filter(column => {
    if (responsive.isMobile && column.hideOnMobile) return false;
    if (responsive.isTablet && column.hideOnTablet) return false;
    return true;
  });

  // Sort columns by priority for mobile view
  const prioritizedColumns = [...visibleColumns].sort((a, b) => {
    const priorityA = a.priority ?? 999;
    const priorityB = b.priority ?? 999;
    return priorityA - priorityB;
  });

  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleExpandRow = (rowId: string) => {
    const newExpanded = new Set(expandedRows);
    if (newExpanded.has(rowId)) {
      newExpanded.delete(rowId);
    } else {
      newExpanded.add(rowId);
    }
    setExpandedRows(newExpanded);
  };

  const paginatedRows = pagination
    ? rows.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
    : rows;

  // Card View for Mobile/Tablet
  if (showCardView) {
    return (
      <div className="space-y-4">
        {loading ? (
          Array.from({ length: 3 }).map((_, index) => (
            <div key={index} className="border border-gray-200 rounded-lg p-4 bg-white">
              <div className="animate-pulse space-y-3">
                <div className="h-4 bg-gray-200 rounded w-3/5"></div>
                <div className="h-3 bg-gray-200 rounded w-full"></div>
                <div className="h-3 bg-gray-200 rounded w-4/5"></div>
              </div>
            </div>
          ))
        ) : paginatedRows.length === 0 ? (
          <div className="border border-gray-200 rounded-lg p-8 text-center bg-white">
            <p className="text-gray-500">{emptyMessage}</p>
          </div>
        ) : (
          paginatedRows.map((row, index) => (
            <div
              key={row.id || index}
              className={`border border-gray-200 rounded-lg p-4 bg-white transition-all duration-200 ${
                onRowClick ? 'cursor-pointer hover:bg-gray-50' : 'cursor-default'
              }`}
              onClick={() => onRowClick?.(row)}
            >
              <div className="space-y-3">
                {prioritizedColumns.slice(0, 3).map((column) => (
                  <div key={column.id}>
                    <p className="text-xs text-gray-500 uppercase tracking-wide">
                      {column.label}
                    </p>
                    <p className="text-sm text-gray-900 mt-1">
                      {column.format ? column.format(row[column.id]) : row[column.id]}
                    </p>
                  </div>
                ))}
              </div>
              
              {(actions || expandable) && (
                <div className="flex justify-between items-center mt-4">
                  {actions && (
                    <div onClick={(e) => e.stopPropagation()}>
                      {actions(row)}
                    </div>
                  )}
                  {expandable && (
                    <button
                      className="p-1 rounded-md hover:bg-gray-100"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleExpandRow(row.id || index.toString());
                      }}
                    >
                      {expandedRows.has(row.id || index.toString()) ? 
                        <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
                    </button>
                  )}
                </div>
              )}
              
              {expandable && expandedRows.has(row.id || index.toString()) && (
                <div className="mt-4 pt-4 border-t border-gray-200">
                  {expandedContent?.(row)}
                </div>
              )}
            </div>
          ))
        )}

        {pagination && (
          <div className="flex items-center justify-between px-4 py-3 bg-white border-t border-gray-200">
            <div className="flex items-center text-sm text-gray-700">
              <span>Showing {page * rowsPerPage + 1} to {Math.min((page + 1) * rowsPerPage, rows.length)} of {rows.length} results</span>
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={() => handleChangePage(null, page - 1)}
                disabled={page === 0}
                className="p-2 rounded-md hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <ChevronLeft className="h-4 w-4" />
              </button>
              <span className="text-sm text-gray-700">
                Page {page + 1} of {Math.ceil(rows.length / rowsPerPage)}
              </span>
              <button
                onClick={() => handleChangePage(null, page + 1)}
                disabled={page >= Math.ceil(rows.length / rowsPerPage) - 1}
                className="p-2 rounded-md hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <ChevronRight className="h-4 w-4" />
              </button>
            </div>
          </div>
        )}
      </div>
    );
  }

  // Regular Table View for Desktop
  return (
    <div className="border border-gray-200 rounded-lg bg-white shadow-sm">
      <div className={`overflow-auto ${stickyHeader ? 'max-h-96' : ''}`}>
        <table className="min-w-full divide-y divide-gray-200">
          <thead className={`bg-gray-50 ${stickyHeader ? 'sticky top-0' : ''}`}>
            <tr>
              {expandable && (
                <th className="px-6 py-3 w-12"></th>
              )}
              {visibleColumns.map((column) => (
                <th
                  key={column.id}
                  className={`px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider text-${column.align || 'left'}`}
                  style={{ minWidth: column.minWidth }}
                >
                  {column.label}
                </th>
              ))}
              {actions && (
                <th className="px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider text-right">
                  Actions
                </th>
              )}
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {loading ? (
              Array.from({ length: 5 }).map((_, index) => (
                <tr key={index}>
                  {expandable && (
                    <td className="px-6 py-4">
                      <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
                    </td>
                  )}
                  {visibleColumns.map((column) => (
                    <td key={column.id} className="px-6 py-4">
                      <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
                    </td>
                  ))}
                  {actions && (
                    <td className="px-6 py-4">
                      <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
                    </td>
                  )}
                </tr>
              ))
            ) : paginatedRows.length === 0 ? (
              <tr>
                <td 
                  colSpan={visibleColumns.length + (expandable ? 1 : 0) + (actions ? 1 : 0)}
                  className="px-6 py-8 text-center"
                >
                  <p className="text-gray-500">{emptyMessage}</p>
                </td>
              </tr>
            ) : (
              paginatedRows.map((row, index) => (
                <React.Fragment key={row.id || index}>
                  <tr
                    className={`hover:bg-gray-50 ${onRowClick ? 'cursor-pointer' : 'cursor-default'}`}
                    onClick={() => onRowClick?.(row)}
                  >
                    {expandable && (
                      <td className="px-6 py-4">
                        <button
                          className="p-1 rounded-md hover:bg-gray-100"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleExpandRow(row.id || index.toString());
                          }}
                        >
                          {expandedRows.has(row.id || index.toString()) ?
                            <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
                        </button>
                      </td>
                    )}
                    {visibleColumns.map((column) => (
                      <td key={column.id} className={`px-6 py-4 text-sm text-gray-900 text-${column.align || 'left'}`}>
                        {column.format ? column.format(row[column.id]) : row[column.id]}
                      </td>
                    ))}
                    {actions && (
                      <td className="px-6 py-4 text-right" onClick={(e) => e.stopPropagation()}>
                        {actions(row)}
                      </td>
                    )}
                  </tr>
                  {expandable && expandedRows.has(row.id || index.toString()) && (
                    <tr>
                      <td 
                        colSpan={visibleColumns.length + (expandable ? 1 : 0) + (actions ? 1 : 0)}
                        className="px-6 py-4 bg-gray-50"
                      >
                        {expandedContent?.(row)}
                      </td>
                    </tr>
                  )}
                </React.Fragment>
              ))
            )}
          </tbody>
        </table>
      </div>

      {pagination && (
        <div className="flex items-center justify-between px-6 py-3 bg-gray-50 border-t border-gray-200">
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-700">Rows per page:</span>
            <select
              value={rowsPerPage}
              onChange={handleChangeRowsPerPage}
              className="border border-gray-300 rounded px-2 py-1 text-sm"
            >
              {[5, 10, 25, 50].map((size) => (
                <option key={size} value={size}>{size}</option>
              ))}
            </select>
          </div>
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-700">
              {page * rowsPerPage + 1}-{Math.min((page + 1) * rowsPerPage, rows.length)} of {rows.length}
            </span>
            <button
              onClick={() => handleChangePage(null, page - 1)}
              disabled={page === 0}
              className="p-2 rounded-md hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <ChevronLeft className="h-4 w-4" />
            </button>
            <button
              onClick={() => handleChangePage(null, page + 1)}
              disabled={page >= Math.ceil(rows.length / rowsPerPage) - 1}
              className="p-2 rounded-md hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <ChevronRight className="h-4 w-4" />
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

// Responsive Image Component
interface ResponsiveImageProps {
  src: string;
  alt: string;
  width?: number | { xs?: number; sm?: number; md?: number; lg?: number; xl?: number };
  height?: number | { xs?: number; sm?: number; md?: number; lg?: number; xl?: number };
  objectFit?: 'cover' | 'contain' | 'fill' | 'none' | 'scale-down';
  borderRadius?: number;
  onClick?: () => void;
  loading?: 'lazy' | 'eager';
}

export const ResponsiveImage: React.FC<ResponsiveImageProps> = ({
  src,
  alt,
  width,
  height,
  objectFit = 'cover',
  borderRadius = 0,
  onClick,
  loading = 'lazy',
}) => {
  const [imageLoading, setImageLoading] = useState(true);
  const [error, setError] = useState(false);

  const responsiveWidth = typeof width === 'object' ? useResponsiveValue(width) : width;
  const responsiveHeight = typeof height === 'object' ? useResponsiveValue(height) : height;

  const containerStyle = {
    width: responsiveWidth || '100%',
    height: responsiveHeight || 'auto',
  };

  return (
    <div
      className={`relative overflow-hidden ${onClick ? 'cursor-pointer' : 'cursor-default'}`}
      style={{
        ...containerStyle,
        borderRadius: borderRadius,
      }}
      onClick={onClick}
    >
      {imageLoading && !error && (
        <div className="absolute inset-0 bg-gray-200 animate-pulse" />
      )}
      
      {error ? (
        <div className="w-full h-full flex items-center justify-center bg-gray-100">
          <p className="text-sm text-gray-500">
            Failed to load image
          </p>
        </div>
      ) : (
        <img
          src={src}
          alt={alt}
          loading={loading}
          className={`w-full h-full object-${objectFit} ${imageLoading ? 'hidden' : 'block'}`}
          onLoad={() => setImageLoading(false)}
          onError={() => {
            setImageLoading(false);
            setError(true);
          }}
        />
      )}
    </div>
  );
};
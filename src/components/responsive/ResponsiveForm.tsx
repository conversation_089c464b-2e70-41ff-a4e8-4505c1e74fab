import React, { useState } from 'react';
import * as Select from '@radix-ui/react-select';
import * as Switch from '@radix-ui/react-switch';
import * as Checkbox from '@radix-ui/react-checkbox';
import * as RadioGroup from '@radix-ui/react-radio-group';
import * as Slider from '@radix-ui/react-slider';
import * as Label from '@radix-ui/react-label';
import * as Progress from '@radix-ui/react-progress';
import * as Collapsible from '@radix-ui/react-collapsible';
import * as Toggle from '@radix-ui/react-toggle';
import {
  Eye,
  EyeOff,
  Plus,
  Minus,
  Upload,
  X,
  Save,
  Send,
  ChevronDown,
  ChevronUp,
  Check,
  Loader2,
} from 'lucide-react';
import { motion } from 'framer-motion';
import {
  useResponsive,
  useBreakpointValue,
  useGridColumns,
} from '../../hooks/useResponsive';

// Form Field Configuration
export interface FormFieldConfig {
  name: string;
  label: string;
  type: 
    | 'text' 
    | 'email' 
    | 'password' 
    | 'number' 
    | 'tel' 
    | 'url'
    | 'textarea'
    | 'select'
    | 'multiselect'
    | 'checkbox'
    | 'radio'
    | 'switch'
    | 'slider'
    | 'date'
    | 'time'
    | 'datetime'
    | 'file'
    | 'autocomplete'
    | 'chips'
    | 'rating'
    | 'toggle';
  placeholder?: string;
  helperText?: string;
  required?: boolean;
  disabled?: boolean;
  options?: Array<{ value: any; label: string; disabled?: boolean }>;
  validation?: {
    min?: number;
    max?: number;
    minLength?: number;
    maxLength?: number;
    pattern?: RegExp;
    custom?: (value: any) => string | undefined;
  };
  grid?: {
    xs?: number;
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
  };
  dependsOn?: {
    field: string;
    value: any;
  };
  multiple?: boolean;
  rows?: number;
  accept?: string; // For file input
  defaultValue?: any;
}

interface FormSectionConfig {
  title?: string;
  description?: string;
  fields: FormFieldConfig[];
  collapsible?: boolean;
  defaultExpanded?: boolean;
}

interface ResponsiveFormProps {
  sections?: FormSectionConfig[];
  fields?: FormFieldConfig[];
  values: Record<string, any>;
  errors?: Record<string, string>;
  onChange: (name: string, value: any) => void;
  onSubmit: (values: Record<string, any>) => void;
  onCancel?: () => void;
  submitText?: string;
  cancelText?: string;
  loading?: boolean;
  success?: boolean;
  successMessage?: string;
  layout?: 'vertical' | 'horizontal' | 'inline';
  showValidation?: boolean;
  autoSave?: boolean;
  autoSaveDelay?: number;
}

export const ResponsiveForm: React.FC<ResponsiveFormProps> = ({
  sections,
  fields,
  values,
  errors = {},
  onChange,
  onSubmit,
  onCancel,
  submitText = 'Submit',
  cancelText = 'Cancel',
  loading = false,
  success = false,
  successMessage = 'Form submitted successfully!',
  layout = 'vertical',
  showValidation = true,
  autoSave = false,
  autoSaveDelay = 2000,
}) => {
  const responsive = useResponsive();
  const gridColumns = useGridColumns();
  const [showPassword, setShowPassword] = useState<Record<string, boolean>>({});
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({});
  const [autoSaving, setAutoSaving] = useState(false);

  // Form layout based on screen size
  const formLayout = useBreakpointValue(
    {
      xs: 'vertical',
      sm: 'vertical',
      md: layout,
      lg: layout,
      xl: layout,
    },
    'vertical'
  );

  // Handle auto-save
  React.useEffect(() => {
    if (!autoSave) return;

    const timer = setTimeout(() => {
      setAutoSaving(true);
      onSubmit(values);
      setTimeout(() => setAutoSaving(false), 1000);
    }, autoSaveDelay);

    return () => clearTimeout(timer);
  }, [values, autoSave, autoSaveDelay, onSubmit]);

  const toggleSection = (index: number) => {
    setExpandedSections(prev => ({
      ...prev,
      [index]: !prev[index],
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(values);
  };

  const renderField = (field: FormFieldConfig) => {
    // Check dependencies
    if (field.dependsOn) {
      const dependencyValue = values[field.dependsOn.field];
      if (dependencyValue !== field.dependsOn.value) {
        return null;
      }
    }

    const hasError = !!errors[field.name];
    const isDisabled = field.disabled || loading;
    const inputClasses = `w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
      hasError ? 'border-red-500 focus:ring-red-500 focus:border-red-500' : 'border-gray-300'
    } ${isDisabled ? 'bg-gray-100 cursor-not-allowed' : ''}`;

    switch (field.type) {
      case 'text':
      case 'email':
      case 'tel':
      case 'url':
        return (
          <div className="space-y-2">
            <Label.Root className="text-sm font-medium text-gray-700">
              {field.label} {field.required && <span className="text-red-500">*</span>}
            </Label.Root>
            <input
              type={field.type}
              value={values[field.name] || ''}
              placeholder={field.placeholder}
              onChange={(e) => onChange(field.name, e.target.value)}
              disabled={isDisabled}
              required={field.required}
              maxLength={field.validation?.maxLength}
              minLength={field.validation?.minLength}
              className={inputClasses}
            />
            {(errors[field.name] || field.helperText) && (
              <p className={`text-sm ${hasError ? 'text-red-600' : 'text-gray-500'}`}>
                {errors[field.name] || field.helperText}
              </p>
            )}
          </div>
        );

      case 'password':
        return (
          <div className="space-y-2">
            <Label.Root className="text-sm font-medium text-gray-700">
              {field.label} {field.required && <span className="text-red-500">*</span>}
            </Label.Root>
            <div className="relative">
              <input
                type={showPassword[field.name] ? 'text' : 'password'}
                value={values[field.name] || ''}
                placeholder={field.placeholder}
                onChange={(e) => onChange(field.name, e.target.value)}
                disabled={isDisabled}
                required={field.required}
                className={`${inputClasses} pr-10`}
              />
              <button
                type="button"
                className="absolute inset-y-0 right-0 pr-3 flex items-center"
                onClick={() => setShowPassword(prev => ({
                  ...prev,
                  [field.name]: !prev[field.name],
                }))}
              >
                {showPassword[field.name] ? (
                  <EyeOff className="h-4 w-4 text-gray-400" />
                ) : (
                  <Eye className="h-4 w-4 text-gray-400" />
                )}
              </button>
            </div>
            {(errors[field.name] || field.helperText) && (
              <p className={`text-sm ${hasError ? 'text-red-600' : 'text-gray-500'}`}>
                {errors[field.name] || field.helperText}
              </p>
            )}
          </div>
        );

      case 'number':
        return (
          <div className="space-y-2">
            <Label.Root className="text-sm font-medium text-gray-700">
              {field.label} {field.required && <span className="text-red-500">*</span>}
            </Label.Root>
            <input
              type="number"
              value={values[field.name] || ''}
              placeholder={field.placeholder}
              onChange={(e) => onChange(field.name, parseFloat(e.target.value))}
              disabled={isDisabled}
              required={field.required}
              min={field.validation?.min}
              max={field.validation?.max}
              className={inputClasses}
            />
            {(errors[field.name] || field.helperText) && (
              <p className={`text-sm ${hasError ? 'text-red-600' : 'text-gray-500'}`}>
                {errors[field.name] || field.helperText}
              </p>
            )}
          </div>
        );

      case 'textarea':
        return (
          <div className="space-y-2">
            <Label.Root className="text-sm font-medium text-gray-700">
              {field.label} {field.required && <span className="text-red-500">*</span>}
            </Label.Root>
            <textarea
              value={values[field.name] || ''}
              placeholder={field.placeholder}
              onChange={(e) => onChange(field.name, e.target.value)}
              disabled={isDisabled}
              required={field.required}
              rows={field.rows || 4}
              className={inputClasses}
            />
            {(errors[field.name] || field.helperText) && (
              <p className={`text-sm ${hasError ? 'text-red-600' : 'text-gray-500'}`}>
                {errors[field.name] || field.helperText}
              </p>
            )}
          </div>
        );

      case 'select':
        return (
          <div className="space-y-2">
            <Label.Root className="text-sm font-medium text-gray-700">
              {field.label} {field.required && <span className="text-red-500">*</span>}
            </Label.Root>
            <Select.Root
              value={values[field.name] || ''}
              onValueChange={(value) => onChange(field.name, value)}
              disabled={isDisabled}
            >
              <Select.Trigger className={inputClasses}>
                <Select.Value placeholder={field.placeholder || 'Select...'} />
                <Select.Icon>
                  <ChevronDown className="h-4 w-4" />
                </Select.Icon>
              </Select.Trigger>
              <Select.Portal>
                <Select.Content className="bg-white border border-gray-300 rounded-md shadow-lg z-50">
                  <Select.Viewport className="p-1">
                    {field.options?.map((option) => (
                      <Select.Item
                        key={option.value}
                        value={option.value}
                        disabled={option.disabled}
                        className="px-2 py-1 text-sm cursor-pointer hover:bg-gray-100 rounded focus:bg-gray-100 focus:outline-none"
                      >
                        <Select.ItemText>{option.label}</Select.ItemText>
                      </Select.Item>
                    ))}
                  </Select.Viewport>
                </Select.Content>
              </Select.Portal>
            </Select.Root>
            {(errors[field.name] || field.helperText) && (
              <p className={`text-sm ${hasError ? 'text-red-600' : 'text-gray-500'}`}>
                {errors[field.name] || field.helperText}
              </p>
            )}
          </div>
        );

      case 'multiselect':
        return (
          <div className="space-y-2">
            <Label.Root className="text-sm font-medium text-gray-700">
              {field.label} {field.required && <span className="text-red-500">*</span>}
            </Label.Root>
            <div className="space-y-2">
              {field.options?.map((option) => (
                <div key={option.value} className="flex items-center space-x-2">
                  <Checkbox.Root
                    checked={(values[field.name] || []).includes(option.value)}
                    onCheckedChange={(checked) => {
                      const current = values[field.name] || [];
                      if (checked) {
                        onChange(field.name, [...current, option.value]);
                      } else {
                        onChange(field.name, current.filter((v: any) => v !== option.value));
                      }
                    }}
                    disabled={isDisabled || option.disabled}
                    className="w-4 h-4 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 data-[state=checked]:bg-blue-600 data-[state=checked]:border-blue-600"
                  >
                    <Checkbox.Indicator className="text-white">
                      <Check className="w-3 h-3" />
                    </Checkbox.Indicator>
                  </Checkbox.Root>
                  <Label.Root className="text-sm text-gray-700">
                    {option.label}
                  </Label.Root>
                </div>
              ))}
            </div>
            {(errors[field.name] || field.helperText) && (
              <p className={`text-sm ${hasError ? 'text-red-600' : 'text-gray-500'}`}>
                {errors[field.name] || field.helperText}
              </p>
            )}
          </div>
        );

      case 'checkbox':
        return (
          <div className="space-y-2">
            <Label.Root className="text-sm font-medium text-gray-700">
              {field.label} {field.required && <span className="text-red-500">*</span>}
            </Label.Root>
            <div className="space-y-2">
              {field.options?.map((option) => (
                <div key={option.value} className="flex items-center space-x-2">
                  <Checkbox.Root
                    checked={(values[field.name] || []).includes(option.value)}
                    onCheckedChange={(checked) => {
                      const current = values[field.name] || [];
                      if (checked) {
                        onChange(field.name, [...current, option.value]);
                      } else {
                        onChange(field.name, current.filter((v: any) => v !== option.value));
                      }
                    }}
                    disabled={isDisabled || option.disabled}
                    className="w-4 h-4 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 data-[state=checked]:bg-blue-600 data-[state=checked]:border-blue-600"
                  >
                    <Checkbox.Indicator className="text-white">
                      <Check className="w-3 h-3" />
                    </Checkbox.Indicator>
                  </Checkbox.Root>
                  <Label.Root className="text-sm text-gray-700">
                    {option.label}
                  </Label.Root>
                </div>
              ))}
            </div>
            {(errors[field.name] || field.helperText) && (
              <p className={`text-sm ${hasError ? 'text-red-600' : 'text-gray-500'}`}>
                {errors[field.name] || field.helperText}
              </p>
            )}
          </div>
        );

      case 'radio':
        return (
          <div className="space-y-2">
            <Label.Root className="text-sm font-medium text-gray-700">
              {field.label} {field.required && <span className="text-red-500">*</span>}
            </Label.Root>
            <RadioGroup.Root
              value={values[field.name] || ''}
              onValueChange={(value) => onChange(field.name, value)}
              disabled={isDisabled}
              className={`space-y-2 ${formLayout === 'horizontal' ? 'flex flex-row space-y-0 space-x-4' : ''}`}
            >
              {field.options?.map((option) => (
                <div key={option.value} className="flex items-center space-x-2">
                  <RadioGroup.Item
                    value={option.value}
                    disabled={option.disabled}
                    className="w-4 h-4 border border-gray-300 rounded-full focus:ring-2 focus:ring-blue-500 data-[state=checked]:border-blue-600"
                  >
                    <RadioGroup.Indicator className="w-2 h-2 bg-blue-600 rounded-full" />
                  </RadioGroup.Item>
                  <Label.Root className="text-sm text-gray-700">
                    {option.label}
                  </Label.Root>
                </div>
              ))}
            </RadioGroup.Root>
            {(errors[field.name] || field.helperText) && (
              <p className={`text-sm ${hasError ? 'text-red-600' : 'text-gray-500'}`}>
                {errors[field.name] || field.helperText}
              </p>
            )}
          </div>
        );

      case 'switch':
        return (
          <div className="flex items-center space-x-2">
            <Switch.Root
              checked={values[field.name] || false}
              onCheckedChange={(checked) => onChange(field.name, checked)}
              disabled={isDisabled}
              className="w-11 h-6 bg-gray-200 rounded-full relative focus:outline-none focus:ring-2 focus:ring-blue-500 data-[state=checked]:bg-blue-600"
            >
              <Switch.Thumb className="block w-5 h-5 bg-white rounded-full shadow transform transition-transform data-[state=checked]:translate-x-5" />
            </Switch.Root>
            <Label.Root className="text-sm text-gray-700">
              {field.label} {field.required && <span className="text-red-500">*</span>}
            </Label.Root>
            {(errors[field.name] || field.helperText) && (
              <p className={`text-sm ${hasError ? 'text-red-600' : 'text-gray-500'}`}>
                {errors[field.name] || field.helperText}
              </p>
            )}
          </div>
        );

      case 'slider':
        return (
          <div className="space-y-2">
            <Label.Root className="text-sm font-medium text-gray-700">
              {field.label} {field.required && <span className="text-red-500">*</span>}
            </Label.Root>
            <div className="px-3">
              <Slider.Root
                value={[values[field.name] || field.validation?.min || 0]}
                onValueChange={(value) => onChange(field.name, value[0])}
                min={field.validation?.min}
                max={field.validation?.max}
                disabled={isDisabled}
                className="relative flex items-center w-full h-5"
              >
                <Slider.Track className="bg-gray-200 relative grow rounded-full h-1">
                  <Slider.Range className="absolute bg-blue-600 rounded-full h-full" />
                </Slider.Track>
                <Slider.Thumb className="block w-4 h-4 bg-white shadow-lg border border-gray-300 rounded-full hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500" />
              </Slider.Root>
              <div className="flex justify-between text-xs text-gray-500 mt-1">
                <span>{field.validation?.min || 0}</span>
                <span className="font-medium">{values[field.name] || field.validation?.min || 0}</span>
                <span>{field.validation?.max || 100}</span>
              </div>
            </div>
            {(errors[field.name] || field.helperText) && (
              <p className={`text-sm ${hasError ? 'text-red-600' : 'text-gray-500'}`}>
                {errors[field.name] || field.helperText}
              </p>
            )}
          </div>
        );

      case 'toggle':
        return (
          <div className="space-y-2">
            <Label.Root className="text-sm font-medium text-gray-700">
              {field.label} {field.required && <span className="text-red-500">*</span>}
            </Label.Root>
            <div className="flex space-x-2">
              {field.options?.map((option) => (
                <Toggle.Root
                  key={option.value}
                  pressed={values[field.name] === option.value}
                  onPressedChange={(pressed) => {
                    if (pressed) {
                      onChange(field.name, option.value);
                    } else {
                      onChange(field.name, null);
                    }
                  }}
                  disabled={isDisabled || option.disabled}
                  className="px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50 data-[state=on]:bg-blue-600 data-[state=on]:text-white data-[state=on]:border-blue-600"
                >
                  {option.label}
                </Toggle.Root>
              ))}
            </div>
            {(errors[field.name] || field.helperText) && (
              <p className={`text-sm ${hasError ? 'text-red-600' : 'text-gray-500'}`}>
                {errors[field.name] || field.helperText}
              </p>
            )}
          </div>
        );

      case 'file':
        return (
          <div className="space-y-2">
            <Label.Root className="text-sm font-medium text-gray-700">
              {field.label} {field.required && <span className="text-red-500">*</span>}
            </Label.Root>
            <label className="w-full">
              <div className="border-2 border-dashed border-gray-300 rounded-md p-6 text-center hover:border-gray-400 cursor-pointer">
                <Upload className="mx-auto h-8 w-8 text-gray-400 mb-2" />
                <span className="text-sm text-gray-600">
                  Click to upload {field.multiple ? 'files' : 'a file'}
                </span>
                <input
                  type="file"
                  className="hidden"
                  accept={field.accept}
                  multiple={field.multiple}
                  disabled={isDisabled}
                  onChange={(e) => {
                    const files = e.target.files;
                    if (files) {
                      onChange(field.name, field.multiple ? Array.from(files) : files[0]);
                    }
                  }}
                />
              </div>
            </label>
            {values[field.name] && (
              <p className="text-sm text-gray-600">
                Selected: {
                  field.multiple 
                    ? `${values[field.name].length} files`
                    : values[field.name].name
                }
              </p>
            )}
            {(errors[field.name] || field.helperText) && (
              <p className={`text-sm ${hasError ? 'text-red-600' : 'text-gray-500'}`}>
                {errors[field.name] || field.helperText}
              </p>
            )}
          </div>
        );

      default:
        return null;
    }
  };

  const renderSection = (section: FormSectionConfig, index: number) => {
    const isExpanded = expandedSections[index] !== false || !section.collapsible;

    return (
      <div
        key={index}
        className={`border border-gray-200 rounded-lg ${responsive.isMobile ? 'p-4' : 'p-6'} mb-4 bg-white shadow-sm`}
      >
        {section.title && (
          <>
            <Collapsible.Root open={isExpanded} onOpenChange={() => section.collapsible && toggleSection(index)}>
              <div className="flex items-center justify-between mb-4">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">{section.title}</h3>
                  {section.description && (
                    <p className="text-sm text-gray-600 mt-1">
                      {section.description}
                    </p>
                  )}
                </div>
                {section.collapsible && (
                  <Collapsible.Trigger asChild>
                    <button className="p-2 rounded-md hover:bg-gray-100">
                      {isExpanded ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
                    </button>
                  </Collapsible.Trigger>
                )}
              </div>
              <div className="border-t border-gray-200 mb-4" />
              
              <Collapsible.Content>
                <div className={`grid gap-${responsive.isMobile ? '4' : '6'} ${getGridColumns(section.fields)}`}>
                  {section.fields.map((field) => (
                    <div key={field.name}>
                      {renderField(field)}
                    </div>
                  ))}
                </div>
              </Collapsible.Content>
            </Collapsible.Root>
          </>
        )}
      </div>
    );
  };

  // Helper function to determine grid columns based on field configuration
  const getGridColumns = (fields: FormFieldConfig[]) => {
    const hasWideFields = fields.some(field => field.type === 'textarea' || field.type === 'file');
    if (responsive.isMobile) return 'grid-cols-1';
    if (hasWideFields) return 'grid-cols-1 md:grid-cols-2';
    return 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3';
  };

  const allFields = sections 
    ? sections.flatMap(s => s.fields)
    : fields || [];

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {success && (
        <div className="bg-green-50 border border-green-200 rounded-md p-4 mb-4">
          <div className="flex">
            <Check className="h-5 w-5 text-green-400" />
            <div className="ml-3">
              <p className="text-sm text-green-800">{successMessage}</p>
            </div>
          </div>
        </div>
      )}

      {sections ? (
        sections.map((section, index) => renderSection(section, index))
      ) : (
        <div className={`grid gap-${responsive.isMobile ? '4' : '6'} ${getGridColumns(allFields)}`}>
          {allFields.map((field) => (
            <div key={field.name}>
              {renderField(field)}
            </div>
          ))}
        </div>
      )}

      <div className={`flex ${responsive.isMobile ? 'flex-col space-y-3' : 'flex-row space-x-3'} justify-end mt-6`}>
        {autoSaving && (
          <div className="flex items-center space-x-2 text-gray-600">
            <Loader2 className="h-4 w-4 animate-spin" />
            <span className="text-sm">Auto-saving...</span>
          </div>
        )}
        
        {onCancel && (
          <button
            type="button"
            onClick={onCancel}
            disabled={loading}
            className={`px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50 disabled:cursor-not-allowed ${
              responsive.isMobile ? 'w-full' : ''
            }`}
          >
            {cancelText}
          </button>
        )}
        
        <button
          type="submit"
          disabled={loading}
          className={`px-4 py-2 bg-blue-600 border border-transparent rounded-md text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed ${
            responsive.isMobile ? 'w-full' : ''
          }`}
        >
          <div className="flex items-center justify-center space-x-2">
            {loading ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Send className="h-4 w-4" />
            )}
            <span>{loading ? 'Submitting...' : submitText}</span>
          </div>
        </button>
      </div>
    </form>
  );
};
import React, { useState, useEffect } from 'react';
import {
  Menu,
  X,
  ChevronLeft,
  ChevronRight,
  Home,
  MessageCircle,
  Folder,
  GitBranch,
  Bo<PERSON>,
  ArrowUp,
} from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate, useLocation } from 'react-router-dom';
import { 
  useResponsive, 
  useSidebarMode, 
  useDrawerWidth,
  useContainerWidth,
  useResponsivePadding,
  breakpoints,
} from '../../hooks/useResponsive';

interface ResponsiveLayoutProps {
  children: React.ReactNode;
  sidebar?: React.ReactNode;
  header?: React.ReactNode;
  footer?: React.ReactNode;
  showSidebar?: boolean;
  showHeader?: boolean;
  showFooter?: boolean;
  showBottomNav?: boolean;
  showScrollTop?: boolean;
  maxWidth?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | false;
  disablePadding?: boolean;
  fullHeight?: boolean;
}

export const ResponsiveLayout: React.FC<ResponsiveLayoutProps> = ({
  children,
  sidebar,
  header,
  footer,
  showSidebar = true,
  showHeader = true,
  showFooter = false,
  showBottomNav = true,
  showScrollTop = true,
  maxWidth = 'lg',
  disablePadding = false,
  fullHeight = true,
}) => {
  const navigate = useNavigate();
  const location = useLocation();
  const responsive = useResponsive();
  const sidebarMode = useSidebarMode();
  const drawerWidth = useDrawerWidth();
  const containerWidth = useContainerWidth();
  const padding = useResponsivePadding();
  
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [showScrollButton, setShowScrollButton] = useState(false);
  const [bottomNavValue, setBottomNavValue] = useState(0);

  // Handle scroll to top button visibility
  useEffect(() => {
    const handleScroll = () => {
      setShowScrollButton(window.pageYOffset > 300);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Update bottom navigation based on route
  useEffect(() => {
    const routes = ['/', '/chat', '/files', '/workflows', '/agents'];
    const index = routes.findIndex(route => location.pathname.startsWith(route));
    setBottomNavValue(index !== -1 ? index : 0);
  }, [location]);

  const handleSidebarToggle = () => {
    setSidebarOpen(!sidebarOpen);
  };

  const handleScrollTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const handleBottomNavChange = (event: React.SyntheticEvent, newValue: number) => {
    setBottomNavValue(newValue);
    const routes = ['/', '/chat', '/files', '/workflows', '/agents'];
    navigate(routes[newValue]);
  };

  // Determine layout margins based on sidebar mode
  const getMainMargin = () => {
    if (!showSidebar) return 0;
    if (sidebarMode === 'permanent' && sidebarOpen) return drawerWidth;
    return 0;
  };

  const getMainShift = () => {
    if (!showSidebar) return 0;
    if (sidebarMode === 'persistent' && sidebarOpen) return drawerWidth;
    return 0;
  };

  // Responsive app bar height
  const appBarHeight = responsive.isMobile ? 56 : 64;
  const bottomNavHeight = 56;

  return (
    <div className={`flex flex-col ${fullHeight ? 'min-h-screen' : ''} bg-gray-50`}>
      {/* Header/AppBar */}
      {showHeader && (
        <header 
          className={`fixed top-0 left-0 right-0 z-50 bg-white shadow-sm border-b border-gray-200 transition-all duration-200 ${
            sidebarMode === 'permanent' && sidebarOpen
              ? `ml-${Math.round(drawerWidth / 4)}`
              : ''
          }`}
          style={{
            marginLeft: sidebarMode === 'permanent' && sidebarOpen ? drawerWidth : 0,
            width: sidebarMode === 'permanent' && sidebarOpen ? `calc(100% - ${drawerWidth}px)` : '100%',
          }}
        >
          <div className={`flex items-center justify-between px-4 ${responsive.isMobile ? 'h-14' : 'h-16'}`}>
            <div className="flex items-center space-x-4">
              {showSidebar && sidebarMode !== 'permanent' && (
                <button
                  onClick={handleSidebarToggle}
                  className="p-2 rounded-md hover:bg-gray-100"
                >
                  {sidebarOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
                </button>
              )}
              {header || (
                <h1 className="text-xl font-semibold text-gray-900">
                  Claudia
                </h1>
              )}
            </div>
          </div>
        </header>
      )}

      {/* Sidebar Drawer */}
      {showSidebar && sidebar && (
        <>
          {/* Permanent drawer for desktop */}
          {sidebarMode === 'permanent' && (
            <div
              className={`fixed left-0 top-0 h-full bg-white border-r border-gray-200 z-40 transition-all duration-300 ease-out overflow-hidden ${
                sidebarOpen ? '' : 'w-14'
              }`}
              style={{
                width: sidebarOpen ? drawerWidth : 56,
                transform: 'translateX(0)',
              }}
            >
              <div style={{ height: appBarHeight }} />
              <div className={`overflow-auto h-full ${sidebarOpen ? 'p-2' : 'p-1'}`}>
                {sidebar}
              </div>
              <div className="absolute bottom-0 left-0 right-0 p-2 bg-white border-t border-gray-100">
                <button
                  onClick={handleSidebarToggle}
                  className="p-2 rounded-md hover:bg-gray-100 w-full flex justify-center"
                >
                  {sidebarOpen ? <ChevronLeft className="h-5 w-5" /> : <ChevronRight className="h-5 w-5" />}
                </button>
              </div>
            </div>
          )}

          {/* Persistent drawer for tablet */}
          {sidebarMode === 'persistent' && (
            <div
              className={`fixed left-0 top-0 h-full bg-white border-r border-gray-200 z-40 transition-transform duration-300 ease-out ${
                sidebarOpen ? 'translate-x-0' : '-translate-x-full'
              }`}
              style={{ width: drawerWidth }}
            >
              <div style={{ height: appBarHeight }} />
              <div className="overflow-auto h-full p-2">
                {sidebar}
              </div>
            </div>
          )}

          {/* Temporary drawer for mobile */}
          {sidebarMode === 'temporary' && (
            <>
              {/* Backdrop */}
              {sidebarOpen && (
                <div
                  className="fixed inset-0 bg-black bg-opacity-50 z-30"
                  onClick={handleSidebarToggle}
                />
              )}
              {/* Drawer */}
              <div
                className={`fixed left-0 top-0 h-full bg-white border-r border-gray-200 z-40 transition-transform duration-300 ease-out ${
                  sidebarOpen ? 'translate-x-0' : '-translate-x-full'
                }`}
                style={{ width: drawerWidth }}
              >
                <div 
                  className="flex items-center px-4 border-b border-gray-200"
                  style={{ height: appBarHeight }}
                >
                  <button
                    onClick={handleSidebarToggle}
                    className="p-2 rounded-md hover:bg-gray-100 mr-2"
                  >
                    <X className="h-5 w-5" />
                  </button>
                  <h2 className="text-lg font-semibold">Menu</h2>
                </div>
                <div className="overflow-auto h-full p-2">
                  {sidebar}
                </div>
              </div>
            </>
          )}
        </>
      )}

      {/* Main Content Area */}
      <main
        className="flex-1 flex flex-col transition-all duration-300 ease-out"
        style={{
          marginLeft: getMainMargin(),
          marginTop: showHeader ? appBarHeight : 0,
          marginBottom: responsive.isMobile && showBottomNav ? bottomNavHeight : 0,
          transform: getMainShift() ? `translateX(${getMainShift()}px)` : 'none',
        }}
      >
        <div
          className={`flex-1 mx-auto w-full ${
            !disablePadding ? (responsive.isMobile ? 'px-4 py-4' : 'px-8 py-8') : ''
          } ${
            maxWidth === 'xs' ? 'max-w-xs' :
            maxWidth === 'sm' ? 'max-w-sm' :
            maxWidth === 'md' ? 'max-w-4xl' :
            maxWidth === 'lg' ? 'max-w-6xl' :
            maxWidth === 'xl' ? 'max-w-7xl' :
            maxWidth === false ? 'max-w-none' : 'max-w-6xl'
          }`}
          style={{
            ...(typeof containerWidth === 'number' && {
              maxWidth: containerWidth,
            }),
          }}
        >
          <AnimatePresence mode="wait">
            <motion.div
              key={location.pathname}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.2 }}
            >
              {children}
            </motion.div>
          </AnimatePresence>
        </div>

        {/* Footer */}
        {showFooter && footer && (
          <footer className="mt-auto border-t border-gray-200 bg-white">
            <div className={`mx-auto py-4 ${
              maxWidth === 'xs' ? 'max-w-xs' :
              maxWidth === 'sm' ? 'max-w-sm' :
              maxWidth === 'md' ? 'max-w-4xl' :
              maxWidth === 'lg' ? 'max-w-6xl' :
              maxWidth === 'xl' ? 'max-w-7xl' :
              maxWidth === false ? 'max-w-none' : 'max-w-6xl'
            }`}>
              {footer}
            </div>
          </footer>
        )}
      </main>

      {/* Mobile Bottom Navigation */}
      {responsive.isMobile && showBottomNav && (
        <div className="fixed bottom-0 left-0 right-0 z-50 bg-white border-t border-gray-200 shadow-lg">
          <div className="flex">
            {[
              { label: 'Home', icon: Home, index: 0 },
              { label: 'Chat', icon: MessageCircle, index: 1 },
              { label: 'Files', icon: Folder, index: 2 },
              { label: 'Workflows', icon: GitBranch, index: 3 },
              { label: 'Agents', icon: Bot, index: 4 },
            ].map(({ label, icon: Icon, index }) => (
              <button
                key={label}
                onClick={(e) => handleBottomNavChange(e, index)}
                className={`flex-1 flex flex-col items-center justify-center py-2 px-1 transition-colors ${
                  bottomNavValue === index
                    ? 'text-blue-600 bg-blue-50'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                }`}
              >
                <Icon className="h-5 w-5 mb-1" />
                <span className="text-xs font-medium">{label}</span>
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Scroll to Top Button */}
      {showScrollTop && showScrollButton && (
        <motion.button
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          exit={{ scale: 0 }}
          onClick={handleScrollTop}
          className={`fixed z-50 bg-blue-600 hover:bg-blue-700 text-white rounded-full shadow-lg transition-colors ${
            responsive.isMobile ? 'w-12 h-12' : 'w-14 h-14'
          }`}
          style={{
            bottom: responsive.isMobile && showBottomNav ? 72 : 16,
            right: 16,
          }}
        >
          <ArrowUp className={`${responsive.isMobile ? 'h-5 w-5' : 'h-6 w-6'} mx-auto`} />
        </motion.button>
      )}
    </div>
  );
};

// Responsive Grid Component
interface ResponsiveGridProps {
  children: React.ReactNode;
  columns?: {
    xs?: number;
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
  };
  spacing?: {
    xs?: number;
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
  };
}

export const ResponsiveGrid: React.FC<ResponsiveGridProps> = ({
  children,
  columns = { xs: 1, sm: 2, md: 3, lg: 4, xl: 5 },
  spacing = { xs: 1, sm: 2, md: 2, lg: 3, xl: 3 },
}) => {
  const responsive = useResponsive();
  const currentColumns = columns[responsive.breakpoint] || 1;
  const currentSpacing = spacing[responsive.breakpoint] || 2;

  return (
    <div
      className="grid w-full"
      style={{
        gridTemplateColumns: `repeat(${currentColumns}, 1fr)`,
        gap: `${currentSpacing * 0.5}rem`,
      }}
    >
      {children}
    </div>
  );
};

// Responsive Stack Component
interface ResponsiveStackProps {
  children: React.ReactNode;
  direction?: {
    xs?: 'row' | 'column';
    sm?: 'row' | 'column';
    md?: 'row' | 'column';
    lg?: 'row' | 'column';
    xl?: 'row' | 'column';
  };
  spacing?: {
    xs?: number;
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
  };
  alignItems?: string;
  justifyContent?: string;
}

export const ResponsiveStack: React.FC<ResponsiveStackProps> = ({
  children,
  direction = { xs: 'column', sm: 'column', md: 'row', lg: 'row', xl: 'row' },
  spacing = { xs: 1, sm: 2, md: 2, lg: 3, xl: 3 },
  alignItems = 'stretch',
  justifyContent = 'flex-start',
}) => {
  const responsive = useResponsive();
  const currentDirection = direction[responsive.breakpoint] || 'row';
  const currentSpacing = spacing[responsive.breakpoint] || 2;

  const alignItemsClass = 
    alignItems === 'stretch' ? 'items-stretch' :
    alignItems === 'center' ? 'items-center' :
    alignItems === 'flex-start' ? 'items-start' :
    alignItems === 'flex-end' ? 'items-end' :
    'items-stretch';

  const justifyContentClass = 
    justifyContent === 'flex-start' ? 'justify-start' :
    justifyContent === 'center' ? 'justify-center' :
    justifyContent === 'flex-end' ? 'justify-end' :
    justifyContent === 'space-between' ? 'justify-between' :
    justifyContent === 'space-around' ? 'justify-around' :
    justifyContent === 'space-evenly' ? 'justify-evenly' :
    'justify-start';

  return (
    <div
      className={`flex w-full ${
        currentDirection === 'column' ? 'flex-col' : 'flex-row'
      } ${alignItemsClass} ${justifyContentClass}`}
      style={{
        gap: `${currentSpacing * 0.5}rem`,
      }}
    >
      {children}
    </div>
  );
};

// Responsive Hidden Component
interface ResponsiveHiddenProps {
  children: React.ReactNode;
  hideOn?: ('xs' | 'sm' | 'md' | 'lg' | 'xl')[];
  showOn?: ('xs' | 'sm' | 'md' | 'lg' | 'xl')[];
}

export const ResponsiveHidden: React.FC<ResponsiveHiddenProps> = ({
  children,
  hideOn = [],
  showOn = [],
}) => {
  const responsive = useResponsive();
  
  const shouldHide = hideOn.includes(responsive.breakpoint);
  const shouldShow = showOn.length === 0 || showOn.includes(responsive.breakpoint);
  
  if (shouldHide || !shouldShow) {
    return null;
  }
  
  return <>{children}</>;
};
import React, { useState, useEffect, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Bot, 
  Plus, 
  Code2, 
  Zap, 
  Brain,
  Sparkles,
  Terminal,
  FileCode,
  GitBranch,
  Package,
  Layers,
  Activity,
  ArrowRight,
  Users,
  Shield,
  Cpu,
  Search,
  Filter,
  Star,
  TrendingUp,
  Clock,
  Play,
  Settings,
  MoreHorizontal
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useTabState } from '@/hooks/useTabState';

interface Agent {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  category: 'core' | 'development' | 'utility';
  status: 'available' | 'running' | 'disabled';
  capabilities: string[];
  usageCount?: number;
}

export const AgentsOverview: React.FC = () => {
  const { createCreateAgentTab } = useTabState();
  const [agents, setAgents] = useState<Agent[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState<'name' | 'usage' | 'recent'>('name');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadAgents();
  }, []);

  const loadAgents = async () => {
    try {
      setLoading(true);
      // For now, we'll use empty array since the real agents come from CCAgents component
      // This component appears to be a showcase/landing page for agents
      // The actual agent management happens in CCAgents
      setAgents([]);
    } catch (error) {
      console.error("Failed to load agents:", error);
    } finally {
      setLoading(false);
    }
  };

  const categories = [
    { id: 'all', name: 'All Agents', count: agents.length },
    { id: 'core', name: 'Core', count: agents.filter(a => a.category === 'core').length },
    { id: 'development', name: 'Development', count: agents.filter(a => a.category === 'development').length },
    { id: 'utility', name: 'Utility', count: agents.filter(a => a.category === 'utility').length }
  ];

  const filteredAndSortedAgents = useMemo(() => {
    let filtered = selectedCategory === 'all' 
      ? agents 
      : agents.filter(a => a.category === selectedCategory);
    
    // Apply search filter
    if (searchQuery) {
      filtered = filtered.filter(agent => 
        agent.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        agent.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        agent.capabilities.some(cap => cap.toLowerCase().includes(searchQuery.toLowerCase()))
      );
    }
    
    // Apply sorting
    switch (sortBy) {
      case 'usage':
        return filtered.sort((a, b) => (b.usageCount || 0) - (a.usageCount || 0));
      case 'recent':
        return filtered.sort((a, b) => a.name.localeCompare(b.name)); // In real app, would sort by created/updated date
      case 'name':
      default:
        return filtered.sort((a, b) => a.name.localeCompare(b.name));
    }
  }, [agents, selectedCategory, searchQuery, sortBy]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'available': return 'text-green-500';
      case 'running': return 'text-blue-500';
      case 'disabled': return 'text-gray-400';
      default: return 'text-gray-500';
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'available': return <Badge variant="outline" className="border-green-500/50 text-green-600">Available</Badge>;
      case 'running': return <Badge variant="outline" className="border-blue-500/50 text-blue-600">Running</Badge>;
      case 'disabled': return <Badge variant="outline" className="border-gray-500/50 text-gray-600">Disabled</Badge>;
      default: return null;
    }
  };

  return (
    <div className="h-full overflow-y-auto bg-background">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-8 max-w-7xl">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4 }}
          className="mb-8"
        >
          <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-6 mb-6">
            <div>
              <h1 className="text-3xl font-bold bg-gradient-to-r from-purple-500 to-pink-600 bg-clip-text text-transparent">
                CC Agents
              </h1>
              <p className="text-muted-foreground mt-2">
                Specialized AI agents to enhance your development workflow
              </p>
            </div>
            <div className="flex items-center gap-3">
              <Button 
                variant="outline"
                size="sm"
                onClick={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}
              >
                {viewMode === 'grid' ? <Layers className="w-4 h-4" /> : <Activity className="w-4 h-4" />}
              </Button>
              <Button 
                onClick={() => createCreateAgentTab()}
                className="bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700"
              >
                <Plus className="w-4 h-4 mr-2" />
                Create Agent
              </Button>
            </div>
          </div>
          
          {/* Search and Filters */}
          <div className="flex flex-col sm:flex-row gap-4 mb-6">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder="Search agents, capabilities, or descriptions..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
            <div className="flex gap-2">
              <Select value={sortBy} onValueChange={(value: 'name' | 'usage' | 'recent') => setSortBy(value)}>
                <SelectTrigger className="w-[140px]">
                  <SelectValue placeholder="Sort by" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="name">Name</SelectItem>
                  <SelectItem value="usage">Most Used</SelectItem>
                  <SelectItem value="recent">Recent</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Stats */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.4, delay: 0.1 }}
            className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8"
          >
            <Card className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950 dark:to-blue-900 border-blue-200 dark:border-blue-800 hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-blue-600 dark:text-blue-400">Total Agents</p>
                    <p className="text-2xl font-bold text-blue-900 dark:text-blue-100">{agents.length}</p>
                    <p className="text-xs text-blue-500 dark:text-blue-400 mt-1">+2 this week</p>
                  </div>
                  <div className="p-3 bg-blue-100 dark:bg-blue-800 rounded-full">
                    <Bot className="h-6 w-6 text-blue-600 dark:text-blue-300" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950 dark:to-green-900 border-green-200 dark:border-green-800 hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-green-600 dark:text-green-400">Active</p>
                    <p className="text-2xl font-bold text-green-900 dark:text-green-100">
                      {agents.filter(a => a.status === 'running').length}
                    </p>
                    <p className="text-xs text-green-500 dark:text-green-400 mt-1">Running now</p>
                  </div>
                  <div className="p-3 bg-green-100 dark:bg-green-800 rounded-full">
                    <Activity className="h-6 w-6 text-green-600 dark:text-green-300" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-950 dark:to-purple-900 border-purple-200 dark:border-purple-800 hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-purple-600 dark:text-purple-400">Most Used</p>
                    <p className="text-2xl font-bold text-purple-900 dark:text-purple-100">
                      {Math.max(...agents.map(a => a.usageCount || 0))}
                    </p>
                    <p className="text-xs text-purple-500 dark:text-purple-400 mt-1">executions</p>
                  </div>
                  <div className="p-3 bg-purple-100 dark:bg-purple-800 rounded-full">
                    <TrendingUp className="h-6 w-6 text-purple-600 dark:text-purple-300" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-950 dark:to-orange-900 border-orange-200 dark:border-orange-800 hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-orange-600 dark:text-orange-400">Available</p>
                    <p className="text-2xl font-bold text-orange-900 dark:text-orange-100">
                      {agents.filter(a => a.status === 'available').length}
                    </p>
                    <p className="text-xs text-orange-500 dark:text-orange-400 mt-1">ready to use</p>
                  </div>
                  <div className="p-3 bg-orange-100 dark:bg-orange-800 rounded-full">
                    <Shield className="h-6 w-6 text-orange-600 dark:text-orange-300" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </motion.div>

        {/* Category Tabs */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4, delay: 0.1 }}
          className="mb-6"
        >
          <Tabs value={selectedCategory} onValueChange={setSelectedCategory} className="w-full">
            <TabsList className="grid w-full grid-cols-4 lg:w-auto lg:grid-cols-none lg:flex">
              {categories.map((category) => (
                <TabsTrigger 
                  key={category.id} 
                  value={category.id}
                  className="flex items-center gap-2 data-[state=active]:bg-gradient-to-r data-[state=active]:from-purple-500 data-[state=active]:to-pink-600"
                >
                  {category.name}
                  <Badge variant="secondary" className="text-xs">
                    {category.count}
                  </Badge>
                </TabsTrigger>
              ))}
            </TabsList>
          </Tabs>
        </motion.div>

        {/* Agents Grid/List */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.4, delay: 0.2 }}
        >
          {filteredAndSortedAgents.length === 0 ? (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="text-center py-12"
            >
              <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-purple-100 to-pink-100 dark:from-purple-900/50 dark:to-pink-900/50 rounded-full flex items-center justify-center">
                <Bot className="w-8 h-8 text-purple-600 dark:text-purple-400" />
              </div>
              <h3 className="text-lg font-semibold mb-2">Welcome to CC Agents</h3>
              <p className="text-muted-foreground mb-4 max-w-md mx-auto">
                Create your first agent to get started with intelligent automation and enhanced development workflows
              </p>
              <Button 
                onClick={() => createCreateAgentTab()}
                className="bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700"
              >
                <Plus className="w-4 h-4 mr-2" />
                Create Your First Agent
              </Button>
            </motion.div>
          ) : (
            <div className={viewMode === 'grid' 
              ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" 
              : "space-y-4"
            }>
              {filteredAndSortedAgents.map((agent: Agent, index: number) => (
                <motion.div
                  key={agent.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.05 }}
                  whileHover={{ y: -2 }}
                  className="group"
                >
                  <Card className={`h-full hover:shadow-xl transition-all duration-300 cursor-pointer border hover:border-purple-200 dark:hover:border-purple-700 ${
                    viewMode === 'list' ? 'flex flex-row items-center p-4' : ''
                  }`}>
                    {viewMode === 'grid' ? (
                      <div className="p-6">
                        <div className="flex items-start justify-between mb-4">
                          <div className="flex items-center gap-3">
                            <div className="p-3 bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/50 dark:to-pink-900/50 rounded-xl group-hover:scale-110 transition-transform">
                              {agent.icon}
                            </div>
                            <div>
                              <h3 className="font-semibold text-lg group-hover:text-purple-600 dark:group-hover:text-purple-400 transition-colors">
                                {agent.name}
                              </h3>
                              {getStatusBadge(agent.status)}
                            </div>
                          </div>
                          <Button variant="ghost" size="sm" className="opacity-0 group-hover:opacity-100 transition-opacity">
                            <MoreHorizontal className="w-4 h-4" />
                          </Button>
                        </div>
                        
                        <p className="text-muted-foreground text-sm mb-4 line-clamp-2">
                          {agent.description}
                        </p>
                        
                        <div className="mb-4">
                          <p className="text-xs font-medium text-muted-foreground mb-2">Capabilities</p>
                          <div className="flex flex-wrap gap-1">
                            {agent.capabilities.slice(0, 3).map((cap: string) => (
                              <Badge key={cap} variant="secondary" className="text-xs">
                                {cap}
                              </Badge>
                            ))}
                            {agent.capabilities.length > 3 && (
                              <Badge variant="secondary" className="text-xs">
                                +{agent.capabilities.length - 3}
                              </Badge>
                            )}
                          </div>
                        </div>
                        
                        <div className="flex items-center justify-between pt-4 border-t">
                          <div className="flex items-center gap-4 text-xs text-muted-foreground">
                            <span className="flex items-center gap-1">
                              <Play className="w-3 h-3" />
                              {agent.usageCount || 0}
                            </span>
                            <span className="flex items-center gap-1">
                              <Star className="w-3 h-3 fill-yellow-400 text-yellow-400" />
                              4.8
                            </span>
                            <span className="flex items-center gap-1">
                              <Clock className="w-3 h-3" />
                              2m avg
                            </span>
                          </div>
                          <div className="flex gap-2">
                            <Button size="sm" variant="outline" className="opacity-0 group-hover:opacity-100 transition-opacity">
                              <Settings className="w-4 h-4" />
                            </Button>
                            <Button 
                              size="sm" 
                              className="bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700 shadow-lg hover:shadow-xl transition-all"
                            >
                              <Play className="w-4 h-4 mr-1" />
                              Run
                            </Button>
                          </div>
                        </div>
                      </div>
                    ) : (
                      <div className="flex items-center justify-between w-full">
                        <div className="flex items-center gap-4">
                          <div className="p-2 bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/50 dark:to-pink-900/50 rounded-lg">
                            {agent.icon}
                          </div>
                          <div>
                            <h3 className="font-semibold group-hover:text-purple-600 dark:group-hover:text-purple-400 transition-colors">
                              {agent.name}
                            </h3>
                            <p className="text-sm text-muted-foreground line-clamp-1">
                              {agent.description}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center gap-4">
                          {getStatusBadge(agent.status)}
                          <div className="flex items-center gap-4 text-xs text-muted-foreground">
                            <span>{agent.usageCount || 0} runs</span>
                            <Star className="w-3 h-3 fill-yellow-400 text-yellow-400" />
                          </div>
                          <div className="flex gap-2">
                            <Button size="sm" variant="outline">
                              <Settings className="w-4 h-4" />
                            </Button>
                            <Button 
                              size="sm" 
                              className="bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700"
                            >
                              <Play className="w-4 h-4 mr-1" />
                              Run
                            </Button>
                          </div>
                        </div>
                      </div>
                    )}
                  </Card>
                </motion.div>
              ))}
            </div>
          )}
        </motion.div>

        {/* Featured Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4, delay: 0.3 }}
          className="mt-12"
        >
          <h2 className="text-2xl font-semibold mb-6">Build Your Own Agent</h2>
          <Card className="p-8 bg-gradient-to-br from-purple-500/5 to-pink-500/5 border-purple-500/20">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
              <div>
                <h3 className="text-xl font-semibold mb-3">Create Custom Agents</h3>
                <p className="text-muted-foreground mb-6">
                  Design and deploy specialized agents tailored to your specific workflow needs. 
                  Use our intuitive builder to create powerful automation tools.
                </p>
                <div className="space-y-2 mb-6">
                  <div className="flex items-center gap-2">
                    <Brain className="w-4 h-4 text-purple-500" />
                    <span className="text-sm">AI-powered intelligence</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Terminal className="w-4 h-4 text-purple-500" />
                    <span className="text-sm">Full system access</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <FileCode className="w-4 h-4 text-purple-500" />
                    <span className="text-sm">Custom code execution</span>
                  </div>
                </div>
                <Button 
                  onClick={() => createCreateAgentTab()}
                  className="bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700"
                >
                  Start Building
                  <ArrowRight className="w-4 h-4 ml-2" />
                </Button>
              </div>
              <div className="flex justify-center">
                <div className="relative">
                  <div className="absolute inset-0 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl blur-2xl opacity-20"></div>
                  <div className="relative grid grid-cols-2 gap-4">
                    <div className="p-4 bg-background/80 backdrop-blur rounded-lg border border-purple-500/20">
                      <Users className="w-8 h-8 text-purple-500 mb-2" />
                      <p className="text-xs font-medium">Multi-Agent</p>
                    </div>
                    <div className="p-4 bg-background/80 backdrop-blur rounded-lg border border-purple-500/20">
                      <Cpu className="w-8 h-8 text-purple-500 mb-2" />
                      <p className="text-xs font-medium">Parallel Exec</p>
                    </div>
                    <div className="p-4 bg-background/80 backdrop-blur rounded-lg border border-purple-500/20">
                      <Shield className="w-8 h-8 text-purple-500 mb-2" />
                      <p className="text-xs font-medium">Sandboxed</p>
                    </div>
                    <div className="p-4 bg-background/80 backdrop-blur rounded-lg border border-purple-500/20">
                      <Zap className="w-8 h-8 text-purple-500 mb-2" />
                      <p className="text-xs font-medium">Fast Deploy</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </Card>
        </motion.div>
      </div>
    </div>
  );
};

export default AgentsOverview;
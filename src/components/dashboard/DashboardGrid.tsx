import React from 'react';
import { motion } from 'framer-motion';
import { MCPStatusWidget } from './MCPStatusWidget';
import { TokenUsageWidget } from './TokenUsageWidget';
import { SystemHealthWidget } from './SystemHealthWidget';

interface DashboardGridProps {
  className?: string;
}

export const DashboardGrid: React.FC<DashboardGridProps> = ({ className }) => {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.05
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 10 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.3,
        ease: "easeOut"
      }
    }
  };

  return (
    <motion.div
      className={`grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-4 gap-4 ${className || ''}`}
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      <motion.div variants={itemVariants} className="min-h-[120px]">
        <MCPStatusWidget />
      </motion.div>
      
      <motion.div variants={itemVariants} className="min-h-[120px]">
        <TokenUsageWidget />
      </motion.div>
      
      <motion.div variants={itemVariants} className="min-h-[120px]">
        <SystemHealthWidget />
      </motion.div>
    </motion.div>
  );
};

export default DashboardGrid;
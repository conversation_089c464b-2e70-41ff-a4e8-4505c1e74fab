import { useMemo } from 'react';
import { BudgetStats, HealthMetrics } from '../../types/budget.types';

export const useBudgetCalculations = (
  totalBudget: number,
  spent: number,
  committed: number = 0
) => {
  const calculations = useMemo(() => {
    const remaining = totalBudget - spent - committed;
    const utilization = totalBudget > 0 ? (spent / totalBudget) * 100 : 0;
    const committedPercentage = totalBudget > 0 ? (committed / totalBudget) * 100 : 0;
    
    // Calculate burn rate (assuming monthly)
    const currentMonth = new Date().getMonth() + 1;
    const expectedUtilization = (currentMonth / 12) * 100;
    const burnRate = expectedUtilization > 0 ? utilization / expectedUtilization : 0;
    
    // Calculate health score
    const healthScore = calculateHealthScore(utilization, burnRate, remaining);
    
    // Determine risk level
    const riskLevel = determineRiskLevel(utilization, burnRate, remaining, totalBudget);
    
    return {
      remaining,
      utilization,
      committedPercentage,
      burnRate,
      healthScore,
      riskLevel,
      isOverBudget: remaining < 0,
      availablePercentage: totalBudget > 0 ? (remaining / totalBudget) * 100 : 0,
    };
  }, [totalBudget, spent, committed]);

  return calculations;
};

export const useBudgetMetrics = (stats: Partial<BudgetStats>): HealthMetrics => {
  return useMemo(() => {
    const totalBudget = stats.total || 0;
    const totalSpent = stats.spent || 0;
    const utilization = totalBudget > 0 ? (totalSpent / totalBudget) * 100 : 0;
    const burnRate = calculateBurnRate(totalSpent, totalBudget);
    const efficiencyScore = calculateEfficiency(totalSpent, stats.committed || 0);
    const riskScore = calculateRiskScore(utilization, burnRate);
    const riskLevel = determineRiskLevel(
      utilization,
      burnRate,
      stats.remaining || 0,
      totalBudget
    );
    const projectedSpend = burnRate > 0 ? totalSpent * burnRate : totalSpent;
    const savingsOpportunity = Math.max(0, totalBudget - projectedSpend);
    const complianceRate = 95; // Placeholder
    
    return {
      // Basic metrics
      totalBudget,
      totalSpent,
      utilization,
      burnRate,
      projectedSpend,
      riskScore,
      savingsOpportunity,
      complianceRate,
      efficiencyScore,
      categoryDistribution: {}, // Will be populated elsewhere
      monthlyTrend: [], // Will be populated elsewhere
      score: calculateHealthScore(utilization, burnRate, stats.remaining || 0),
      status: riskLevel === 'high' ? 'critical' : riskLevel === 'medium' ? 'warning' : 'good',
      
      // Detailed indicators
      indicators: {
        spending: {
          value: utilization,
          status: utilization > 90 ? 'critical' : utilization > 70 ? 'warning' : 'healthy',
          trend: burnRate > 1 ? 'increasing' : 'stable'
        },
        efficiency: {
          value: efficiencyScore,
          status: efficiencyScore > 70 ? 'healthy' : efficiencyScore > 50 ? 'warning' : 'critical',
          trend: 'stable'
        },
        risk: {
          value: riskScore,
          status: riskScore > 60 ? 'critical' : riskScore > 30 ? 'warning' : 'healthy',
          trend: riskScore > 50 ? 'increasing' : 'stable'
        },
        compliance: {
          value: complianceRate,
          status: complianceRate > 90 ? 'healthy' : complianceRate > 80 ? 'warning' : 'critical',
          trend: 'stable'
        }
      },
      
      // Recommendations
      recommendations: generateRecommendations(utilization, burnRate, riskLevel).map(message => ({
        type: 'info' as const,
        message,
        action: 'review-budget'
      })),
    };
  }, [stats]);
};

// Helper functions
function calculateHealthScore(
  utilization: number,
  burnRate: number,
  remaining: number
): number {
  let score = 100;
  
  // Deduct points for over-utilization
  if (utilization > 100) {
    score -= 50;
  } else if (utilization > 90) {
    score -= 30;
  } else if (utilization > 80) {
    score -= 15;
  }
  
  // Deduct points for high burn rate
  if (burnRate > 1.2) {
    score -= 20;
  } else if (burnRate > 1.1) {
    score -= 10;
  }
  
  // Deduct points if budget is negative
  if (remaining < 0) {
    score -= 30;
  }
  
  return Math.max(0, Math.min(100, score));
}

function calculateBurnRate(spent: number, total: number): number {
  const currentMonth = new Date().getMonth() + 1;
  const expectedSpending = (total / 12) * currentMonth;
  return expectedSpending > 0 ? spent / expectedSpending : 0;
}

function calculateEfficiency(spent: number, committed: number): number {
  const total = spent + committed;
  return total > 0 ? (spent / total) * 100 : 100;
}

function calculateRiskScore(utilization: number, burnRate: number): number {
  let risk = 0;
  
  if (utilization > 100) risk += 50;
  else if (utilization > 90) risk += 30;
  else if (utilization > 80) risk += 15;
  
  if (burnRate > 1.3) risk += 30;
  else if (burnRate > 1.15) risk += 20;
  else if (burnRate > 1) risk += 10;
  
  return Math.min(100, risk);
}

function determineRiskLevel(
  utilization: number,
  burnRate: number,
  remaining: number,
  total: number
): 'low' | 'medium' | 'high' {
  if (remaining < 0 || utilization > 95 || burnRate > 1.3) {
    return 'high';
  }
  if (utilization > 80 || burnRate > 1.1) {
    return 'medium';
  }
  return 'low';
}

function calculateProjectedOverrun(
  spent: number,
  total: number,
  burnRate: number
): number {
  const projectedTotal = spent * (12 / (new Date().getMonth() + 1));
  return Math.max(0, projectedTotal - total);
}

function generateRecommendations(
  utilization: number,
  burnRate: number,
  riskLevel: 'low' | 'medium' | 'high'
): string[] {
  const recommendations: string[] = [];
  
  if (riskLevel === 'high') {
    recommendations.push('Immediate action required to control spending');
    recommendations.push('Review and postpone non-critical expenses');
  }
  
  if (burnRate > 1.2) {
    recommendations.push('Spending rate exceeds planned budget allocation');
    recommendations.push('Consider reallocating funds from underutilized categories');
  }
  
  if (utilization > 90) {
    recommendations.push('Budget nearly exhausted - monitor closely');
    recommendations.push('Prepare budget increase request if needed');
  }
  
  if (utilization < 50 && new Date().getMonth() > 6) {
    recommendations.push('Budget underutilized - review spending plans');
    recommendations.push('Consider strategic investments or reallocations');
  }
  
  return recommendations.slice(0, 3); // Return top 3 recommendations
}
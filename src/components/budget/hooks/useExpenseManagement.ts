import { useState, useCallback } from 'react';
import { ExpenseFormData, Expense, ExpenseTemplate } from '../types/expense.types';
import { useToast } from './useToast';

interface UseExpenseManagementProps {
  expenses: Expense[];
  addExpense: (data: ExpenseFormData) => Promise<void>;
  updateExpense: (id: string, data: ExpenseFormData) => Promise<void>;
  deleteExpense: (id: string) => Promise<void>;
  updateExpenseStatus: (id: string, status: string) => Promise<void>;
}

export const useExpenseManagement = ({
  expenses,
  addExpense,
  updateExpense,
  deleteExpense,
  updateExpenseStatus,
}: UseExpenseManagementProps) => {
  const { showToast } = useToast();
  const [showExpenseForm, setShowExpenseForm] = useState(false);
  const [editingExpense, setEditingExpense] = useState<Expense | null>(null);
  const [expenseTemplates, setExpenseTemplates] = useState<ExpenseTemplate[]>([]);

  const handleExpenseSubmit = useCallback(async (data: ExpenseFormData) => {
    try {
      if (editingExpense) {
        await updateExpense(editingExpense.id, data);
        showToast('Expense updated successfully!', 'success');
      } else {
        await addExpense(data);
        showToast('Expense added successfully!', 'success');
      }
      setShowExpenseForm(false);
      setEditingExpense(null);
    } catch (error) {
      showToast('Failed to save expense', 'error');
      throw error;
    }
  }, [editingExpense, updateExpense, addExpense, showToast]);

  const handleSaveTemplate = useCallback(async (
    template: Omit<ExpenseTemplate, 'id' | 'created_at' | 'updated_at'>
  ) => {
    try {
      const newTemplate: ExpenseTemplate = {
        ...template,
        id: `template-${Date.now()}`,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };
      
      setExpenseTemplates(prev => [...prev, newTemplate]);
      showToast('Template saved successfully!', 'success');
      return newTemplate;
    } catch (error) {
      showToast('Failed to save template', 'error');
      throw error;
    }
  }, [showToast]);

  const handleExpenseEdit = useCallback((expense: Expense) => {
    setEditingExpense(expense);
    setShowExpenseForm(true);
  }, []);

  const handleExpenseDelete = useCallback(async (id: string) => {
    try {
      await deleteExpense(id);
      showToast('Expense deleted successfully!', 'success');
    } catch (error) {
      showToast('Failed to delete expense', 'error');
      throw error;
    }
  }, [deleteExpense, showToast]);

  const handleStatusChange = useCallback(async (id: string, status: string) => {
    try {
      await updateExpenseStatus(id, status);
      showToast('Status updated successfully!', 'success');
    } catch (error) {
      showToast('Failed to update status', 'error');
      throw error;
    }
  }, [updateExpenseStatus, showToast]);

  const handleViewReceipt = useCallback((url: string) => {
    window.open(url, '_blank');
  }, []);

  const openExpenseForm = useCallback((expense?: Expense) => {
    if (expense) {
      setEditingExpense(expense);
    }
    setShowExpenseForm(true);
  }, []);

  const closeExpenseForm = useCallback(() => {
    setShowExpenseForm(false);
    setEditingExpense(null);
  }, []);

  return {
    showExpenseForm,
    editingExpense,
    expenseTemplates,
    handleExpenseSubmit,
    handleSaveTemplate,
    handleExpenseEdit,
    handleExpenseDelete,
    handleStatusChange,
    handleViewReceipt,
    openExpenseForm,
    closeExpenseForm,
    setExpenseTemplates,
  };
};
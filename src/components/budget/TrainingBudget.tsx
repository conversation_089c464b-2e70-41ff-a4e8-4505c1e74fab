import React, { lazy, Suspense, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Wallet, Receipt } from 'lucide-react';
import { Toast, ToastContainer } from '@/components/ui/toast';
import { format } from 'date-fns';

// Components
import { YearSelector } from './components/YearSelector';
import { BudgetInputSection } from './components/BudgetInputSection';
import { TabManager } from './components/TabManager';
import { MobileResponsiveWrapper, MobileStatsGrid } from './components/mobile';
import { QuickActionsMenu } from './components/quickactions';
import { QuickExpenseDialog } from './components/dialogs';

// Pages - Lazy loaded for performance
const OverviewTabPage = lazy(() => import('./pages/OverviewTabPage'));
const BudgetTabPage = lazy(() => import('./pages/BudgetTabPage'));
const ExpensesTabPage = lazy(() => import('./pages/ExpensesTabPage'));
const AnalyticsTabPage = lazy(() => import('./pages/AnalyticsTabPage'));
const SettingsTabPage = lazy(() => import('./pages/SettingsTabPage'));

// Hooks
import { useBudgetData } from './hooks/useBudgetData';
import { useBudgetActions } from './hooks/useBudgetActions';
import { useBudgetTabs } from './hooks/useBudgetTabs';
import { useToast } from './hooks/useToast';
import { useBudgetHealth } from './hooks/useBudgetHealth';
import { useHeatmapData } from './hooks/useHeatmapData';
import { useCalendarEvents } from './hooks/useCalendarEvents';
import { useExpenseManagement } from './hooks/useExpenseManagement';
import { useBudgetStoreExtended } from '@/stores/budgetStoreExtended';

// Utils
import { formatCurrency } from './utils/formatters';

// Types
import { QuickAction } from './components/quickactions';
import { MobileStat } from './components/mobile';
import { ROIMetric } from './types/analytics.types';
import { Expense } from './types/expense.types';

export const TrainingBudget: React.FC = () => {
  // Core data and actions
  const { year, stats, budget, loading } = useBudgetData();
  const { updateBudget, changeYear, isUpdating } = useBudgetActions();
  const { toast, showToast, hideToast } = useToast();
  
  // Tab management
  const { activeTab, setActiveTab } = useBudgetTabs('overview');
  
  // Store data
  const storeData = useBudgetStoreExtended();
  const {
    quarterlyAllocations = [],
    departmentAllocations = [],
    categoryLimits = [],
    expenses = [],
    allocationRules = [],
    analytics = null,
    analyticsLoading = false,
    rulesLoading = false,
    expensesLoading = false,
    // Store actions
    updateQuarterlyAllocations,
    updateDepartmentAllocation,
    addDepartment,
    removeDepartment,
    setCategoryLimit,
    removeCategoryLimit,
    addExpense,
    updateExpense,
    deleteExpense,
    updateExpenseStatus,
    addAllocationRule,
    updateAllocationRule,
    deleteAllocationRule,
    toggleAllocationRule,
    executeAllocationRule,
    fetchAnalytics,
    fetchExpenses,
    fetchBudget,
    fetchStats,
    fetchQuarterlyAllocations,
    fetchDepartmentAllocations,
    fetchCategoryLimits,
    fetchAllocationRules,
  } = storeData;
  
  // Calculated data with memoization
  const healthMetrics = useBudgetHealth(stats, budget, year);
  const heatmapData = useHeatmapData(expenses);
  const calendarEvents = useCalendarEvents(expenses, stats, year);
  
  // Expense management
  const {
    showExpenseForm,
    editingExpense,
    expenseTemplates,
    handleExpenseSubmit,
    handleSaveTemplate,
    handleExpenseEdit,
    handleExpenseDelete,
    handleStatusChange,
    handleViewReceipt,
    openExpenseForm,
    closeExpenseForm,
  } = useExpenseManagement({
    expenses,
    addExpense,
    updateExpense,
    deleteExpense,
    updateExpenseStatus,
  });

  // ROI Metrics (properly typed)
  const { roiMetrics = [], addROIMetric, updateROIMetric } = storeData;

  // Fetch initial data on component mount and when year changes
  useEffect(() => {
    fetchBudget(year);
    fetchStats(year);
    fetchExpenses(year);
    fetchQuarterlyAllocations(year);
    fetchDepartmentAllocations(year);
    fetchCategoryLimits(year);
    fetchAllocationRules();
    fetchAnalytics(year);
  }, [year, fetchBudget, fetchStats, fetchExpenses, fetchQuarterlyAllocations, fetchDepartmentAllocations, fetchCategoryLimits, fetchAllocationRules, fetchAnalytics]);

  const handleBudgetUpdate = async (amount: number) => {
    const result = await updateBudget(amount);
    
    if (result.success) {
      showToast('Budget updated successfully!', 'success');
    } else {
      showToast(result.error || 'Failed to update budget', 'error');
    }
    
    return result;
  };

  const handleRefresh = () => {
    fetchAnalytics(year);
    fetchExpenses(year);
  };

  // Quick Actions configuration
  const quickActions: QuickAction[] = [
    {
      id: 'add-expense',
      label: 'Add Expense',
      icon: <Receipt className="h-4 w-4" />,
      description: 'Record a new training expense',
      shortcut: 'Ctrl+E',
      category: 'expense',
      color: 'text-blue-500',
      action: () => {
        openExpenseForm();
        setActiveTab('expenses');
      }
    },
    // ... more actions can be added here
  ];

  // Mobile stats
  const mobileStats: MobileStat[] = [
    {
      id: 'total',
      label: 'Total Budget',
      value: `$${((stats?.total ?? 0) / 1000).toFixed(0)}k`,
      icon: <Wallet className="h-4 w-4" />,
      color: 'text-primary'
    },
    // ... more stats
  ];

  // Tab configurations
  const tabConfigs = [
    {
      value: 'overview',
      label: 'Overview',
      component: OverviewTabPage,
      props: {
        budget,
        stats,
        expenses,
        healthMetrics,
        heatmapData,
        loading,
        year,
        onViewDetails: (section: string) => setActiveTab(section),
        onCardClick: (cardType: string) => setActiveTab(cardType),
        onRefresh: handleRefresh,
        onTabChange: setActiveTab,
      }
    },
    {
      value: 'budget',
      label: 'Budget',
      component: BudgetTabPage,
      props: {
        budget,
        stats,
        quarterlyAllocations,
        departmentAllocations,
        categoryLimits,
        allocationRules,
        loading,
        rulesLoading,
        onUpdateQuarterlyAllocations: updateQuarterlyAllocations,
        onUpdateDepartmentAllocation: updateDepartmentAllocation,
        onAddDepartment: addDepartment,
        onRemoveDepartment: removeDepartment,
        onSetCategoryLimit: setCategoryLimit,
        onRemoveCategoryLimit: removeCategoryLimit,
        onAddAllocationRule: addAllocationRule,
        onUpdateAllocationRule: updateAllocationRule,
        onDeleteAllocationRule: deleteAllocationRule,
        onToggleAllocationRule: toggleAllocationRule,
        onExecuteAllocationRule: executeAllocationRule,
        onTabChange: setActiveTab,
      }
    },
    {
      value: 'expenses',
      label: 'Expenses',
      component: ExpensesTabPage,
      props: {
        expenses,
        departmentAllocations,
        templates: [],
        expenseTemplates,
        loading: expensesLoading,
        onExpenseSubmit: handleExpenseSubmit,
        onExpenseEdit: handleExpenseEdit,
        onExpenseDelete: handleExpenseDelete,
        onStatusChange: handleStatusChange,
        onViewReceipt: handleViewReceipt,
        onSaveTemplate: handleSaveTemplate,
        addExpense,
        updateExpense,
      }
    },
    {
      value: 'analytics',
      label: 'Analytics',
      component: AnalyticsTabPage,
      props: {
        analytics,
        budget,
        stats,
        roiMetrics,
        year,
        loading: analyticsLoading,
        onAddROIMetric: addROIMetric,
        onUpdateROIMetric: updateROIMetric,
      }
    },
    {
      value: 'settings',
      label: 'Settings',
      component: SettingsTabPage,
      props: {
        onSave: () => {
          showToast('Settings saved successfully!', 'success');
          handleRefresh();
        }
      }
    }
  ];

  return (
    <div className="h-full overflow-y-auto">
      <ToastContainer>
        {toast.isVisible && (
          <Toast
            message={toast.message}
            type={toast.type}
            onDismiss={hideToast}
            duration={3000}
          />
        )}
      </ToastContainer>
      
      <MobileResponsiveWrapper
        currentTab={activeTab}
        onTabChange={setActiveTab}
        budgetStats={{
          total: stats?.total ?? 0,
          spent: stats?.spent ?? 0,
          remaining: stats?.remaining ?? 0
        }}
      >
        <div className="container mx-auto p-6 md:p-6 p-4">
          {/* Header */}
          <div className="mb-8">
            <motion.div 
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6"
            >
              {/* Title Section */}
              <div>
                <h1 className="text-3xl font-bold tracking-tight flex items-center gap-3">
                  <Wallet className="h-8 w-8 text-primary" aria-hidden="true" />
                  Training Budget
                </h1>
                <p className="mt-2 text-sm text-muted-foreground">
                  Manage your training and development budget for {year}
                </p>
              </div>

              {/* Year Selector */}
              <div className="flex items-center justify-start md:justify-end">
                <YearSelector 
                  year={year}
                  onYearChange={changeYear}
                />
              </div>
            </motion.div>

            {/* Budget Stats Cards */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="grid grid-cols-1 sm:grid-cols-3 gap-4"
            >
              {/* Total Budget Card */}
              <Card className="p-6 bg-gradient-to-br from-primary/5 to-transparent border-primary/20">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <p className="text-sm font-medium text-muted-foreground">Total Budget</p>
                    <Badge variant="default" className="text-xs">
                      {healthMetrics?.status?.toUpperCase() || 'UNKNOWN'}
                    </Badge>
                  </div>
                  <div className="text-2xl font-bold">
                    {loading ? (
                      <Skeleton className="h-8 w-24" />
                    ) : (
                      formatCurrency(stats?.total ?? 0)
                    )}
                  </div>
                </div>
              </Card>

              {/* Spent Card */}
              <Card className="p-6 bg-gradient-to-br from-orange-500/5 to-transparent border-orange-500/20">
                <div className="space-y-2">
                  <p className="text-sm font-medium text-muted-foreground">Spent</p>
                  <div className="text-2xl font-bold text-orange-600">
                    {loading ? (
                      <Skeleton className="h-8 w-24" />
                    ) : (
                      formatCurrency(stats?.spent ?? 0)
                    )}
                  </div>
                </div>
              </Card>

              {/* Remaining Card */}
              <Card className="p-6 bg-gradient-to-br from-green-500/5 to-transparent border-green-500/20">
                <div className="space-y-2">
                  <p className="text-sm font-medium text-muted-foreground">Remaining</p>
                  <div className="text-2xl font-bold text-green-600">
                    {loading ? (
                      <Skeleton className="h-8 w-24" />
                    ) : (
                      formatCurrency(stats?.remaining ?? 0)
                    )}
                  </div>
                </div>
              </Card>
            </motion.div>
          </div>

          {/* Mobile Stats Grid */}
          <div className="block md:hidden mb-6">
            <MobileStatsGrid
              stats={mobileStats}
              columns={3}
              variant="compact"
              loading={loading}
            />
          </div>

          {/* Budget Input */}
          <BudgetInputSection
            currentBudget={budget?.total_amount || stats?.total || 0}
            onUpdate={handleBudgetUpdate}
            loading={loading}
            isUpdating={isUpdating}
          />

          {/* Tabbed Interface */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="mt-6"
          >
            <TabManager
              tabs={tabConfigs}
              activeTab={activeTab}
              onTabChange={setActiveTab}
              showTabList={true}
              gridCols={5}
            />
          </motion.div>
        </div>
        
        {/* Quick Actions Menu */}
        <div className="hidden md:block">
          <QuickActionsMenu
            actions={quickActions}
            position="bottom-right"
            onActionExecute={(actionId) => {
              showToast(`Action "${actionId}" executed`, 'success');
            }}
          />
        </div>
      </MobileResponsiveWrapper>
      
      {/* Quick Expense Dialog */}
      <QuickExpenseDialog
        open={showExpenseForm}
        onOpenChange={(open) => {
          if (!open) closeExpenseForm();
          else openExpenseForm();
        }}
        onSubmit={handleExpenseSubmit}
        departments={departmentAllocations}
        initialData={editingExpense || undefined}
        templates={expenseTemplates}
        onSaveAsTemplate={handleSaveTemplate}
      />
    </div>
  );
};

export default TrainingBudget;
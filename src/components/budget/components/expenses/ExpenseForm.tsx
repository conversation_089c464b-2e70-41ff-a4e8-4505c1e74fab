import React, { useState, useCallback, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Calendar } from '@/components/ui/calendar';
import { Root as Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Switch } from '@/components/ui/switch';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { format } from 'date-fns';
import { 
  Calendar as CalendarIcon, 
  Upload, 
  DollarSign,
  Tag,
  FileText,
  Building,
  RefreshCw,
  Save,
  Zap,
  AlertCircle,
  CheckCircle
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { ExpenseFormData, ExpenseTemplate, defaultExpenseCategories } from '../../types/expense.types';
import { ExpenseStatus, ExpenseRecurrence } from '@/stores/budgetStore';
import { DepartmentAllocation } from '@/stores/budgetStore';
import { validateExpense } from '../../utils/validations';
import { toast } from 'sonner';

interface ExpenseFormProps {
  onSubmit: (data: ExpenseFormData) => Promise<void>;
  onCancel: () => void;
  departments: DepartmentAllocation[];
  initialData?: Partial<ExpenseFormData>;
  isEditing?: boolean;
  quickMode?: boolean;
  templates?: ExpenseTemplate[];
  onSaveAsTemplate?: (template: Omit<ExpenseTemplate, 'id' | 'created_at' | 'updated_at'>) => void;
}

const statusOptions: { value: ExpenseStatus; label: string; color: string }[] = [
  { value: 'draft', label: 'Draft', color: 'text-muted-foreground' },
  { value: 'submitted', label: 'Submitted', color: 'text-blue-600' },
  { value: 'paid', label: 'Paid', color: 'text-green-600' },
  { value: 'reimbursed', label: 'Reimbursed', color: 'text-purple-600' },
];

const recurrenceOptions: { value: ExpenseRecurrence; label: string }[] = [
  { value: 'none', label: 'One-time' },
  { value: 'monthly', label: 'Monthly' },
  { value: 'quarterly', label: 'Quarterly' },
  { value: 'yearly', label: 'Annual' },
];

export const ExpenseForm: React.FC<ExpenseFormProps> = ({
  onSubmit,
  onCancel,
  departments,
  initialData,
  isEditing = false,
  quickMode = false,
  templates = [],
  onSaveAsTemplate,
}) => {
  const [formData, setFormData] = useState<ExpenseFormData>({
    title: initialData?.title || '',
    description: initialData?.description || '',
    amount: initialData?.amount || 0,
    category: initialData?.category || '',
    department_id: initialData?.department_id || 'none',
    status: initialData?.status || 'draft',
    date: initialData?.date || format(new Date(), 'yyyy-MM-dd'),
    receipt_url: initialData?.receipt_url || '',
    recurrence: initialData?.recurrence || 'none',
    recurrence_end_date: initialData?.recurrence_end_date || '',
  });
  
  const [suggestedCategory, setSuggestedCategory] = useState<string | null>(null);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showRecurring, setShowRecurring] = useState(formData.recurrence !== 'none');

  // Category suggestion logic based on keywords
  const suggestCategory = useCallback((title: string, description: string): string | null => {
    const text = (title + ' ' + description).toLowerCase();
    
    const categoryKeywords: Record<string, string[]> = {
      'Online Courses': ['course', 'tutorial', 'udemy', 'coursera', 'edx', 'subscription', 'online learning', 'e-learning'],
      'Conferences': ['conference', 'summit', 'symposium', 'expo', 'seminar', 'workshop', 'event'],
      'Certifications': ['certification', 'cert', 'exam', 'license', 'accreditation', 'credential'],
      'Books & Resources': ['book', 'ebook', 'manual', 'guide', 'documentation', 'resource', 'textbook'],
      'Workshops': ['workshop', 'training', 'bootcamp', 'class', 'session', 'tutorial'],
      'Coaching': ['coaching', 'mentoring', 'consulting', 'advice', 'guidance'],
      'Software & Tools': ['software', 'tool', 'application', 'app', 'license', 'subscription', 'program'],
      'Other': []
    };
    
    let bestMatch: { category: string; score: number } | null = null;
    
    Object.entries(categoryKeywords).forEach(([category, keywords]) => {
      let score = 0;
      keywords.forEach(keyword => {
        if (text.includes(keyword)) {
          score += 1;
        }
      });
      
      if (score > 0) {
        if (!bestMatch || score > (bestMatch as { category: string; score: number }).score) {
          bestMatch = { category, score };
        }
      }
    });
    
    return bestMatch ? bestMatch.category : null;
  }, []);
  
  // Suggest category when title or description changes
  useEffect(() => {
    if (!formData.category) {
      const suggestion = suggestCategory(formData.title, formData.description || '');
      setSuggestedCategory(suggestion);
    } else {
      setSuggestedCategory(null);
    }
  }, [formData.title, formData.description, formData.category, suggestCategory]);

  // Real-time validation
  const validateField = useCallback((field: keyof ExpenseFormData, value: any) => {
    let error = '';
    
    switch (field) {
      case 'title':
        if (!value || value.trim().length === 0) {
          error = 'Title is required';
        } else if (value.trim().length < 3) {
          error = 'Title must be at least 3 characters';
        } else if (value.trim().length > 100) {
          error = 'Title must be less than 100 characters';
        }
        break;
      
      case 'amount':
        if (!value || value <= 0) {
          error = 'Amount must be greater than 0';
        } else if (value > 1000000) {
          error = 'Amount seems too large. Please verify.';
        }
        break;
      
      case 'category':
        if (!value || value === '') {
          error = 'Category is required';
        }
        break;
      
      case 'date':
        if (!value) {
          error = 'Date is required';
        } else {
          const date = new Date(value);
          const today = new Date();
          const oneYearAgo = new Date();
          oneYearAgo.setFullYear(today.getFullYear() - 1);
          const oneYearAhead = new Date();
          oneYearAhead.setFullYear(today.getFullYear() + 1);
          
          if (date < oneYearAgo) {
            error = 'Date cannot be more than 1 year in the past';
          } else if (date > oneYearAhead) {
            error = 'Date cannot be more than 1 year in the future';
          }
        }
        break;
        
      case 'description':
        if (value && value.length > 500) {
          error = 'Description must be less than 500 characters';
        }
        break;
    }

    return error;
  }, []);

  const handleInputChange = useCallback((field: keyof ExpenseFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Real-time validation
    const error = validateField(field, value);
    setErrors(prev => {
      const newErrors = { ...prev };
      if (error) {
        newErrors[field] = error;
      } else {
        delete newErrors[field];
      }
      return newErrors;
    });
  }, [validateField]);

  const handleDateChange = (date: Date | undefined) => {
    if (date) {
      handleInputChange('date', format(date, 'yyyy-MM-dd'));
    }
  };

  const handleRecurrenceChange = (value: ExpenseRecurrence) => {
    handleInputChange('recurrence', value);
    setShowRecurring(value !== 'none');
    if (value === 'none') {
      handleInputChange('recurrence_end_date', '');
    }
  };

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // In a real app, you would upload to a server
    const url = URL.createObjectURL(file);
    handleInputChange('receipt_url', url);
    toast.success('Receipt uploaded successfully');
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate form
    const validationErrors = validateExpense(formData);
    if (validationErrors.length > 0) {
      const errorMap: Record<string, string> = {};
      validationErrors.forEach(err => {
        errorMap[err.field] = err.message;
      });
      setErrors(errorMap);
      return;
    }

    setIsSubmitting(true);
    try {
      const submitData = {
        ...formData,
        department_id: formData.department_id === 'none' ? '' : formData.department_id
      };
      await onSubmit(submitData);
      toast.success(isEditing ? 'Expense updated successfully' : 'Expense added successfully');
    } catch (error) {
      console.error('Failed to save expense:', error);
      setErrors({ form: 'Failed to save expense. Please try again.' });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSaveAsTemplate = () => {
    const name = prompt('Enter template name:');
    if (name && onSaveAsTemplate) {
      onSaveAsTemplate({
        name,
        title: formData.title,
        description: formData.description || '',
        amount: formData.amount,
        category: formData.category,
        department_id: formData.department_id || '',
        recurrence: formData.recurrence || 'none',
      });
      toast.success('Template saved successfully');
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>
          {quickMode ? 'Quick Expense' : isEditing ? 'Edit Expense' : 'Add New Expense'}
        </CardTitle>
        {!quickMode && (
          <CardDescription>
            Fill in the details below to {isEditing ? 'update' : 'record'} an expense
          </CardDescription>
        )}
      </CardHeader>
      
      <form onSubmit={handleSubmit}>
        <CardContent className="space-y-6">
          {/* Template Selection */}
          {templates && templates.length > 0 && !quickMode && (
            <div className="space-y-2">
              <Label htmlFor="template">Use Template</Label>
              <Select 
                onValueChange={(value) => {
                  const template = templates.find(t => t.id === value);
                  if (template) {
                    handleInputChange('title', template.title);
                    handleInputChange('description', template.description);
                    handleInputChange('amount', template.amount);
                    handleInputChange('category', template.category);
                    handleInputChange('department_id', template.department_id || 'none');
                    handleInputChange('recurrence', template.recurrence);
                    toast.success('Template applied');
                  }
                }}
              >
                <SelectTrigger id="template">
                  <SelectValue placeholder="Select a template" />
                </SelectTrigger>
                <SelectContent>
                  {templates.map((template) => (
                    <SelectItem key={template.id} value={template.id}>
                      {template.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}

          {/* Title and Amount Row */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="title" className="flex items-center gap-2">
                <FileText className="h-4 w-4" />
                Title <span className="text-destructive">*</span>
              </Label>
              <Input
                id="title"
                type="text"
                value={formData.title}
                onChange={(e) => handleInputChange('title', e.target.value)}
                placeholder="e.g., AWS Certification Exam"
                className={cn(errors.title && "border-destructive")}
                data-testid="expense-title-input"
              />
              {errors.title && (
                <p className="text-sm text-destructive">{errors.title}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="amount" className="flex items-center gap-2">
                <DollarSign className="h-4 w-4" />
                Amount <span className="text-destructive">*</span>
              </Label>
              <Input
                id="amount"
                type="number"
                value={formData.amount || ''}
                onChange={(e) => handleInputChange('amount', parseFloat(e.target.value) || 0)}
                placeholder="0.00"
                min={0.01}
                max={1000000}
                step={0.01}
                className={cn(errors.amount && "border-destructive")}
                data-testid="expense-amount-input"
              />
              {errors.amount && (
                <p className="text-sm text-destructive">{errors.amount}</p>
              )}
            </div>
          </div>

          {/* Category and Department Row */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="category" className="flex items-center gap-2">
                <Tag className="h-4 w-4" />
                Category <span className="text-destructive">*</span>
              </Label>
              <Select
                value={formData.category}
                onValueChange={(value) => {
                  handleInputChange('category', value);
                  setSuggestedCategory(null);
                }}
              >
                <SelectTrigger id="category" className={cn(errors.category && "border-destructive")}>
                  <SelectValue placeholder="Select category" />
                </SelectTrigger>
                <SelectContent>
                  {defaultExpenseCategories.map((cat) => (
                    <SelectItem key={cat.name} value={cat.name}>
                      <div className="flex items-center gap-2">
                        <div className={cn("w-3 h-3 rounded-full", cat.color)} />
                        {cat.name}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.category && (
                <p className="text-sm text-destructive">{errors.category}</p>
              )}
              
              {/* Category Suggestion */}
              {suggestedCategory && !formData.category && (
                <Alert className="mt-2">
                  <Zap className="h-4 w-4" />
                  <AlertDescription className="flex items-center justify-between">
                    <span>Suggested: {suggestedCategory}</span>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => handleInputChange('category', suggestedCategory)}
                    >
                      Apply
                    </Button>
                  </AlertDescription>
                </Alert>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="department" className="flex items-center gap-2">
                <Building className="h-4 w-4" />
                Department
              </Label>
              <Select
                value={formData.department_id || undefined}
                onValueChange={(value) => handleInputChange('department_id', value)}
              >
                <SelectTrigger id="department">
                  <SelectValue placeholder="Select department (optional)" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">No department</SelectItem>
                  {departments.map((dept) => (
                    <SelectItem key={dept.id} value={dept.id}>
                      {dept.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Date and Status Row */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="date" className="flex items-center gap-2">
                <CalendarIcon className="h-4 w-4" />
                Date <span className="text-destructive">*</span>
              </Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    id="date"
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !formData.date && "text-muted-foreground",
                      errors.date && "border-destructive"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {formData.date ? format(new Date(formData.date), 'PPP') : 'Pick a date'}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={formData.date ? new Date(formData.date) : undefined}
                    onSelect={handleDateChange}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
              {errors.date && (
                <p className="text-sm text-destructive">{errors.date}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="status">Status</Label>
              <Select
                value={formData.status}
                onValueChange={(value) => handleInputChange('status', value as ExpenseStatus)}
              >
                <SelectTrigger id="status">
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  {statusOptions.map((status) => (
                    <SelectItem key={status.value} value={status.value}>
                      <span className={status.color}>{status.label}</span>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Description */}
          {!quickMode && (
            <div className="space-y-2">
              <Label htmlFor="description" className="flex items-center gap-2">
                <FileText className="h-4 w-4" />
                Description
              </Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="Add any additional details..."
                rows={3}
                className={cn(errors.description && "border-destructive")}
                data-testid="expense-description-textarea"
              />
              {errors.description && (
                <p className="text-sm text-destructive">{errors.description}</p>
              )}
            </div>
          )}

          {/* Recurring Expense Toggle */}
          {!quickMode && (
            <Card className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <RefreshCw className="h-5 w-5 text-primary" />
                  <div>
                    <Label htmlFor="recurring" className="text-base font-semibold">
                      Recurring Expense
                    </Label>
                    <p className="text-sm text-muted-foreground">
                      Set up automatic recurring expenses
                    </p>
                  </div>
                </div>
                <Switch
                  id="recurring"
                  checked={showRecurring}
                  onCheckedChange={(checked) => {
                    setShowRecurring(checked);
                    if (!checked) {
                      handleRecurrenceChange('none');
                    }
                  }}
                />
              </div>
            </Card>
          )}

          {/* Recurrence Options */}
          {showRecurring && !quickMode && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="recurrence">Frequency</Label>
                <Select
                  value={formData.recurrence}
                  onValueChange={handleRecurrenceChange}
                >
                  <SelectTrigger id="recurrence">
                    <SelectValue placeholder="Select frequency" />
                  </SelectTrigger>
                  <SelectContent>
                    {recurrenceOptions.slice(1).map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="recurrence_end_date">End Date</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !formData.recurrence_end_date && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {formData.recurrence_end_date 
                        ? format(new Date(formData.recurrence_end_date), 'PPP') 
                        : 'Select end date'}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={formData.recurrence_end_date ? new Date(formData.recurrence_end_date) : undefined}
                      onSelect={(date) => date && handleInputChange('recurrence_end_date', format(date, 'yyyy-MM-dd'))}
                      initialFocus
                      disabled={(date) => date < new Date(formData.date)}
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>
          )}

          {/* Receipt Upload */}
          <div className="space-y-2">
            <Label htmlFor="receipt">Receipt Upload</Label>
            <div className="flex items-center gap-4">
              <Input
                id="receipt"
                type="file"
                accept="image/*,.pdf"
                onChange={handleFileUpload}
                className="hidden"
              />
              <Button
                type="button"
                variant="outline"
                onClick={() => document.getElementById('receipt')?.click()}
              >
                <Upload className="h-4 w-4 mr-2" />
                Upload Receipt
              </Button>
              {formData.receipt_url && (
                <Badge variant="secondary" className="flex items-center gap-2">
                  <CheckCircle className="h-3 w-3" />
                  Receipt uploaded
                </Badge>
              )}
            </div>
          </div>

          {/* Form Error */}
          {errors.form && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{errors.form}</AlertDescription>
            </Alert>
          )}
        </CardContent>

        <CardFooter className="flex items-center justify-between gap-3">
          <div className="flex gap-2">
            {!quickMode && onSaveAsTemplate && (
              <Button
                type="button"
                variant="outline"
                onClick={handleSaveAsTemplate}
                disabled={isSubmitting}
              >
                Save as Template
              </Button>
            )}
          </div>
          <div className="flex gap-2">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                    className="mr-2"
                  >
                    <RefreshCw className="h-4 w-4" />
                  </motion.div>
                  Saving...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  {isEditing ? 'Update Expense' : 'Save Expense'}
                </>
              )}
            </Button>
          </div>
        </CardFooter>
      </form>
    </Card>
  );
};

export default ExpenseForm;
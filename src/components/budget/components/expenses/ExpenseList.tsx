import React, { useState, useMemo } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { format } from 'date-fns';
import {
  Search,
  Download,
  MoreVertical,
  Edit,
  Trash2,
  Receipt,
  Eye,
  FileText,
  Filter,
  ArrowUp,
  ArrowDown,
  DollarSign,
  CheckCircle,
  Clock,
  Copy,
  SlidersHorizontal
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { Expense } from '../../types/expense.types';

interface ExpenseListProps {
  expenses: Expense[];
  departments: any[];
  onEdit: (expense: Expense) => void;
  onDelete: (id: string) => void;
  onStatusChange: (id: string, status: Expense['status']) => void;
  onViewReceipt: (url: string) => void;
  loading?: boolean;
}

const statusConfig = {
  draft: { 
    label: 'Draft', 
    color: 'bg-gray-100 text-gray-700 border-gray-200',
    icon: FileText
  },
  submitted: { 
    label: 'Submitted', 
    color: 'bg-blue-100 text-blue-700 border-blue-200',
    icon: Clock
  },
  paid: { 
    label: 'Paid', 
    color: 'bg-green-100 text-green-700 border-green-200',
    icon: CheckCircle
  },
  reimbursed: { 
    label: 'Reimbursed', 
    color: 'bg-purple-100 text-purple-700 border-purple-200',
    icon: DollarSign
  },
};

const categoryColors: Record<string, string> = {
  'Online Courses': 'bg-blue-500',
  'Conferences': 'bg-purple-500',
  'Certifications': 'bg-yellow-500',
  'Books & Resources': 'bg-green-500',
  'Workshops': 'bg-orange-500',
  'Coaching': 'bg-pink-500',
  'Software & Tools': 'bg-indigo-500',
  'Other': 'bg-gray-500',
};

export const ExpenseList: React.FC<ExpenseListProps> = ({
  expenses,
  departments,
  onEdit,
  onDelete,
  onStatusChange,
  onViewReceipt,
  loading = false,
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [sortField, setSortField] = useState<'date' | 'amount' | 'title'>('date');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [filterCategory, setFilterCategory] = useState<string>('all');

  // Filter and sort expenses
  const filteredExpenses = useMemo(() => {
    let filtered = [...expenses];

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(expense =>
        expense.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        expense.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        expense.category.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Status filter
    if (filterStatus !== 'all') {
      filtered = filtered.filter(expense => expense.status === filterStatus);
    }

    // Category filter
    if (filterCategory !== 'all') {
      filtered = filtered.filter(expense => expense.category === filterCategory);
    }

    // Sort
    filtered.sort((a, b) => {
      let aVal: any = a[sortField];
      let bVal: any = b[sortField];

      if (sortField === 'date') {
        aVal = new Date(aVal).getTime();
        bVal = new Date(bVal).getTime();
      }

      if (sortDirection === 'asc') {
        return aVal > bVal ? 1 : -1;
      } else {
        return aVal < bVal ? 1 : -1;
      }
    });

    return filtered;
  }, [expenses, searchTerm, sortField, sortDirection, filterStatus, filterCategory]);

  const totalAmount = filteredExpenses.reduce((sum, expense) => sum + expense.amount, 0);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const handleSort = (field: 'date' | 'amount' | 'title') => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('desc');
    }
  };

  const handleExport = () => {
    const data = filteredExpenses.map(expense => ({
      Date: format(new Date(expense.date), 'yyyy-MM-dd'),
      Title: expense.title,
      Description: expense.description || '',
      Amount: expense.amount,
      Category: expense.category,
      Department: departments.find(d => d.id === expense.department_id)?.name || 'No Department',
      Status: expense.status,
    }));

    // Convert to CSV
    const headers = Object.keys(data[0] || {});
    const csv = [
      headers.join(','),
      ...data.map(row => 
        headers.map(header => 
          JSON.stringify(row[header as keyof typeof row] || '')
        ).join(',')
      )
    ].join('\n');

    // Download
    const blob = new Blob([csv], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `expenses-${format(new Date(), 'yyyy-MM-dd')}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const categories = Array.from(new Set(expenses.map(e => e.category)));

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="p-3 bg-secondary rounded-xl">
            <FileText className="h-6 w-6 text-secondary-foreground" />
          </div>
          <div>
            <h2 className="text-xl font-semibold text-card-foreground">Expenses</h2>
            <p className="text-sm text-muted-foreground">
              Manage your training and development expenses
            </p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                <SlidersHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-56" align="end">
              <div className="p-2">
                <label className="text-xs font-medium text-muted-foreground">Status</label>
                <Select value={filterStatus} onValueChange={setFilterStatus}>
                  <SelectTrigger className="mt-1 h-8">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="draft">Draft</SelectItem>
                    <SelectItem value="submitted">Submitted</SelectItem>
                    <SelectItem value="paid">Paid</SelectItem>
                    <SelectItem value="reimbursed">Reimbursed</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <DropdownMenuSeparator />
              <div className="p-2">
                <label className="text-xs font-medium text-muted-foreground">Category</label>
                <Select value={filterCategory} onValueChange={setFilterCategory}>
                  <SelectTrigger className="mt-1 h-8">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Categories</SelectItem>
                    {categories.map(cat => (
                      <SelectItem key={cat} value={cat}>{cat}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </DropdownMenuContent>
          </DropdownMenu>
          <Button variant="outline" size="sm" onClick={handleExport}>
            <Download className="h-4 w-4" />
            <span className="ml-2 hidden sm:inline">Export</span>
          </Button>
        </div>
      </div>

      {/* Search Bar */}
      <div className="flex gap-2">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search by title, description, or category..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <Button variant="outline">
          <Filter className="h-4 w-4 mr-2" />
          Filter
        </Button>
      </div>

      {/* Summary Card */}
      <Card className="p-4 bg-card border-border">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Eye className="h-5 w-5 text-primary" />
            <span className="text-sm font-medium text-card-foreground">
              Showing {filteredExpenses.length} of {expenses.length} expenses
            </span>
          </div>
          <div className="flex items-center gap-2">
            <DollarSign className="h-5 w-5 text-green-500" />
            <div className="text-right">
              <p className="text-xl font-bold text-card-foreground">{formatCurrency(totalAmount)}</p>
              <p className="text-xs text-muted-foreground">{filteredExpenses.length} items</p>
            </div>
          </div>
        </div>
      </Card>

      {/* Expense Table */}
      <Card className="overflow-hidden bg-card">
        <Table>
          <TableHeader>
            <TableRow className="bg-muted/30 border-border">
              <TableHead 
                className="cursor-pointer hover:bg-muted/50 transition-colors text-card-foreground"
                onClick={() => handleSort('date')}
              >
                <div className="flex items-center gap-1">
                  Date
                  {sortField === 'date' && (
                    sortDirection === 'asc' ? 
                    <ArrowUp className="h-3 w-3" /> : 
                    <ArrowDown className="h-3 w-3" />
                  )}
                </div>
              </TableHead>
              <TableHead 
                className="cursor-pointer hover:bg-muted/50 transition-colors text-card-foreground"
                onClick={() => handleSort('title')}
              >
                <div className="flex items-center gap-1">
                  Title
                  {sortField === 'title' && (
                    sortDirection === 'asc' ? 
                    <ArrowUp className="h-3 w-3" /> : 
                    <ArrowDown className="h-3 w-3" />
                  )}
                </div>
              </TableHead>
              <TableHead className="text-card-foreground">Category</TableHead>
              <TableHead className="text-card-foreground">Department</TableHead>
              <TableHead 
                className="cursor-pointer hover:bg-muted/50 transition-colors text-card-foreground"
                onClick={() => handleSort('amount')}
              >
                <div className="flex items-center gap-1">
                  Amount
                  {sortField === 'amount' && (
                    sortDirection === 'asc' ? 
                    <ArrowUp className="h-3 w-3" /> : 
                    <ArrowDown className="h-3 w-3" />
                  )}
                </div>
              </TableHead>
              <TableHead className="text-card-foreground">Status</TableHead>
              <TableHead></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredExpenses.length === 0 ? (
              <TableRow>
                <TableCell colSpan={7} className="text-center py-8 bg-card">
                  <div className="flex flex-col items-center gap-2">
                    <Receipt className="h-8 w-8 text-muted-foreground" />
                    <p className="font-medium text-card-foreground">No expenses found</p>
                    <p className="text-sm text-muted-foreground">
                      {searchTerm ? 'Try adjusting your search' : 'Add your first expense to get started'}
                    </p>
                  </div>
                </TableCell>
              </TableRow>
            ) : (
              filteredExpenses.map((expense) => {
                const StatusIcon = statusConfig[expense.status].icon;
                return (
                  <TableRow key={expense.id} className="hover:bg-muted/50">
                    <TableCell>
                      {format(new Date(expense.date), 'MMM d, yyyy')}
                    </TableCell>
                    <TableCell>
                      <div>
                        <p className="font-medium">{expense.title}</p>
                        {expense.description && (
                          <p className="text-xs text-muted-foreground line-clamp-1">
                            {expense.description}
                          </p>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <div className={cn('h-2 w-2 rounded-full', categoryColors[expense.category] || 'bg-gray-500')} />
                        <span className="text-sm">{expense.category}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <span className="text-sm text-muted-foreground">
                        {departments.find(d => d.id === expense.department_id)?.name || 'No Department'}
                      </span>
                    </TableCell>
                    <TableCell>
                      <span className="font-semibold">{formatCurrency(expense.amount)}</span>
                    </TableCell>
                    <TableCell>
                      <Badge className={cn('gap-1', statusConfig[expense.status].color)}>
                        <StatusIcon className="h-3 w-3" />
                        {statusConfig[expense.status].label}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                            <MoreVertical className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => onEdit(expense)}>
                            <Edit className="h-4 w-4 mr-2" />
                            Edit
                          </DropdownMenuItem>
                          {expense.receipt_url && (
                            <DropdownMenuItem onClick={() => onViewReceipt(expense.receipt_url!)}>
                              <Eye className="h-4 w-4 mr-2" />
                              View Receipt
                            </DropdownMenuItem>
                          )}
                          <DropdownMenuItem onClick={() => navigator.clipboard.writeText(expense.id)}>
                            <Copy className="h-4 w-4 mr-2" />
                            Copy ID
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem 
                            onClick={() => onDelete(expense.id)}
                            className="text-red-600 focus:text-red-600"
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                );
              })
            )}
          </TableBody>
        </Table>
      </Card>
    </div>
  );
};
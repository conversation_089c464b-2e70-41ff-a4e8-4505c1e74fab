import React from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { ExpenseForm } from '../expenses/ExpenseForm';
import { ExpenseFormData, ExpenseTemplate } from '../../types/expense.types';
import { DepartmentAllocation } from '@/stores/budgetStore';

interface ExpenseDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (data: ExpenseFormData) => Promise<boolean>;
  departments: DepartmentAllocation[];
  initialData?: Partial<ExpenseFormData>;
  isEditing?: boolean;
  templates?: ExpenseTemplate[];
  onSaveAsTemplate?: (template: Omit<ExpenseTemplate, 'id' | 'created_at' | 'updated_at'>) => void;
  title?: string;
  description?: string;
}

export const ExpenseDialog: React.FC<ExpenseDialogProps> = ({
  open,
  onOpenChange,
  onSubmit,
  departments,
  initialData,
  isEditing = false,
  templates = [],
  onSaveAsTemplate,
  title,
  description,
}) => {
  const handleSubmit = async (data: ExpenseFormData) => {
    await onSubmit(data);
    onOpenChange(false);
  };

  const handleCancel = () => {
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
        <DialogHeader className="sr-only">
          <DialogTitle>
            {title || (isEditing ? 'Edit Expense' : 'Add New Expense')}
          </DialogTitle>
          {description && (
            <DialogDescription>{description}</DialogDescription>
          )}
        </DialogHeader>
        
        <ExpenseForm
          onSubmit={handleSubmit}
          onCancel={handleCancel}
          departments={departments}
          initialData={initialData}
          isEditing={isEditing}
          quickMode={false}
          templates={templates}
          onSaveAsTemplate={onSaveAsTemplate}
        />
      </DialogContent>
    </Dialog>
  );
};

// Quick Expense Dialog - Simplified version for quick entry
export const QuickExpenseDialog: React.FC<Omit<ExpenseDialogProps, 'title' | 'description' | 'isEditing'>> = ({
  open,
  onOpenChange,
  onSubmit,
  departments,
  templates = [],
  onSaveAsTemplate,
}) => {
  const handleSubmit = async (data: ExpenseFormData) => {
    await onSubmit(data);
    onOpenChange(false);
  };

  const handleCancel = () => {
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md">
        <DialogHeader className="sr-only">
          <DialogTitle>Quick Expense</DialogTitle>
          <DialogDescription>
            Quickly add a new expense
          </DialogDescription>
        </DialogHeader>
        
        <ExpenseForm
          onSubmit={handleSubmit}
          onCancel={handleCancel}
          departments={departments}
          quickMode={true}
          templates={templates}
          onSaveAsTemplate={onSaveAsTemplate}
        />
      </DialogContent>
    </Dialog>
  );
};

export default ExpenseDialog;
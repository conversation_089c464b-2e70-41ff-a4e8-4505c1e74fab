import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Activity,
  Calendar
} from 'lucide-react';

// Import existing components
import { BudgetHealthScore } from '../../components/healthscore/BudgetHealthScore';
import { SpendingHeatmap } from '../../components/heatmap/SpendingHeatmap';

// Import proper types
import { 
  BudgetStats, 
  HealthMetrics, 
  HeatmapData,
  ViewMode 
} from '../../types/budget.types';

interface BudgetOverviewLayoutProps {
  // Data props
  budget: {
    total_amount: number;
    allocated_amount: number;
    spent_amount: number;
    remaining_amount: number;
  };
  stats: BudgetStats;
  expenses: Array<{
    id: string;
    amount: number;
    category: string;
    date: string;
    description: string;
  }>;
  healthMetrics: HealthMetrics;
  heatmapData?: HeatmapData;
  loading?: boolean;
  
  // Layout configuration
  layoutMode?: ViewMode;
  
  // Event handlers
  onCardClick?: (cardType: string) => void;
  onMetricFocus?: (metric: string) => void;
  onViewDetails?: (section: string) => void;
  onRefresh?: () => void;
}

export const BudgetOverviewLayout: React.FC<BudgetOverviewLayoutProps> = ({
  budget,
  stats,
  expenses,
  healthMetrics,
  heatmapData,
  loading = false,
  layoutMode = 'dashboard',
  onCardClick,
  onMetricFocus,
  onViewDetails,
  onRefresh
}) => {
  // Removed expandedSections, toggleSection, and suggested actions logic

  // Format currency helper
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  // Render based on layout mode
  const renderLayout = () => {
    switch (layoutMode) {
      case 'focus':
        return renderFocusLayout();
      case 'comparison':
        return renderComparisonLayout();
      case 'timeline':
        return renderTimelineLayout();
      default:
        return renderDashboardLayout();
    }
  };

  // Dashboard Layout (Default) - Implements 12-column grid system
  const renderDashboardLayout = () => (
    <motion.div 
      className="space-y-6"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      {/* Budget Health Score - Full Width Hero Card */}
      <Card className="bg-gradient-to-br from-primary/5 via-transparent to-transparent border-primary/20">
        <CardContent className="p-6">
          {healthMetrics && (
            <BudgetHealthScore
              metrics={healthMetrics}
              onRefresh={onRefresh}
              onViewDetails={(metric) => onViewDetails?.(metric)}
              showRecommendations={true}
              compactMode={false}
              loading={loading}
            />
          )}
        </CardContent>
      </Card>

      {/* Removed Suggested Actions section */}

      {/* Removed Primary Metrics Cards */}

      {/* Removed Secondary Content Grid with Spending by Category and Quick Stats */}

      {/* Tertiary Content - Full Width */}
      {heatmapData && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                Spending Patterns
              </CardTitle>
            </CardHeader>
            <CardContent>
              <SpendingHeatmap
                data={heatmapData}
                currentMonth={new Date()}
                onDayClick={(date, data) => console.log('Day clicked:', date, data)}
                loading={loading}
              />
            </CardContent>
          </Card>
        </motion.div>
      )}
    </motion.div>
  );

  // Focus Layout - Single metric expanded view
  const renderFocusLayout = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">Focus View</h2>
        <Badge variant="secondary">Coming Soon</Badge>
      </div>
      <Card className="min-h-[60vh] flex items-center justify-center p-8">
        <div className="text-center space-y-4">
          <Activity className="h-12 w-12 mx-auto text-muted-foreground" />
          <h3 className="text-lg font-medium">Expanded Metric View</h3>
          <p className="text-muted-foreground">
            This view will show an expanded, detailed view of a single metric
          </p>
        </div>
      </Card>
    </div>
  );

  // Comparison Layout - Side by side comparison
  const renderComparisonLayout = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">Comparison View</h2>
        <Badge variant="secondary">Coming Soon</Badge>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Current Period</CardTitle>
          </CardHeader>
          <CardContent className="flex items-center justify-center min-h-[200px]">
            <p className="text-muted-foreground text-center">
              Current period data
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader>
            <CardTitle>Previous Period</CardTitle>
          </CardHeader>
          <CardContent className="flex items-center justify-center min-h-[200px]">
            <p className="text-muted-foreground text-center">
              Comparison data will be shown here
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  );

  // Timeline Layout - Chronological view
  const renderTimelineLayout = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">Timeline View</h2>
        <Badge variant="secondary">Coming Soon</Badge>
      </div>
      <Card className="min-h-[60vh] flex items-center justify-center p-8">
        <div className="text-center space-y-4">
          <Calendar className="h-12 w-12 mx-auto text-muted-foreground" />
          <h3 className="text-lg font-medium">Budget Timeline</h3>
          <p className="text-muted-foreground">
            This view will show budget events in chronological order
          </p>
        </div>
      </Card>
    </div>
  );

  // Removed renderQuickStats function

  return renderLayout();
};

export default BudgetOverviewLayout;
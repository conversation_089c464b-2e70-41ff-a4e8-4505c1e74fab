import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  BookOpen,
  Save,
  Download,
  Upload,
  MoreVertical,
  Copy,
  Edit,
  Trash2,
  FileText,
  Calendar,
  DollarSign,
  Users,
  Shield,
  Sparkles,
} from 'lucide-react';
import { formatCurrency } from '../../utils/formatters';
import { useBudgetStoreExtended } from '@/stores/budgetStoreExtended';
import { toast } from 'sonner';

// Local BudgetTemplate interface - kept separate for compatibility
// Local BudgetTemplate interface - kept separate for compatibility
interface BudgetTemplate {
  id: string;
  name: string;
  description: string;
  type: 'standard' | 'department' | 'project' | 'custom';
  created: string;
  lastModified: string;
  author: string;
  shared: boolean;
  tags: string[];
  config: {
    totalBudget: number;
    fiscalYear: number;
    categories: Array<{
      id: string;
      name: string;
      percentage: number;
      fixedAmount?: number;
      rules?: {
        minPercentage?: number;
        maxPercentage?: number;
        requiresApproval?: boolean;
        approvalThreshold?: number;
      };
    }>;
    departments: Array<{
      id: string;
      name: string;
      percentage: number;
      fixedAmount?: number;
    }>;
    allocations: Array<{
      id: string;
      categoryId: string;
      departmentId: string;
      amount: number;
      notes?: string;
    }>;
    rules: {
      carryoverEnabled: boolean;
      carryoverPercentage?: number;
      reserveFundPercentage?: number;
      quarterlyDistribution?: number[];
      autoAllocationEnabled?: boolean;
    };
  };
  metrics?: {
    timesUsed: number;
    avgEfficiency: number;
    lastUsed?: string;
  };
}

interface BudgetTemplatesProps {
  templates: BudgetTemplate[];
  onSaveTemplate: (template: Omit<BudgetTemplate, 'id' | 'created' | 'lastModified' | 'metrics'>) => void;
  onApplyTemplate: (templateId: string) => void;
  onUpdateTemplate: (id: string, template: Partial<BudgetTemplate>) => void;
  onDeleteTemplate: (id: string) => void;
  onExportTemplate: (id: string) => void;
  onImportTemplate: (file: File) => void;
  loading?: boolean;
}

const TemplateIcon = ({ type }: { type: string }) => {
  switch (type) {
    case 'standard': return <BookOpen className="h-5 w-5" />;
    case 'department': return <Users className="h-5 w-5" />;
    case 'project': return <FileText className="h-5 w-5" />;
    case 'custom': return <Sparkles className="h-5 w-5" />;
    default: return <FileText className="h-5 w-5" />;
  }
};

export const BudgetTemplates: React.FC<BudgetTemplatesProps> = ({
  templates,
  onSaveTemplate,
  onApplyTemplate,
  onUpdateTemplate,
  onDeleteTemplate,
  onExportTemplate,
  onImportTemplate,
  loading = false,
}) => {
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [showPreviewDialog, setShowPreviewDialog] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<BudgetTemplate | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterType, setFilterType] = useState<string>('all');
  
  const { budget, departmentAllocations, stats, templates: storeTemplates } = useBudgetStoreExtended();

  const [formData, setFormData] = useState<{
    name: string;
    description: string;
    type: 'standard' | 'department' | 'project' | 'custom';
    shared: boolean;
    tags: string[];
    config: BudgetTemplate['config'];
  }>({
    name: '',
    description: '',
    type: 'standard',
    shared: false,
    tags: [],
    config: {
      totalBudget: budget?.total_amount || 0,
      fiscalYear: budget?.year || new Date().getFullYear(),
      categories: [],
      departments: [],
      allocations: [],
      rules: {
        carryoverEnabled: false,
        reserveFundPercentage: 5,
        quarterlyDistribution: [25, 25, 25, 25],
        autoAllocationEnabled: false,
      },
    },
  });

  const handleSaveCurrentAsTemplate = () => {
    // Build template from current budget configuration
    const currentConfig = {
      totalBudget: budget?.total_amount || 0,
      fiscalYear: budget?.year || new Date().getFullYear(),
      categories: (stats?.by_category || []).map(cat => ({
        id: cat.category,
        name: cat.category,
        percentage: (cat.total / (budget?.total_amount || 1)) * 100,
        fixedAmount: cat.total,
        rules: {
          minPercentage: 5,
          maxPercentage: 50,
          requiresApproval: cat.total > 50000,
          approvalThreshold: 50000,
        },
      })),
      departments: departmentAllocations.map(dept => ({
        id: dept.id,
        name: dept.name,  
        percentage: ((dept.amount || (dept as any).allocated_amount) / (budget?.total_amount || 1)) * 100,
        fixedAmount: dept.amount || (dept as any).allocated_amount,
      })),
      allocations: departmentAllocations.map(alloc => ({
        id: alloc.id,
        categoryId: '',  // Not available in new structure
        departmentId: alloc.id,
        amount: alloc.amount || (alloc as any).allocated_amount,
        notes: '',  // Not available in new structure
      })),
      rules: {
        carryoverEnabled: true,
        carryoverPercentage: 10,
        reserveFundPercentage: 5,
        quarterlyDistribution: [25, 25, 25, 25],
        autoAllocationEnabled: true,
      },
    };

    setFormData({
      ...formData,
      config: currentConfig,
    });
    setShowCreateDialog(true);
  };

  const handleCreateTemplate = () => {
    onSaveTemplate({
      ...formData,
      author: 'Current User', // Would come from auth
    });
    setShowCreateDialog(false);
    setFormData({
      name: '',
      description: '',
      type: 'standard',
      shared: false,
      tags: [],
      config: {
        totalBudget: 0,
        fiscalYear: new Date().getFullYear(),
        categories: [],
        departments: [],
        allocations: [],
        rules: {
          carryoverEnabled: false,
          reserveFundPercentage: 5,
          quarterlyDistribution: [25, 25, 25, 25],
          autoAllocationEnabled: false,
        },
      },
    });
    toast.success('Template saved successfully');
  };

  const handleApplyTemplate = (templateId: string) => {
    onApplyTemplate(templateId);
    toast.success('Template applied successfully');
  };

  const handleFileImport = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      onImportTemplate(file);
      toast.success('Template imported successfully');
    }
  };

  const filteredTemplates = templates.filter(template => {
    const matchesSearch = template.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         template.description.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesType = filterType === 'all' || template.type === filterType;
    return matchesSearch && matchesType;
  });

  if (loading) {
    return (
      <Card className="p-6">
        <Skeleton className="h-6 w-48 mb-4" />
        <Skeleton className="h-64 w-full" />
      </Card>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
    >
      <Card className="p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <BookOpen className="h-5 w-5 text-muted-foreground" />
            <h3 className="text-lg font-semibold">Budget Templates</h3>
            <Badge variant="secondary">{templates.length} templates</Badge>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleSaveCurrentAsTemplate}
            >
              <Save className="h-4 w-4 mr-2" />
              Save Current
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => document.getElementById('import-template')?.click()}
            >
              <Upload className="h-4 w-4 mr-2" />
              Import
            </Button>
            <input
              id="import-template"
              type="file"
              accept=".json"
              className="hidden"
              onChange={handleFileImport}
            />
          </div>
        </div>

        {/* Search and Filter */}
        <div className="flex items-center gap-4 mb-6">
          <div className="flex-1">
            <Input
              placeholder="Search templates..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full"
            />
          </div>
          <Select value={filterType} onValueChange={setFilterType}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Types</SelectItem>
              <SelectItem value="standard">Standard</SelectItem>
              <SelectItem value="department">Department</SelectItem>
              <SelectItem value="project">Project</SelectItem>
              <SelectItem value="custom">Custom</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Templates List */}
        {filteredTemplates.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <BookOpen className="h-12 w-12 mx-auto mb-3 opacity-50" />
            <p>No templates found</p>
            <p className="text-sm mt-1">Create your first template from current budget</p>
          </div>
        ) : (
          <div className="space-y-3">
            {filteredTemplates.map((template) => (
              <Card key={template.id} className="p-4">
                <div className="flex items-start justify-between">
                  <div className="flex items-start gap-3">
                    <div className="p-2 bg-muted rounded-lg">
                      <TemplateIcon type={template.type} />
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <h4 className="font-medium">{template.name}</h4>
                        <Badge variant="secondary" className="text-xs">
                          {template.type}
                        </Badge>
                        {template.shared && (
                          <Shield className="h-3 w-3 text-muted-foreground" />
                        )}
                      </div>
                      <p className="text-sm text-muted-foreground mb-2">
                        {template.description}
                      </p>
                      <div className="flex items-center gap-4 text-xs text-muted-foreground">
                        <span className="flex items-center gap-1">
                          <Calendar className="h-3 w-3" />
                          Created {new Date(template.created).toLocaleDateString()}
                        </span>
                        <span className="flex items-center gap-1">
                          <DollarSign className="h-3 w-3" />
                          {formatCurrency(template.config.totalBudget)}
                        </span>
                        {template.metrics && (
                          <span>Used {template.metrics.timesUsed} times</span>
                        )}
                      </div>
                      {template.tags.length > 0 && (
                        <div className="flex flex-wrap gap-1 mt-2">
                          {template.tags.map((tag, idx) => (
                            <Badge key={idx} variant="outline" className="text-xs">
                              {tag}
                            </Badge>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <Button
                      variant="default"
                      size="sm"
                      onClick={() => handleApplyTemplate(template.id)}
                    >
                      Apply
                    </Button>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreVertical className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem
                          onClick={() => {
                            setSelectedTemplate(template);
                            setShowPreviewDialog(true);
                          }}
                        >
                          Preview
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => onExportTemplate(template.id)}>
                          <Download className="h-4 w-4 mr-2" />
                          Export
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <Copy className="h-4 w-4 mr-2" />
                          Duplicate
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem>
                          <Edit className="h-4 w-4 mr-2" />
                          Edit
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          className="text-destructive"
                          onClick={() => onDeleteTemplate(template.id)}
                        >
                          <Trash2 className="h-4 w-4 mr-2" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        )}
      </Card>

      {/* Create Template Dialog */}
      <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Save Budget Template</DialogTitle>
            <DialogDescription>
              Save your current budget configuration as a reusable template.
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">Template Name</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  placeholder="e.g., FY2024 Standard Budget"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="type">Template Type</Label>
                <Select
                  value={formData.type}
                  onValueChange={(value: any) => setFormData({ ...formData, type: value })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="standard">Standard</SelectItem>
                    <SelectItem value="department">Department-based</SelectItem>
                    <SelectItem value="project">Project-based</SelectItem>
                    <SelectItem value="custom">Custom</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                placeholder="Describe the template and when to use it..."
                rows={3}
              />
            </div>

            <div className="space-y-2">
              <Label>Tags (comma-separated)</Label>
              <Input
                value={formData.tags.join(', ')}
                onChange={(e) => setFormData({ 
                  ...formData, 
                  tags: e.target.value.split(',').map(t => t.trim()).filter(Boolean)
                })}
                placeholder="e.g., training, technology, quarterly"
              />
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="shared"
                checked={formData.shared}
                onCheckedChange={(checked: boolean) => 
                  setFormData({ ...formData, shared: checked })
                }
              />
              <Label htmlFor="shared" className="text-sm font-normal">
                Share this template with other team members
              </Label>
            </div>

            {/* Template Preview */}
            <div className="border rounded-lg p-4 bg-muted/50">
              <h4 className="font-medium mb-2">Template Configuration</h4>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <p className="text-muted-foreground">Total Budget</p>
                  <p className="font-medium">{formatCurrency(formData.config.totalBudget)}</p>
                </div>
                <div>
                  <p className="text-muted-foreground">Fiscal Year</p>
                  <p className="font-medium">{formData.config.fiscalYear}</p>
                </div>
                <div>
                  <p className="text-muted-foreground">Categories</p>
                  <p className="font-medium">{formData.config.categories.length} configured</p>
                </div>
                <div>
                  <p className="text-muted-foreground">Departments</p>
                  <p className="font-medium">{formData.config.departments.length} configured</p>
                </div>
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowCreateDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleCreateTemplate} disabled={!formData.name}>
              Save Template
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Preview Template Dialog */}
      <Dialog open={showPreviewDialog} onOpenChange={setShowPreviewDialog}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Template Preview: {selectedTemplate?.name}</DialogTitle>
            <DialogDescription>
              Review template configuration before applying
            </DialogDescription>
          </DialogHeader>

          {selectedTemplate && (
            <div className="space-y-6 py-4">
              {/* Template Info */}
              <div className="grid grid-cols-2 gap-4">
                <Card className="p-4">
                  <h4 className="font-medium mb-2">Budget Overview</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Total Budget</span>
                      <span className="font-medium">
                        {formatCurrency(selectedTemplate.config.totalBudget)}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Fiscal Year</span>
                      <span className="font-medium">{selectedTemplate.config.fiscalYear}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Reserve Fund</span>
                      <span className="font-medium">
                        {selectedTemplate.config.rules.reserveFundPercentage}%
                      </span>
                    </div>
                  </div>
                </Card>

                <Card className="p-4">
                  <h4 className="font-medium mb-2">Rules & Settings</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex items-center justify-between">
                      <span className="text-muted-foreground">Carryover</span>
                      <Badge variant={selectedTemplate.config.rules.carryoverEnabled ? 'default' : 'secondary'}>
                        {selectedTemplate.config.rules.carryoverEnabled ? 'Enabled' : 'Disabled'}
                      </Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-muted-foreground">Auto-allocation</span>
                      <Badge variant={selectedTemplate.config.rules.autoAllocationEnabled ? 'default' : 'secondary'}>
                        {selectedTemplate.config.rules.autoAllocationEnabled ? 'Enabled' : 'Disabled'}
                      </Badge>
                    </div>
                  </div>
                </Card>
              </div>

              {/* Categories Breakdown */}
              <Card className="p-4">
                <h4 className="font-medium mb-3">Category Allocation</h4>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Category</TableHead>
                      <TableHead>Percentage</TableHead>
                      <TableHead>Amount</TableHead>
                      <TableHead>Rules</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {selectedTemplate.config.categories.map((cat) => (
                      <TableRow key={cat.id}>
                        <TableCell>{cat.name}</TableCell>
                        <TableCell>{cat.percentage.toFixed(1)}%</TableCell>
                        <TableCell>{formatCurrency(cat.fixedAmount || 0)}</TableCell>
                        <TableCell>
                          {cat.rules?.requiresApproval && (
                            <Badge variant="outline" className="text-xs">
                              Approval Required
                            </Badge>
                          )}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </Card>
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowPreviewDialog(false)}>
              Close
            </Button>
            <Button
              onClick={() => {
                if (selectedTemplate) {
                  handleApplyTemplate(selectedTemplate.id);
                  setShowPreviewDialog(false);
                }
              }}
            >
              Apply Template
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </motion.div>
  );
};
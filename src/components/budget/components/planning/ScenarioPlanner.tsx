import React, { useState, useMemo } from 'react';
import { motion } from 'framer-motion';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  BarChart,
  Bar,
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar,
} from 'recharts';
import {
  Zap,
  RefreshCw,
  Save,
  AlertTriangle,
  CheckCircle,
  TrendingUp,
  TrendingDown,
  Calculator,
  Shuffle,
  Play,
  History,
} from 'lucide-react';
import { formatCurrency, formatPercentage } from '../../utils/formatters';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useBudgetStore } from '@/stores/budgetStore';

export interface Scenario {
  id: string;
  name: string;
  description: string;
  created: string;
  parameters: {
    budgetChange: number; // percentage change
    categoryAdjustments: Record<string, number>; // category ID to percentage change
    departmentAdjustments: Record<string, number>; // department ID to percentage change
    timeframe: 'quarterly' | 'yearly';
    constraints: {
      minCategoryPercentage: number;
      maxCategoryPercentage: number;
      maintainRatios: boolean;
      respectRules: boolean;
    };
  };
  results: {
    totalBudget: number;
    categoryAllocations: Record<string, number>;
    departmentAllocations: Record<string, number>;
    feasibility: 'optimal' | 'viable' | 'risky' | 'infeasible';
    warnings: string[];
    opportunities: string[];
    score: number; // 0-100
  };
}

interface ScenarioPlannerProps {
  scenarios: Scenario[];
  onCreateScenario: (scenario: Omit<Scenario, 'id' | 'created'>) => void;
  onUpdateScenario: (id: string, scenario: Partial<Scenario>) => void;
  onDeleteScenario: (id: string) => void;
  onApplyScenario: (id: string) => void;
  onRunSimulation: (parameters: Scenario['parameters']) => Scenario['results'];
  loading?: boolean;
}

const CustomTooltip = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    return (
      <div className="bg-popover border rounded-lg shadow-lg p-3">
        <p className="font-medium mb-2">{label}</p>
        {payload.map((entry: any, index: number) => (
          <div key={index} className="flex items-center justify-between gap-4 text-sm">
            <span className="flex items-center gap-2">
              <span
                className="w-3 h-3 rounded-full"
                style={{ backgroundColor: entry.color }}
              />
              {entry.name}:
            </span>
            <span className="font-medium">
              {entry.name.includes('%') 
                ? formatPercentage(entry.value / 100)
                : formatCurrency(entry.value)}
            </span>
          </div>
        ))}
      </div>
    );
  }
  return null;
};

export const ScenarioPlanner: React.FC<ScenarioPlannerProps> = ({
  scenarios = [],
  onCreateScenario,
  onUpdateScenario,
  onDeleteScenario,
  onApplyScenario,
  onRunSimulation,
  loading = false,
}) => {
  const { budget, stats } = useBudgetStore();
  const [activeScenario, setActiveScenario] = useState<Scenario | null>(null);
  const [isSimulating, setIsSimulating] = useState(false);
  
  const [parameters, setParameters] = useState<Scenario['parameters']>({
    budgetChange: 0,
    categoryAdjustments: {},
    departmentAdjustments: {},
    timeframe: 'yearly',
    constraints: {
      minCategoryPercentage: 5,
      maxCategoryPercentage: 40,
      maintainRatios: false,
      respectRules: true,
    },
  });

  const [simulationResults, setSimulationResults] = useState<Scenario['results'] | null>(null);
  const [activeTab, setActiveTab] = useState('parameters');

  const handleRunSimulation = async () => {
    setIsSimulating(true);
    try {
      // Simulate delay for realistic feel
      await new Promise(resolve => setTimeout(resolve, 1000));
      const results = onRunSimulation(parameters);
      setSimulationResults(results);
    } finally {
      setIsSimulating(false);
    }
  };

  const handleSaveScenario = () => {
    if (!simulationResults) return;
    
    const name = prompt('Enter scenario name:');
    if (!name) return;

    onCreateScenario({
      name,
      description: `Budget change: ${parameters.budgetChange}%`,
      parameters,
      results: simulationResults,
    });
  };

  const handleQuickScenario = (type: 'growth' | 'reduction' | 'rebalance') => {
    switch (type) {
      case 'growth':
        setParameters({
          ...parameters,
          budgetChange: 20,
          categoryAdjustments: Object.fromEntries(
            (stats?.by_category || []).map(cat => [cat.category, 15])
          ),
        });
        break;
      case 'reduction':
        setParameters({
          ...parameters,
          budgetChange: -15,
          categoryAdjustments: Object.fromEntries(
            (stats?.by_category || []).map(cat => [cat.category, -10])
          ),
        });
        break;
      case 'rebalance':
        const adjustments: Record<string, number> = {};
        (stats?.by_category || []).forEach((cat, idx) => {
          adjustments[cat.category] = idx % 2 === 0 ? 10 : -10;
        });
        setParameters({
          ...parameters,
          budgetChange: 0,
          categoryAdjustments: adjustments,
        });
        break;
    }
    handleRunSimulation();
  };

  // Prepare comparison data
  const comparisonData = useMemo(() => {
    if (!simulationResults || !budget) return [];
    
    return (stats?.by_category || []).map(cat => ({
      category: cat.category,
      current: cat.total,
      scenario: simulationResults.categoryAllocations[cat.category] || 0,
      change: ((simulationResults.categoryAllocations[cat.category] || 0) - cat.total) / cat.total * 100,
    }));
  }, [stats, simulationResults, budget]);

  // Prepare radar chart data
  const radarData = useMemo(() => {
    if (!simulationResults) return [];
    
    return [
      { metric: 'Budget Efficiency', value: simulationResults.score },
      { metric: 'Risk Level', value: 100 - (simulationResults.feasibility === 'optimal' ? 90 : 
                                           simulationResults.feasibility === 'viable' ? 70 :
                                           simulationResults.feasibility === 'risky' ? 40 : 20) },
      { metric: 'Coverage', value: 85 },
      { metric: 'Flexibility', value: parameters.constraints.maintainRatios ? 60 : 90 },
      { metric: 'Compliance', value: parameters.constraints.respectRules ? 95 : 50 },
    ];
  }, [simulationResults, parameters]);

  if (loading) {
    return (
      <Card className="p-6">
        <Skeleton className="h-6 w-48 mb-4" />
        <Skeleton className="h-64 w-full" />
      </Card>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
    >
      <Card className="p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <Zap className="h-5 w-5 text-muted-foreground" />
            <h3 className="text-lg font-semibold">What-if Scenario Planner</h3>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleQuickScenario('growth')}
            >
              <TrendingUp className="h-4 w-4 mr-2" />
              Growth
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleQuickScenario('reduction')}
            >
              <TrendingDown className="h-4 w-4 mr-2" />
              Reduction
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleQuickScenario('rebalance')}
            >
              <Shuffle className="h-4 w-4 mr-2" />
              Rebalance
            </Button>
          </div>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="parameters">Parameters</TabsTrigger>
            <TabsTrigger value="results" disabled={!simulationResults}>Results</TabsTrigger>
            <TabsTrigger value="comparison" disabled={!simulationResults}>Comparison</TabsTrigger>
            <TabsTrigger value="history">History</TabsTrigger>
          </TabsList>

          <TabsContent value="parameters" className="mt-6 space-y-6">
            {/* Overall Budget Change */}
            <Card className="p-4">
              <h4 className="font-medium mb-4">Overall Budget Adjustment</h4>
              <div className="space-y-4">
                <div>
                  <div className="flex justify-between mb-2">
                    <Label>Budget Change</Label>
                    <span className={`font-medium ${parameters.budgetChange >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                      {parameters.budgetChange > 0 && '+'}{parameters.budgetChange}%
                    </span>
                  </div>
                  <Slider
                    value={[parameters.budgetChange]}
                    onValueChange={(value) => setParameters({
                      ...parameters,
                      budgetChange: value[0],
                    })}
                    min={-50}
                    max={50}
                    step={5}
                    className="w-full"
                  />
                  <div className="flex justify-between text-xs text-muted-foreground mt-1">
                    <span>-50%</span>
                    <span>0%</span>
                    <span>+50%</span>
                  </div>
                </div>

                <div className="flex items-center gap-4">
                  <div className="flex-1">
                    <Label>Current Budget</Label>
                    <p className="text-2xl font-bold">{formatCurrency(budget?.total_amount || 0)}</p>
                  </div>
                  <div className="text-2xl">→</div>
                  <div className="flex-1">
                    <Label>New Budget</Label>
                    <p className="text-2xl font-bold text-primary">
                      {formatCurrency((budget?.total_amount || 0) * (1 + parameters.budgetChange / 100))}
                    </p>
                  </div>
                </div>
              </div>
            </Card>

            {/* Category Adjustments */}
            <Card className="p-4">
              <h4 className="font-medium mb-4">Category Adjustments</h4>
              <div className="space-y-3">
                {(stats?.by_category || []).map(cat => (
                  <div key={cat.category}>
                    <div className="flex justify-between mb-1">
                      <Label className="text-sm">{cat.category}</Label>
                      <span className={`text-sm font-medium ${
                        (parameters.categoryAdjustments[cat.category] || 0) >= 0 ? 'text-green-600' : 'text-red-600'
                      }`}>
                        {(parameters.categoryAdjustments[cat.category] || 0) > 0 && '+'}
                        {parameters.categoryAdjustments[cat.category] || 0}%
                      </span>
                    </div>
                    <Slider
                      value={[parameters.categoryAdjustments[cat.category] || 0]}
                      onValueChange={(value) => setParameters({
                        ...parameters,
                        categoryAdjustments: {
                          ...parameters.categoryAdjustments,
                          [cat.category]: value[0],
                        },
                      })}
                      min={-30}
                      max={30}
                      step={5}
                      className="w-full"
                    />
                  </div>
                ))}
              </div>
            </Card>

            {/* Constraints */}
            <Card className="p-4">
              <h4 className="font-medium mb-4">Simulation Constraints</h4>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>Min Category %</Label>
                  <Input
                    type="number"
                    value={parameters.constraints.minCategoryPercentage}
                    onChange={(e) => setParameters({
                      ...parameters,
                      constraints: {
                        ...parameters.constraints,
                        minCategoryPercentage: parseInt(e.target.value) || 0,
                      },
                    })}
                    min={0}
                    max={100}
                  />
                </div>
                <div className="space-y-2">
                  <Label>Max Category %</Label>
                  <Input
                    type="number"
                    value={parameters.constraints.maxCategoryPercentage}
                    onChange={(e) => setParameters({
                      ...parameters,
                      constraints: {
                        ...parameters.constraints,
                        maxCategoryPercentage: parseInt(e.target.value) || 100,
                      },
                    })}
                    min={0}
                    max={100}
                  />
                </div>
              </div>
            </Card>

            <Button
              className="w-full"
              size="lg"
              onClick={handleRunSimulation}
              disabled={isSimulating}
            >
              {isSimulating ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Running Simulation...
                </>
              ) : (
                <>
                  <Play className="h-4 w-4 mr-2" />
                  Run Simulation
                </>
              )}
            </Button>
          </TabsContent>

          <TabsContent value="results" className="mt-6 space-y-4">
            {simulationResults && (
              <>
                {/* Feasibility Alert */}
                <Alert className={
                  simulationResults.feasibility === 'optimal' ? 'border-green-500' :
                  simulationResults.feasibility === 'viable' ? 'border-blue-500' :
                  simulationResults.feasibility === 'risky' ? 'border-orange-500' :
                  'border-destructive'
                }>
                  <div className="flex items-start gap-2">
                    {simulationResults.feasibility === 'optimal' ? <CheckCircle className="h-4 w-4" /> :
                     simulationResults.feasibility === 'infeasible' ? <AlertTriangle className="h-4 w-4" /> :
                     <AlertTriangle className="h-4 w-4" />}
                    <div>
                      <AlertTitle>
                        Scenario {simulationResults.feasibility === 'optimal' ? 'Optimal' :
                                 simulationResults.feasibility === 'viable' ? 'Viable' :
                                 simulationResults.feasibility === 'risky' ? 'Risky' : 'Not Feasible'}
                      </AlertTitle>
                      <AlertDescription>
                        Score: {simulationResults.score}/100
                      </AlertDescription>
                    </div>
                  </div>
                </Alert>

                {/* Warnings & Opportunities */}
                <div className="grid grid-cols-2 gap-4">
                  {simulationResults.warnings.length > 0 && (
                    <Card className="p-4">
                      <h4 className="font-medium mb-2 text-orange-600">Warnings</h4>
                      <ul className="list-disc list-inside space-y-1 text-sm">
                        {simulationResults.warnings.map((warning, idx) => (
                          <li key={idx}>{warning}</li>
                        ))}
                      </ul>
                    </Card>
                  )}
                  
                  {simulationResults.opportunities.length > 0 && (
                    <Card className="p-4">
                      <h4 className="font-medium mb-2 text-green-600">Opportunities</h4>
                      <ul className="list-disc list-inside space-y-1 text-sm">
                        {simulationResults.opportunities.map((opp, idx) => (
                          <li key={idx}>{opp}</li>
                        ))}
                      </ul>
                    </Card>
                  )}
                </div>

                {/* Radar Chart */}
                <Card className="p-4">
                  <h4 className="font-medium mb-4">Scenario Metrics</h4>
                  <div className="h-64">
                    <ResponsiveContainer width="100%" height="100%">
                      <RadarChart data={radarData}>
                        <PolarGrid />
                        <PolarAngleAxis dataKey="metric" className="text-xs" />
                        <PolarRadiusAxis angle={90} domain={[0, 100]} />
                        <Radar
                          name="Score"
                          dataKey="value"
                          stroke="#3b82f6"
                          fill="#3b82f6"
                          fillOpacity={0.6}
                        />
                        <Tooltip />
                      </RadarChart>
                    </ResponsiveContainer>
                  </div>
                </Card>

                <Button onClick={handleSaveScenario} className="w-full">
                  <Save className="h-4 w-4 mr-2" />
                  Save Scenario
                </Button>
              </>
            )}
          </TabsContent>

          <TabsContent value="comparison" className="mt-6 space-y-4">
            {simulationResults && (
              <>
                {/* Comparison Chart */}
                <Card className="p-4">
                  <h4 className="font-medium mb-4">Current vs Scenario</h4>
                  <div className="h-80">
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart data={comparisonData}>
                        <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
                        <XAxis
                          dataKey="category"
                          className="text-xs"
                          tick={{ fill: 'currentColor' }}
                          angle={-45}
                          textAnchor="end"
                          height={80}
                        />
                        <YAxis
                          className="text-xs"
                          tick={{ fill: 'currentColor' }}
                          tickFormatter={(value) => `${(value / 1000).toFixed(0)}k`}
                        />
                        <Tooltip content={<CustomTooltip />} />
                        <Legend />
                        <Bar dataKey="current" fill="#10b981" name="Current" />
                        <Bar dataKey="scenario" fill="#3b82f6" name="Scenario" />
                      </BarChart>
                    </ResponsiveContainer>
                  </div>
                </Card>

                {/* Detailed Comparison Table */}
                <Card className="p-4">
                  <h4 className="font-medium mb-4">Detailed Comparison</h4>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Category</TableHead>
                        <TableHead className="text-right">Current</TableHead>
                        <TableHead className="text-right">Scenario</TableHead>
                        <TableHead className="text-right">Change</TableHead>
                        <TableHead className="text-right">% Change</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {comparisonData.map((item) => (
                        <TableRow key={item.category}>
                          <TableCell>{item.category}</TableCell>
                          <TableCell className="text-right">
                            {formatCurrency(item.current)}
                          </TableCell>
                          <TableCell className="text-right">
                            {formatCurrency(item.scenario)}
                          </TableCell>
                          <TableCell className={`text-right ${
                            item.scenario - item.current >= 0 ? 'text-green-600' : 'text-red-600'
                          }`}>
                            {item.scenario - item.current >= 0 && '+'}
                            {formatCurrency(item.scenario - item.current)}
                          </TableCell>
                          <TableCell className={`text-right ${
                            item.change >= 0 ? 'text-green-600' : 'text-red-600'
                          }`}>
                            {item.change >= 0 && '+'}{item.change.toFixed(1)}%
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </Card>
              </>
            )}
          </TabsContent>

          <TabsContent value="history" className="mt-6">
            {scenarios.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                <History className="h-12 w-12 mx-auto mb-3 opacity-50" />
                <p>No saved scenarios yet</p>
                <p className="text-sm mt-1">Run simulations and save scenarios to see them here</p>
              </div>
            ) : (
              <div className="space-y-3">
                {scenarios.map((scenario) => (
                  <Card key={scenario.id} className="p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <h5 className="font-medium">{scenario.name}</h5>
                          <Badge 
                            variant={
                              scenario.results.feasibility === 'optimal' ? 'default' :
                              scenario.results.feasibility === 'viable' ? 'secondary' :
                              scenario.results.feasibility === 'risky' ? 'outline' :
                              'destructive'
                            }
                          >
                            {scenario.results.feasibility}
                          </Badge>
                          <Badge variant="outline">
                            Score: {scenario.results.score}
                          </Badge>
                        </div>
                        <p className="text-sm text-muted-foreground mb-2">
                          {scenario.description}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          Created {new Date(scenario.created).toLocaleDateString()}
                        </p>
                      </div>
                      <div className="flex items-center gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            setParameters(scenario.parameters);
                            setSimulationResults(scenario.results);
                          }}
                        >
                          Load
                        </Button>
                        <Button
                          variant="default"
                          size="sm"
                          onClick={() => onApplyScenario(scenario.id)}
                        >
                          Apply
                        </Button>
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            )}
          </TabsContent>
        </Tabs>
      </Card>
    </motion.div>
  );
};
import React, { forwardRef, useRef } from 'react';
import { Button, ButtonProps } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { 
  touchTargetClass, 
  simulateHapticFeedback, 
  useLongPress,
  getOptimalTouchSize
} from '../../utils/mobile-gestures';
import { motion } from 'framer-motion';

interface TouchButtonProps {
  /** Button content */
  children?: React.ReactNode;
  /** Additional CSS classes */
  className?: string;
  /** Click handler */
  onClick?: (event: React.MouseEvent<HTMLButtonElement>) => void;
  /** Disabled state */
  disabled?: boolean;
  /** Button size */
  size?: 'default' | 'sm' | 'lg' | 'icon';
  /** Button variant */
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  /** Inline styles */
  style?: React.CSSProperties;
  /** Enable haptic feedback on touch */
  hapticFeedback?: boolean;
  /** Long press handler */
  onLongPress?: () => void;
  /** Long press delay in ms */
  longPressDelay?: number;
  /** Force minimum touch target size */
  touchOptimized?: boolean;
  /** Loading state with touch feedback */
  loading?: boolean;
  /** Disable button but keep touch target */
  touchDisabled?: boolean;
}

export const TouchButton = forwardRef<HTMLButtonElement, TouchButtonProps>(
  ({
    children,
    className,
    onClick,
    hapticFeedback = true,
    onLongPress,
    longPressDelay = 500,
    touchOptimized = true,
    loading = false,
    touchDisabled = false,
    disabled,
    size = 'default',
    variant = 'default',
    ...props
  }, ref) => {
    const buttonRef = useRef<HTMLButtonElement>(null);
    const combinedRef = (ref as React.RefObject<HTMLButtonElement>) || buttonRef;

    const longPressProps = useLongPress(
      () => onLongPress?.(),
      longPressDelay
    );

    const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
      if (touchDisabled || loading) return;
      
      // Haptic feedback
      if (hapticFeedback && combinedRef.current) {
        simulateHapticFeedback('light', combinedRef.current);
      }
      
      onClick?.(event);
    };

    const handleTouchStart = () => {
      // Visual feedback for touch start
      if (combinedRef.current) {
        combinedRef.current.style.transform = 'scale(0.97)';
      }
    };

    const handleTouchEnd = () => {
      // Reset visual feedback
      if (combinedRef.current) {
        setTimeout(() => {
          if (combinedRef.current) {
            combinedRef.current.style.transform = '';
          }
        }, 100);
      }
    };

    const isActuallyDisabled = disabled || touchDisabled || loading;

    return (
      <motion.div
        whileTap={!isActuallyDisabled ? { scale: 0.97 } : undefined}
        transition={{ duration: 0.1 }}
      >
        <Button
          ref={combinedRef}
          size={size}
          variant={variant}
          disabled={isActuallyDisabled}
          onClick={handleClick}
          onTouchStart={handleTouchStart}
          onTouchEnd={handleTouchEnd}
          aria-busy={loading}
          aria-describedby={loading ? 'loading-text' : undefined}
          {...(onLongPress ? longPressProps : {})}
          {...props}
          className={cn(
            // Base touch-friendly styles
            touchOptimized && touchTargetClass,
            'select-none touch-manipulation',
            'transition-all duration-200',
            // Ensure visual feedback works
            'active:scale-[0.97]',
            // Loading state
            loading && 'cursor-wait',
            // Touch disabled state (different from regular disabled)
            touchDisabled && !disabled && 'opacity-60 cursor-not-allowed',
            className
          )}
          style={{
            WebkitTapHighlightColor: 'transparent',
            ...props.style,
          }}
          {...props}
        >
          {loading ? (
            <div className="flex items-center gap-2">
              <motion.div
                animate={{ rotate: 360 }}
                transition={{
                  duration: 1,
                  repeat: Infinity,
                  ease: 'linear',
                }}
                className="w-4 h-4 border-2 border-current border-t-transparent rounded-full"
              />
              <span>{typeof children === 'string' ? 'Loading...' : children}</span>
            </div>
          ) : (
            children
          )}
        </Button>
      </motion.div>
    );
  }
);

TouchButton.displayName = 'TouchButton';

// Specific touch button variants
export const TouchIconButton = forwardRef<HTMLButtonElement, TouchButtonProps & {
  icon: React.ReactNode;
  'aria-label': string;
}>(({ icon, children, className, ...props }, ref) => (
  <TouchButton
    ref={ref}
    variant="ghost"
    size="icon"
    className={cn(
      touchTargetClass,
      'rounded-xl', // More touch-friendly rounded corners
      className
    )}
    {...props}
  >
    <div className="flex items-center justify-center">
      {icon}
    </div>
    {children}
  </TouchButton>
));

TouchIconButton.displayName = 'TouchIconButton';

export const TouchFloatingButton = forwardRef<HTMLButtonElement, TouchButtonProps>(
  ({ className, children, ...props }, ref) => (
    <TouchButton
      ref={ref}
      className={cn(
        // Floating action button styles
        'fixed bottom-6 right-6 z-50',
        'w-14 h-14 rounded-full', // Larger than minimum for FAB
        'shadow-lg shadow-primary/30',
        'bg-primary hover:bg-primary/90 text-primary-foreground',
        className
      )}
      {...props}
    >
      {children}
    </TouchButton>
  )
);

TouchFloatingButton.displayName = 'TouchFloatingButton';
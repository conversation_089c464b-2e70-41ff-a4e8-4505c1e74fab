import React from 'react';
import { SpendingTrendsChart } from '../components/analytics/charts/SpendingTrendsChart';
import { BudgetBurndown } from '../components/analytics/charts/BudgetBurndown';
import { YearOverYearComparison } from '../components/analytics/charts/YearOverYearComparison';
import { ForecastProjections } from '../components/analytics/charts/ForecastProjections';
import { ROITracking } from '../components/analytics/charts/ROITracking';
import { BudgetStats } from '../types/budget.types';
import { Budget } from '@/stores/budgetStore';
import { ROIMetric } from '../components/analytics/charts/ROITracking';

interface AnalyticsData {
  trends: {
    monthly?: any[];
    yearly?: any[];
  };
  insights?: {
    top_categories?: any[];
  };
}

interface AnalyticsTabPageProps {
  analytics: AnalyticsData | null;
  budget: Budget | null;
  stats: BudgetStats | null;
  roiMetrics: ROIMetric[];
  year: number;
  loading: boolean;
  onAddROIMetric: (metric: any) => void;
  onUpdateROIMetric: (id: string, updates: any) => void;
}

export const AnalyticsTabPage: React.FC<AnalyticsTabPageProps> = ({
  analytics,
  budget,
  stats,
  roiMetrics,
  year,
  loading,
  onAddROIMetric,
  onUpdateROIMetric
}) => {
  const totalBudget = budget?.total_amount || stats?.total || 0;
  const currentSpent = stats?.spent || 0;
  const remainingDays = Math.floor(
    (new Date(year, 11, 31).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)
  );

  // Transform data for charts
  const burndownData = analytics?.trends?.monthly?.map((item: any) => ({
    date: item.month,
    planned: item.budget || totalBudget,
    actual: item.spent,
    projected: item.spent * 1.1
  })) || [];

  const yearComparisonData = analytics?.insights?.top_categories?.map((cat: any) => ({
    category: cat.category,
    [year - 1]: 0, // Mock previous year data
    [year]: cat.amount
  })) || [];

  const yearSummaries = analytics?.trends?.yearly?.map((y: any) => ({
    year: y.year,
    totalBudget: y.budget,
    totalSpent: y.spent,
    efficiency: y.budget > 0 ? (y.spent / y.budget) * 100 : 0,
    growth: y.growth || 0,
    categories: {},
    departments: {}
  })) || [];

  const historicalData = analytics?.trends?.monthly?.map((item: any) => ({
    date: item.month,
    actual: item.spent,
    forecast: item.budget,
    optimistic: item.budget * 1.1,
    realistic: item.budget,
    pessimistic: item.budget * 0.9,
    confidence: 0.8
  })) || [];

  return (
    <div className="space-y-6">
      {/* Spending Trends */}
      <SpendingTrendsChart
        data={analytics?.trends?.monthly?.map((item: any) => ({
          period: item.month,
          budget: item.budget || 0,
          spent: item.spent || 0,
          categories: item.categories || {}
        })) || []}
        period="monthly"
        onPeriodChange={() => {}}
        loading={loading}
      />
      
      {/* Budget Burndown */}
      <BudgetBurndown
        data={burndownData}
        totalBudget={totalBudget}
        currentSpent={currentSpent}
        endDate={new Date(year, 11, 31).toISOString()}
        loading={loading}
      />
      
      {/* Year-over-Year Comparison */}
      <YearOverYearComparison
        data={yearComparisonData}
        currentYear={year}
        summaries={yearSummaries}
        comparisonYears={[year - 1, year]}
        loading={loading}
      />
      
      {/* Forecast Projections */}
      <ForecastProjections
        historicalData={historicalData}
        totalBudget={totalBudget}
        currentSpent={currentSpent}
        remainingDays={remainingDays}
        loading={loading}
      />
      
      {/* ROI Tracking */}
      <ROITracking
        metrics={roiMetrics}
        onAddMetric={onAddROIMetric}
        onUpdateMetric={onUpdateROIMetric}
        loading={loading}
      />
    </div>
  );
};

export default AnalyticsTabPage;
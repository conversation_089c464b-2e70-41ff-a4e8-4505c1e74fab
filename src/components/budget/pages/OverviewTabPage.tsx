import React from 'react';
import { BudgetOverviewLayout } from '../components/layouts/BudgetOverviewLayout';
import { BudgetStats, HealthMetrics, HeatmapData } from '../types/budget.types';
import { Budget } from '@/stores/budgetStore';
import { Expense } from '../types/expense.types';

interface OverviewTabPageProps {
  budget: Budget | null;
  stats: BudgetStats | null;
  expenses: Expense[];
  healthMetrics: HealthMetrics | null;
  heatmapData: HeatmapData | null;
  loading: boolean;
  year: number;
  onViewDetails: (section: string) => void;
  onCardClick: (cardType: string) => void;
  onRefresh: () => void;
  onTabChange: (tab: string) => void;
}

export const OverviewTabPage: React.FC<OverviewTabPageProps> = ({
  budget,
  stats,
  expenses,
  healthMetrics,
  heatmapData,
  loading,
  onViewDetails,
  onCardClick,
  onRefresh,
  onTabChange
}) => {
  const handleViewDetails = (section: string) => {
    if (section === 'analytics') {
      onTabChange('analytics');
    } else if (section === 'expenses') {
      onTabChange('expenses');
    } else if (section === 'categories') {
      onTabChange('budget');
    } else if (['increase', 'reallocate', 'plan'].includes(section)) {
      onTabChange('budget');
    } else if (section === 'review') {
      onTabChange('expenses');
    }
    onViewDetails(section);
  };

  const handleCardClick = (cardType: string) => {
    if (cardType === 'total-budget') {
      console.log('Total budget card clicked');
    } else if (cardType === 'spent') {
      onTabChange('expenses');
    } else if (cardType === 'remaining') {
      console.log('Remaining budget card clicked');
    }
    onCardClick(cardType);
  };

  // Prepare budget data for the layout
  const budgetData = budget ? {
    total_amount: budget.total_amount,
    allocated_amount: 0, // Placeholder since this doesn't exist in Budget type
    spent_amount: budget.spent_amount || 0,
    remaining_amount: budget.total_amount - (budget.spent_amount || 0)
  } : {
    total_amount: 0,
    allocated_amount: 0,
    spent_amount: 0,
    remaining_amount: 0
  };

  // Default stats if not available
  const defaultStats: BudgetStats = {
    total: 0,
    spent: 0,
    remaining: 0,
    committed: 0,
    utilization: 0,
    trend: 0,
    by_category: []
  };

  // Default health metrics if not available
  const defaultHealthMetrics: HealthMetrics = {
    totalBudget: 0,
    totalSpent: 0,
    utilization: 0,
    burnRate: 0,
    projectedSpend: 0,
    riskScore: 0,
    savingsOpportunity: 0,
    complianceRate: 95,
    efficiencyScore: 0,
    categoryDistribution: {},
    monthlyTrend: [],
    score: 0,
    status: 'unknown',
    indicators: {
      spending: { value: 0, status: 'healthy', trend: 'stable' },
      efficiency: { value: 0, status: 'healthy', trend: 'stable' },
      risk: { value: 0, status: 'healthy', trend: 'stable' },
      compliance: { value: 95, status: 'healthy', trend: 'stable' }
    },
    recommendations: []
  };

  return (
    <BudgetOverviewLayout
      budget={budgetData}
      stats={stats || defaultStats}
      expenses={expenses}
      healthMetrics={healthMetrics || defaultHealthMetrics}
      heatmapData={heatmapData || undefined}
      loading={loading}
      onViewDetails={handleViewDetails}
      onCardClick={handleCardClick}
      onRefresh={onRefresh}
    />
  );
};

export default OverviewTabPage;
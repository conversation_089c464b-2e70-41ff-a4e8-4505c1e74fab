import { ExpenseStatus } from '@/stores/budgetStore';
import { Expense as StoreExpense } from '@/stores/budgetStore';

// Define the Expense type that matches what we use in components
export interface Expense {
  id: string;
  title: string;
  description: string;
  amount: number;
  category: string;
  status: ExpenseStatus;
  date: string;
  created_at: string;
  updated_at: string;
  department_id: string | null;
  recurrence: string;
  recurrence_end_date: string | null;
  receipt_url: string | null;
}

// Re-export for convenience
export type { ExpenseStatus };

export interface ExpenseFormData {
  title: string;
  description?: string;
  amount: number;
  category: string;
  department_id?: string | null;
  status: ExpenseStatus;
  date: string;
  receipt_url?: string | null;
  recurrence?: string;
  recurrence_end_date?: string | null;
}

export interface ExpenseTemplate {
  id: string;
  name: string;
  title: string;
  description: string;
  amount: number;
  category: string;
  department_id?: string;
  recurrence: string;
  created_at: string;
  updated_at: string;
}

export interface ExpenseFilters {
  status?: ExpenseStatus[];
  categories?: string[];
  departments?: string[];
  dateRange?: {
    start: string;
    end: string;
  };
  amountRange?: {
    min: number;
    max: number;
  };
  searchTerm?: string;
}

export interface ExpenseSortOptions {
  field: 'date' | 'amount' | 'title' | 'category' | 'status';
  direction: 'asc' | 'desc';
}

export interface ExpenseValidationRules {
  title: {
    required: boolean;
    minLength: number;
    maxLength: number;
  };
  amount: {
    required: boolean;
    min: number;
    max: number;
  };
  category: {
    required: boolean;
  };
  date: {
    required: boolean;
    maxDate: Date;
  };
  description: {
    maxLength: number;
  };
}

export const defaultExpenseValidationRules: ExpenseValidationRules = {
  title: {
    required: true,
    minLength: 3,
    maxLength: 100,
  },
  amount: {
    required: true,
    min: 0.01,
    max: 999999.99,
  },
  category: {
    required: true,
  },
  date: {
    required: true,
    maxDate: new Date(),
  },
  description: {
    maxLength: 500,
  },
};

export interface ExpenseCategory {
  id: string;
  name: string;
  color: string;
  icon?: string;
}

export const defaultExpenseCategories: ExpenseCategory[] = [
  { id: 'online-courses', name: 'Online Courses', color: 'bg-blue-500' },
  { id: 'conferences', name: 'Conferences', color: 'bg-purple-500' },
  { id: 'certifications', name: 'Certifications', color: 'bg-green-500' },
  { id: 'books', name: 'Books & Resources', color: 'bg-yellow-500' },
  { id: 'workshops', name: 'Workshops', color: 'bg-pink-500' },
  { id: 'coaching', name: 'Coaching', color: 'bg-indigo-500' },
  { id: 'software', name: 'Software & Tools', color: 'bg-red-500' },
  { id: 'other', name: 'Other', color: 'bg-gray-500' },
];
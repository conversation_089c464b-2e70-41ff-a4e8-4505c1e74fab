// Core Budget Types
export interface BudgetStats {
  total: number;
  spent: number;
  remaining: number;
  committed: number;
  utilization: number;
  trend: number;
  by_category: CategorySpending[];
}

export interface CategorySpending {
  category: string;
  amount: number;
  budget: number;
  percentage: number;
}

export interface CategoryLimit {
  id: string;
  category: string;
  limit: number;
  spent: number;
  remaining: number;
  percentage: number;
}

// Health Metrics Types
export interface HealthMetrics {
  // Basic metrics
  totalBudget: number;
  totalSpent: number;
  utilization: number;
  burnRate: number;
  projectedSpend: number;
  riskScore: number;
  savingsOpportunity: number;
  complianceRate: number;
  efficiencyScore: number;
  categoryDistribution: Record<string, number>;
  monthlyTrend: Array<{
    month: string;
    spent: number;
    budget: number;
  }>;
  score: number;
  status: 'excellent' | 'good' | 'warning' | 'critical' | 'unknown';
  
  // Detailed indicators
  indicators: {
    spending: {
      value: number;
      status: 'healthy' | 'warning' | 'critical';
      trend: 'increasing' | 'stable' | 'decreasing';
    };
    efficiency: {
      value: number;
      status: 'healthy' | 'warning' | 'critical';
      trend: 'increasing' | 'stable' | 'decreasing';
    };
    risk: {
      value: number;
      status: 'healthy' | 'warning' | 'critical';
      trend: 'increasing' | 'stable' | 'decreasing';
    };
    compliance: {
      value: number;
      status: 'healthy' | 'warning' | 'critical';
      trend: 'increasing' | 'stable' | 'decreasing';
    };
  };
  
  // Recommendations
  recommendations: Array<{
    type: 'warning' | 'info' | 'success';
    message: string;
    action: string;
  }>;
}

// Heatmap Types
export interface DailySpending {
  date: string;
  amount: number;
  transactions: number;
  categories: Record<string, number>;
  departments?: Record<string, number>;
  topExpense?: {
    description: string;
    amount: number;
    category: string;
  };
}

export interface HeatmapData {
  dailyData: DailySpending[];
  minAmount: number;
  maxAmount: number;
  averageAmount: number;
  totalAmount: number;
  averageDaily: number;
  peakDay: string;
  categories: string[];
  departments: string[];
}

// Budget Event Types
export interface BudgetEvent {
  id: string;
  date: Date;
  title: string;
  description?: string;
  type: 'expense' | 'allocation' | 'adjustment' | 'milestone';
  amount?: number;
  category?: string;
}

// Department Types
export interface DepartmentAllocation {
  id: string;
  name: string;
  allocated: number;
  spent: number;
  remaining: number;
  employees: number;
  projects: string[];
}

// Overview Component Props
export interface BudgetOverviewProps {
  stats: BudgetStats;
  healthMetrics: HealthMetrics;
  categoryLimits: CategoryLimit[];
  heatmapData?: HeatmapData;
  loading?: boolean;
  onViewDetails?: (section: string) => void;
  onQuickAction?: (action: string) => void;
  onRefresh?: () => void;
}

export interface BudgetOverviewLayoutProps {
  totalBudget: number;
  spent: number;
  remaining: number;
  categories: CategorySpending[];
  healthMetrics?: HealthMetrics;
  loading?: boolean;
  year?: number;
  layoutMode?: 'dashboard' | 'focus' | 'comparison' | 'timeline';
  onLayoutChange?: (mode: string) => void;
}

// Action Types
export type BudgetAction = 
  | 'allocate-budget'
  | 'generate-report'
  | 'set-goals'
  | 'view-analytics'
  | 'export-data'
  | 'import-data';

// View Modes
export type ViewMode = 'dashboard' | 'focus' | 'comparison' | 'timeline';

// Sort Options
export type SortBy = 'name' | 'amount' | 'percentage' | 'date';
export type SortOrder = 'asc' | 'desc';

// Filter Options
export interface FilterOptions {
  categories?: string[];
  departments?: string[];
  dateRange?: {
    start: Date;
    end: Date;
  };
  minAmount?: number;
  maxAmount?: number;
}
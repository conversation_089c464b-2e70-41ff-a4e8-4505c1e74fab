import { useState, useCallback, useMemo } from 'react';
import { useTraining } from '@/contexts/TrainingContext';

export const useTrainingWorkflow = () => {
  const { state } = useTraining();
  const [loading, setLoading] = useState(false);

  // Simplified workflow metrics without approval flows
  const metrics = useMemo(() => {
    const totalNeeds = state.trainingNeeds.length;
    const completedNeeds = state.trainingNeeds.filter(n => n.status === 'completed').length;
    const inProgressNeeds = state.trainingNeeds.filter(n => n.status === 'in_progress').length;
    const identifiedNeeds = state.trainingNeeds.filter(n => n.status === 'identified').length;
    const approvedNeeds = state.trainingNeeds.filter(n => n.status === 'approved').length;
    const onHoldNeeds = state.trainingNeeds.filter(n => n.status === 'on_hold').length;

    return {
      totalNeeds,
      completedNeeds,
      inProgressNeeds,
      identifiedNeeds,
      approvedNeeds,
      onHoldNeeds,
      completionRate: totalNeeds > 0 ? (completedNeeds / totalNeeds) * 100 : 0,
      progressRate: totalNeeds > 0 ? ((completedNeeds + inProgressNeeds) / totalNeeds) * 100 : 0
    };
  }, [state.trainingNeeds]);

  // Calculate training program metrics
  const programMetrics = useMemo(() => {
    const totalPrograms = state.trainingPrograms.length;
    const activePrograms = state.trainingPrograms.filter(p => p.status === 'active').length;
    const completedPrograms = state.trainingPrograms.filter(p => p.status === 'completed').length;
    const totalBudget = state.trainingPrograms.reduce((sum, p) => sum + (p.costPerPerson || 0), 0);

    return {
      totalPrograms,
      activePrograms,
      completedPrograms,
      totalBudget,
      avgCostPerProgram: totalPrograms > 0 ? totalBudget / totalPrograms : 0
    };
  }, [state.trainingPrograms]);

  // Notify stakeholders (mock implementation)
  const notifyStakeholders = useCallback(async (needId: string, message: string) => {
    setLoading(true);
    // In a real implementation, this would send emails/notifications
    await new Promise(resolve => setTimeout(resolve, 1000));
    console.log(`Notification sent for training need ${needId}: ${message}`);
    setLoading(false);
  }, []);

  // Get training needs by status
  const getNeedsByStatus = useCallback((status: string) => {
    return state.trainingNeeds.filter(need => need.status === status);
  }, [state.trainingNeeds]);

  // Get high priority training needs
  const getHighPriorityNeeds = useCallback(() => {
    return state.trainingNeeds.filter(need => 
      need.priority === 'urgent' || need.priority === 'high'
    );
  }, [state.trainingNeeds]);

  // Calculate department-wise metrics
  const departmentMetrics = useMemo(() => {
    const deptMap = new Map<string, { total: number; completed: number; budget: number }>();
    
    state.trainingNeeds.forEach(need => {
      const current = deptMap.get(need.department) || { total: 0, completed: 0, budget: 0 };
      current.total++;
      if (need.status === 'completed') current.completed++;
      current.budget += need.estimatedCost;
      deptMap.set(need.department, current);
    });

    return Array.from(deptMap.entries()).map(([department, stats]) => ({
      department,
      ...stats,
      completionRate: stats.total > 0 ? (stats.completed / stats.total) * 100 : 0
    }));
  }, [state.trainingNeeds]);

  return {
    metrics,
    programMetrics,
    departmentMetrics,
    loading,
    notifyStakeholders,
    getNeedsByStatus,
    getHighPriorityNeeds
  };
};
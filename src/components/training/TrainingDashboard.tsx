import React, { useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { TrainingProvider, useTraining, trainingActions } from '@/contexts/TrainingContext';
import { 
  BarChart3,
  TrendingUp,
  Users,
  Calendar,
  DollarSign,
  Target,
  Award,
  Clock,
  CheckCircle,
  AlertCircle,
  BookOpen,
  PieChart,
  Activity,
  Download,
  Upload,
  Filter,
  Search,
  Plus,
  FileSpreadsheet
} from 'lucide-react';
import { motion } from 'framer-motion';
import { SkillsGapAnalysis } from './analysis/SkillsGapAnalysis';
import { TrainingNeedsCSVImport } from './data/TrainingNeedsCSVImport';
import LDProfessionalTNA from '@/components/training/analysis/LDProfessionalTNA';

// Import types from centralized location
// DashboardTrainingProgram and LearningPath types removed - no longer needed

const TrainingDashboardContent: React.FC = () => {
  const { state, dispatch } = useTraining();
  const [activeTab, setActiveTab] = useState('overview');
  const metrics = state.metrics;
  const { loading, error } = state;

  // Error handling component
  const ErrorMessage: React.FC<{ message: string }> = ({ message }) => (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      className="mb-4 rounded-lg border border-destructive/50 bg-destructive/10 p-3 text-sm text-destructive"
    >
      <div className="flex items-center gap-2">
        <AlertCircle className="h-4 w-4" />
        {message}
      </div>
    </motion.div>
  );

  // Loading component
  const LoadingSpinner: React.FC = () => (
    <div className="flex items-center justify-center py-8">
      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
    </div>
  );


  // Import handlers for CSV data with context integration
  const handleImportTrainingNeeds = async (needs: any[]) => {
    try {
      dispatch(trainingActions.setLoading(true));
      dispatch(trainingActions.setError(null));
      
      // Convert imported needs to context format
      const contextNeeds = needs.map(need => ({
        ...need,
        id: need.id || Date.now().toString() + Math.random(),
        createdAt: new Date(),
        updatedAt: new Date(),
        status: 'identified' as const
      }));
      
      // Add to context state
      contextNeeds.forEach(need => {
        dispatch(trainingActions.addTrainingNeed(need));
      });
      
      // Update metrics
      dispatch(trainingActions.updateMetrics({
        totalEmployees: metrics.totalEmployees + needs.length,
        upcomingSessions: metrics.upcomingSessions + Math.floor(needs.length / 2),
        highPriorityNeeds: metrics.highPriorityNeeds + needs.filter(n => n.priority === 'high' || n.priority === 'urgent').length
      }));
      
      return Promise.resolve();
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to import training needs';
      dispatch(trainingActions.setError(errorMessage));
      return Promise.reject(error);
    } finally {
      dispatch(trainingActions.setLoading(false));
    }
  };

  const handleImportSkillsAssessment = async (skills: any[]) => {
    try {
      dispatch(trainingActions.setLoading(true));
      dispatch(trainingActions.setError(null));
      
      // Convert and add skill assessments
      const contextSkills = skills.map(skill => ({
        ...skill,
        id: skill.id || Date.now().toString() + Math.random(),
        gapSize: skill.requiredLevel - skill.currentLevel,
        assessmentDate: new Date(skill.assessmentDate || Date.now())
      }));
      
      dispatch(trainingActions.setSkillAssessments(contextSkills));
      
      return Promise.resolve();
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to import skills assessment';
      dispatch(trainingActions.setError(errorMessage));
      return Promise.reject(error);
    } finally {
      dispatch(trainingActions.setLoading(false));
    }
  };

  const handleImportTrainingPrograms = async (programs: any[]) => {
    try {
      dispatch(trainingActions.setLoading(true));
      dispatch(trainingActions.setError(null));
      
      // Convert and add training programs
      const contextPrograms = programs.map(program => ({
        ...program,
        id: program.id || Date.now().toString() + Math.random(),
        status: 'active' as const,
        createdAt: new Date(),
        updatedAt: new Date()
      }));
      
      dispatch(trainingActions.setTrainingPrograms(contextPrograms));
      
      return Promise.resolve();
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to import training programs';
      dispatch(trainingActions.setError(errorMessage));
      return Promise.reject(error);
    } finally {
      dispatch(trainingActions.setLoading(false));
    }
  };

  const showToast = (message: string, type: 'default' | 'success' | 'error') => {
    // Clear previous errors if success
    if (type === 'success' && error) {
      dispatch(trainingActions.setError(null));
    }
    
    // Set error state if error type
    if (type === 'error') {
      dispatch(trainingActions.setError(message));
    }
    
    // Log for debugging
    console.log(`${type.toUpperCase()}: ${message}`);
    
    // In a real implementation, you would integrate with react-hot-toast or similar library
    // toast[type](message);
  };



  const renderMetricCard = (
    title: string,
    value: string | number,
    icon: React.ReactNode,
    trend?: number,
    suffix?: string
  ) => (
    <Card className="p-4">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm text-muted-foreground">{title}</p>
          <p className="text-2xl font-bold">
            {value}{suffix}
          </p>
          {trend !== undefined && (
            <div className="flex items-center gap-1 mt-1">
              {trend > 0 ? (
                <TrendingUp className="h-4 w-4 text-green-500" />
              ) : (
                <TrendingUp className="h-4 w-4 text-red-500 rotate-180" />
              )}
              <span className={`text-sm ${trend > 0 ? 'text-green-500' : 'text-red-500'}`}>
                {Math.abs(trend)}%
              </span>
            </div>
          )}
        </div>
        <div className="text-muted-foreground">
          {icon}
        </div>
      </div>
    </Card>
  );

  return (
    <div className="h-full overflow-y-auto">
      <div className="container mx-auto p-6">
        <motion.div 
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold tracking-tight flex items-center gap-3">
                <Target className="h-8 w-8" />
                Training Management Dashboard
              </h1>
              <p className="mt-1 text-sm text-muted-foreground">
                Comprehensive overview of training programs and progress
              </p>
            </div>
            <div className="flex gap-2">
              <Button variant="outline" size="sm">
                <Upload className="h-4 w-4 mr-2" />
                Import Data
              </Button>
              <Button variant="outline" size="sm">
                <Download className="h-4 w-4 mr-2" />
                Export Report
              </Button>
              <Button size="sm">
                <Filter className="h-4 w-4 mr-2" />
                Filters
              </Button>
            </div>
          </div>
        </motion.div>

        {/* Error Display */}
        {error && <ErrorMessage message={error} />}

        {/* Loading State */}
        {loading ? (
          <LoadingSpinner />
        ) : (
          <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-4 gap-1">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="skillsGap">Skills Gap</TabsTrigger>
            <TabsTrigger value="import">Import Data</TabsTrigger>
            <TabsTrigger value="ld-professional">L&D Professional</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {renderMetricCard(
                'Total Employees',
                metrics.totalEmployees,
                <Users className="h-8 w-8" />,
                5
              )}
              {renderMetricCard(
                'In Training',
                metrics.employeesInTraining,
                <BookOpen className="h-8 w-8" />,
                12
              )}
              {renderMetricCard(
                'Completed This Month',
                metrics.completedThisMonth,
                <CheckCircle className="h-8 w-8" />,
                8
              )}
              {renderMetricCard(
                'Upcoming Sessions',
                metrics.upcomingSessions,
                <Calendar className="h-8 w-8" />
              )}
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card className="p-6">
                <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                  <DollarSign className="h-5 w-5" />
                  Budget Utilization
                </h3>
                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between text-sm mb-2">
                      <span>Utilized</span>
                      <span className="font-medium">
                        ${metrics.budgetUtilized.toLocaleString()} / ${metrics.totalBudgetAllocated.toLocaleString()}
                      </span>
                    </div>
                    <Progress 
                      value={(metrics.budgetUtilized / metrics.totalBudgetAllocated) * 100} 
                      className="h-3"
                    />
                  </div>
                  <div className="grid grid-cols-2 gap-4 pt-2">
                    <div>
                      <p className="text-sm text-muted-foreground">Remaining Budget</p>
                      <p className="text-xl font-bold text-green-500">
                        ${(metrics.totalBudgetAllocated - metrics.budgetUtilized).toLocaleString()}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Average ROI</p>
                      <p className="text-xl font-bold text-primary">
                        {metrics.averageROI}%
                      </p>
                    </div>
                  </div>
                </div>
              </Card>

              <Card className="p-6">
                <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                  <Activity className="h-5 w-5" />
                  Training Activity
                </h3>
                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between text-sm mb-2">
                      <span>Average Completion Rate</span>
                      <span className="font-medium">{metrics.averageCompletionRate}%</span>
                    </div>
                    <Progress value={metrics.averageCompletionRate} className="h-3" />
                  </div>
                  <div className="space-y-2">
                    {['Technical', 'Leadership', 'Compliance', 'Soft Skills'].map((category, index) => {
                      const values = [65, 78, 92, 71];
                      return (
                        <div key={category} className="flex items-center justify-between">
                          <span className="text-sm">{category}</span>
                          <div className="flex items-center gap-2">
                            <Progress value={values[index]} className="w-24 h-2" />
                            <span className="text-sm font-medium w-10">{values[index]}%</span>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              </Card>
            </div>

          </TabsContent>


          <TabsContent value="skillsGap">
            <SkillsGapAnalysis />
          </TabsContent>


          <TabsContent value="import">
            <div className="space-y-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-semibold">Import Training Data</h2>
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <FileSpreadsheet className="h-4 w-4" />
                  <span>Support for CSV, XLS, XLSX formats</span>
                </div>
              </div>
              
              <TrainingNeedsCSVImport
                onImportTrainingNeeds={handleImportTrainingNeeds}
                onImportSkillsAssessment={handleImportSkillsAssessment}
                onImportTrainingPrograms={handleImportTrainingPrograms}
                showToast={showToast}
              />
              
              <div className="mt-6 p-4 bg-muted/30 rounded-lg">
                <h3 className="text-sm font-medium mb-2">Import Guidelines</h3>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• <strong>Training Needs:</strong> Import employee training requirements with skill gaps and priorities</li>
                  <li>• <strong>Skills Assessment:</strong> Import current skill levels and target requirements for gap analysis</li>
                  <li>• <strong>Training Programs:</strong> Import available training courses and their details</li>
                  <li>• Use the download template feature to get correctly formatted CSV files</li>
                  <li>• All required fields must be filled for successful import</li>
                  <li>• Large files (1000+ rows) are processed in batches for optimal performance</li>
                </ul>
              </div>
            </div>
          </TabsContent>


          <TabsContent value="ld-professional">
            <LDProfessionalTNA />
          </TabsContent>

        </Tabs>
        )}
      </div>
    </div>
  );
};

// Main component with context provider
export const TrainingDashboard: React.FC = () => {
  return (
    <TrainingProvider>
      <TrainingDashboardContent />
    </TrainingProvider>
  );
};

export default TrainingDashboard;
# Training Components

## Overview
This directory contains all training-related components for the Learning & Development system. The components are organized by functionality to promote reusability and maintainability.

## Directory Structure

```
training/
├── analysis/           # Training needs analysis components
├── data/              # Data import/export components
├── hooks/             # Custom React hooks
├── types/             # TypeScript type definitions
├── utils/             # Utility functions
├── visualizations/    # Charts and data visualizations
└── TrainingDashboard.tsx  # Main dashboard component
```

## Key Components

### Analysis
- **LDProfessionalTNA**: Professional L&D focused analysis
- **SkillsGapAnalysis**: Employee skill gap assessment

### Data Management
- **TrainingNeedsCSVImport**: CSV import functionality for training needs data

### Visualizations
- **SkillsGapChart**: Visual representation of skill gaps

## Custom Hooks

### useTrainingAnalysis
Provides training analysis functionality including filtering, metrics, and bulk operations.

```typescript
const {
  filteredNeeds,
  metrics,
  bulkApprove,
  exportToCSV
} = useTrainingAnalysis();
```

### useTrainingWorkflow
Provides training workflow metrics and utilities.

```typescript
const {
  metrics,
  programMetrics,
  departmentMetrics,
  notifyStakeholders,
  getNeedsByStatus,
  getHighPriorityNeeds
} = useTrainingWorkflow();
```

### useTrainingMetrics
Calculates and provides training metrics and analytics.

```typescript
const {
  departmentMetrics,
  budgetMetrics,
  roiProjections
} = useTrainingMetrics();
```

## Usage Examples

### L&D Professional Analysis
```tsx
import { LDProfessionalTNA } from '@/components/training/analysis';

function MyComponent() {
  return <LDProfessionalTNA />;
}
```

### Using Custom Hooks
```tsx
import { useTrainingAnalysis } from '@/components/training/hooks';

function AnalysisComponent() {
  const { filteredNeeds, metrics } = useTrainingAnalysis();
  
  return (
    <div>
      <p>Total Needs: {metrics.totalNeeds}</p>
      <p>Budget Required: {metrics.estimatedBudget}</p>
    </div>
  );
}
```

### Skills Gap Analysis
```tsx
import { SkillsGapAnalysis } from '@/components/training/analysis';

function GapAnalysisComponent() {
  return <SkillsGapAnalysis />;
}
```

## Type Safety

All components use TypeScript for type safety. Core types are defined in:
- `@/contexts/TrainingContext` - Context types
- `./types/index.ts` - Component-specific types

## Best Practices

1. **Import from index files** - Use the index exports for cleaner imports
2. **Use custom hooks** - Leverage hooks for business logic
3. **Type safety** - Always provide proper TypeScript types
4. **Validation** - Use validators from utils for data validation
5. **Formatting** - Use formatters for consistent data display

## Development

### Adding New Components
1. Create component in appropriate subfolder
2. Add exports to subfolder's index.ts
3. Update main index.ts if needed
4. Add TypeScript types to types/index.ts
5. Document usage in this README

### Testing
Components should have corresponding test files in `__tests__` folders:
- Unit tests for utils and hooks
- Integration tests for workflows
- Component tests for UI components

## Dependencies
- React 18+
- TypeScript
- Recharts (for visualizations)
- Framer Motion (for animations)
- Custom UI components from @/components/ui

## Contributing
When contributing to training components:
1. Follow the existing folder structure
2. Maintain TypeScript type safety
3. Add proper documentation
4. Include tests for new functionality
5. Update this README as needed
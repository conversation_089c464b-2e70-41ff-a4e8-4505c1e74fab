// Workflow exports removed - no workflow components remain

// Skills Gap Analysis
export { SkillsGapAnalysis } from './analysis/SkillsGapAnalysis';
// Types Skill, Employee, GapAnalysisResult are exported from ./types

// Dashboard exports
export { TrainingDashboard } from './TrainingDashboard';
export { default as TrainingDashboardDefault } from './TrainingDashboard';

// Form exports removed - forms folder deleted

// Data import/export
export { TrainingNeedsCSVImport } from './data/TrainingNeedsCSVImport';

// Analysis components
export * from './analysis';

// Types
export * from './types';

// Hooks
export * from './hooks';

// Utils
export * from './utils';

// Visualizations
export * from './visualizations';
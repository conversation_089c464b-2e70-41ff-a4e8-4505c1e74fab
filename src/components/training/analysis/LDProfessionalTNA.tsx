import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Textarea } from '@/components/ui/textarea';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
// LearningPath type import removed - type no longer exists
import { 
  BookOpen,
  Brain,
  BarChart3,
  Users,
  Target,
  Award,
  Lightbulb,
  TrendingUp,
  FileText,
  MessageSquare,
  Calendar,
  Settings,
  Download,
  Upload,
  Plus,
  ChevronRight,
  ChevronDown,
  CheckCircle,
  AlertCircle,
  Star,
  Zap,
  Eye,
  Edit,
  Share,
  Filter,
  Search,
  Layers,
  <PERSON><PERSON>Bran<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>hart,
  Activity,
  Clock,
  DollarSign,
  Percent,
  ThumbsUp,
  GraduationCap
} from 'lucide-react';

// Professional L&D Interfaces
interface CompetencyModel {
  id: string;
  name: string;
  description: string;
  category: 'technical' | 'behavioral' | 'leadership' | 'compliance';
  levels: CompetencyLevel[];
  businessCriticality: 'critical' | 'important' | 'beneficial';
  industryStandard?: string;
}

interface CompetencyLevel {
  level: number;
  name: string;
  description: string;
  behaviors: string[];
  assessmentCriteria: string[];
  developmentActivities: string[];
}

interface SkillMatrix {
  employeeId: string;
  employeeName: string;
  role: string;
  department: string;
  competencies: {
    competencyId: string;
    currentLevel: number;
    targetLevel: number;
    gap: number;
    priority: 'critical' | 'high' | 'medium' | 'low';
    assessmentMethod: 'self' | 'manager' | '360' | 'observation' | 'test';
    lastAssessed: Date;
    assessorName: string;
  }[];
  overallReadiness: number;
  developmentPlan?: any; // LearningPath type removed, consider creating local type
}

// Local LearningModule interface for this component
interface LearningModule {
  id: string;
  name: string;
  type: 'course' | 'workshop' | 'mentoring' | 'project' | 'assessment';
  format: 'online' | 'classroom' | 'blended' | 'experiential';
  duration: number;
  provider?: string;
  cost: number;
  competenciesAddressed: string[];
  prerequisites: string[];
  learningObjectives: string[];
  assessmentMethod: string;
  effectivenessRating: number;
}

interface ADDIEProject {
  id: string;
  name: string;
  description: string;
  currentPhase: 'analysis' | 'design' | 'development' | 'implementation' | 'evaluation';
  phases: {
    analysis: ADDIEAnalysisData;
    design: ADDIEDesignData;
    development: ADDIEDevelopmentData;
    implementation: ADDIEImplementationData;
    evaluation: ADDIEEvaluationData;
  };
  timeline: {
    startDate: Date;
    endDate: Date;
    milestones: { phase: string; date: Date; status: 'completed' | 'in_progress' | 'planned' }[];
  };
  stakeholders: {
    projectManager: string;
    instructionalDesigner: string;
    subjectMatterExperts: string[];
    businessSponsor: string;
    targetAudience: string;
  };
  budget: {
    total: number;
    allocated: number;
    spent: number;
  };
  riskAssessment: {
    level: 'low' | 'medium' | 'high';
    factors: string[];
    mitigation: string[];
  };
}

interface ADDIEAnalysisData {
  businessNeed: string;
  performanceGap: string;
  targetAudience: {
    size: number;
    demographics: string;
    currentSkillLevel: string;
    learningPreferences: string;
    constraints: string[];
  };
  goalAnalysis: {
    businessGoals: string[];
    learningGoals: string[];
    performanceGoals: string[];
  };
  contextualFactors: {
    organizational: string[];
    environmental: string[];
    technological: string[];
  };
  successCriteria: string[];
}

interface ADDIEDesignData {
  learningObjectives: string[];
  assessmentStrategy: string;
  contentStructure: string[];
  deliveryMethod: string;
  mediaSelection: string[];
  interactionDesign: string;
  accessibility: string[];
  timeline: string;
}

interface ADDIEDevelopmentData {
  contentCreation: {
    modules: string[];
    assessments: string[];
    resources: string[];
  };
  qualityAssurance: {
    reviews: string[];
    testing: string[];
    revisions: string[];
  };
  pilotTesting: {
    participants: number;
    feedback: string[];
    improvements: string[];
  };
}

interface ADDIEImplementationData {
  rolloutPlan: string;
  communicationStrategy: string;
  supportResources: string[];
  facilitatorTraining: string;
  technologySetup: string;
  changeManagement: string[];
}

interface ADDIEEvaluationData {
  kirkpatrickLevels: {
    reaction: KirkpatrickReaction;
    learning: KirkpatrickLearning;
    behavior: KirkpatrickBehavior;
    results: KirkpatrickResults;
  };
  roi: {
    investment: number;
    benefits: number;
    roi: number;
    paybackPeriod: number;
  };
}

interface KirkpatrickReaction {
  satisfactionScore: number;
  engagementLevel: number;
  relevanceRating: number;
  completionRate: number;
  feedback: string[];
}

interface KirkpatrickLearning {
  knowledgeAcquisition: number;
  skillDemonstration: number;
  attitudeChange: number;
  confidenceLevel: number;
  assessmentScores: number[];
}

interface KirkpatrickBehavior {
  behaviorChange: number;
  applicationRate: number;
  performanceImprovement: number;
  timeToApplication: number;
  barriers: string[];
  enablers: string[];
}

interface KirkpatrickResults {
  businessImpact: number;
  kpiImprovement: { metric: string; before: number; after: number; improvement: number }[];
  costReduction: number;
  revenueIncrease: number;
  qualityImprovement: number;
}

export const LDProfessionalTNA: React.FC = () => {
  const [activeView, setActiveView] = useState<'dashboard' | 'addie' | 'competency' | 'analytics' | 'library' | 'collaboration'>('dashboard');
  const [currentProject, setCurrentProject] = useState<ADDIEProject | null>(null);
  const [skillMatrices, setSkillMatrices] = useState<SkillMatrix[]>([]);
  const [competencyModels, setCompetencyModels] = useState<CompetencyModel[]>([]);
  const [learningPaths, setLearningPaths] = useState<any[]>([]);

  // Mock data initialization
  useEffect(() => {
    // Initialize with sample data
    const sampleCompetencies: CompetencyModel[] = [
      {
        id: '1',
        name: 'Technical Leadership',
        description: 'Leading technical teams and making architectural decisions',
        category: 'leadership',
        businessCriticality: 'critical',
        industryStandard: 'IEEE Software Engineering Competency Model',
        levels: [
          {
            level: 1,
            name: 'Emerging',
            description: 'Basic understanding of technical leadership principles',
            behaviors: ['Participates in technical discussions', 'Follows established practices'],
            assessmentCriteria: ['Can explain basic concepts', 'Demonstrates awareness'],
            developmentActivities: ['Technical leadership course', 'Mentoring program']
          },
          {
            level: 2,
            name: 'Developing',
            description: 'Demonstrates technical leadership in small teams',
            behaviors: ['Leads small technical initiatives', 'Mentors junior members'],
            assessmentCriteria: ['Successfully leads projects', 'Positive peer feedback'],
            developmentActivities: ['Advanced leadership workshop', 'Cross-functional projects']
          }
        ]
      }
    ];
    setCompetencyModels(sampleCompetencies);
  }, []);

  // Professional Dashboard View
  const renderDashboard = () => (
    <div className="space-y-6">
      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted-foreground">Active Projects</p>
              <p className="text-2xl font-bold">12</p>
            </div>
            <BookOpen className="h-8 w-8 text-primary" />
          </div>
          <div className="mt-2">
            <Badge variant="secondary" className="text-xs">3 in Analysis</Badge>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted-foreground">Skill Gaps Identified</p>
              <p className="text-2xl font-bold">47</p>
            </div>
            <Target className="h-8 w-8 text-orange-500" />
          </div>
          <div className="mt-2">
            <Badge variant="destructive" className="text-xs">18 Critical</Badge>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted-foreground">Learning Paths</p>
              <p className="text-2xl font-bold">24</p>
            </div>
            <GitBranch className="h-8 w-8 text-blue-500" />
          </div>
          <div className="mt-2">
            <Badge variant="default" className="text-xs">8 New</Badge>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted-foreground">Avg. Training ROI</p>
              <p className="text-2xl font-bold">234%</p>
            </div>
            <TrendingUp className="h-8 w-8 text-green-500" />
          </div>
          <div className="mt-2">
            <Badge variant="outline" className="text-xs">+18% vs Q3</Badge>
          </div>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4">Quick Actions</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <Button variant="outline" className="h-auto p-4" onClick={() => setActiveView('addie')}>
            <div className="flex flex-col items-center gap-2">
              <BookOpen className="h-6 w-6" />
              <span className="text-sm">New ADDIE Project</span>
            </div>
          </Button>
          <Button variant="outline" className="h-auto p-4" onClick={() => setActiveView('competency')}>
            <div className="flex flex-col items-center gap-2">
              <Brain className="h-6 w-6" />
              <span className="text-sm">Skills Assessment</span>
            </div>
          </Button>
          <Button variant="outline" className="h-auto p-4" onClick={() => setActiveView('analytics')}>
            <div className="flex flex-col items-center gap-2">
              <BarChart3 className="h-6 w-6" />
              <span className="text-sm">Training Analytics</span>
            </div>
          </Button>
          <Button variant="outline" className="h-auto p-4" onClick={() => setActiveView('library')}>
            <div className="flex flex-col items-center gap-2">
              <FileText className="h-6 w-6" />
              <span className="text-sm">Template Library</span>
            </div>
          </Button>
        </div>
      </Card>

      {/* Recent Projects */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold">Recent ADDIE Projects</h3>
          <Button size="sm" onClick={() => setActiveView('addie')}>
            <Plus className="h-4 w-4 mr-2" />
            New Project
          </Button>
        </div>
        <div className="space-y-3">
          {[
            { name: 'Leadership Development Program', phase: 'Implementation', progress: 75, risk: 'low' },
            { name: 'Technical Skills Assessment', phase: 'Design', progress: 45, risk: 'medium' },
            { name: 'Onboarding Process Redesign', phase: 'Analysis', progress: 25, risk: 'low' },
          ].map((project, index) => (
            <div key={index} className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
              <div className="flex-1">
                <h4 className="font-medium">{project.name}</h4>
                <div className="flex items-center gap-2 mt-1">
                  <Badge variant="outline" className="text-xs">{project.phase}</Badge>
                  <Badge variant={project.risk === 'low' ? 'secondary' : project.risk === 'medium' ? 'default' : 'destructive'} className="text-xs">
                    {project.risk} risk
                  </Badge>
                </div>
              </div>
              <div className="text-right">
                <div className="text-sm font-medium">{project.progress}%</div>
                <Progress value={project.progress} className="w-20 h-2 mt-1" />
              </div>
            </div>
          ))}
        </div>
      </Card>

      {/* Skills Gap Heatmap */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4">Skills Gap Heatmap by Department</h3>
        <div className="grid grid-cols-5 gap-2">
          {/* Mock heatmap data */}
          {Array.from({ length: 25 }, (_, i) => {
            const intensity = Math.floor(Math.random() * 4) + 1;
            return (
              <div
                key={i}
                className={`aspect-square rounded flex items-center justify-center text-xs font-medium text-white ${
                  intensity === 1 ? 'bg-green-500' :
                  intensity === 2 ? 'bg-yellow-500' :
                  intensity === 3 ? 'bg-orange-500' : 'bg-red-500'
                }`}
              >
                {intensity}
              </div>
            );
          })}
        </div>
        <div className="flex items-center justify-between mt-4 text-sm text-muted-foreground">
          <span>Low Gap</span>
          <span>High Gap</span>
        </div>
      </Card>
    </div>
  );

  // ADDIE Framework View
  const renderADDIEView = () => (
    <div className="space-y-6">
      <Card className="p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-2xl font-bold">ADDIE Framework</h2>
            <p className="text-muted-foreground">Analysis, Design, Development, Implementation, Evaluation</p>
          </div>
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            New ADDIE Project
          </Button>
        </div>

        {/* ADDIE Phases Navigation */}
        <div className="flex items-center gap-2 mb-6 overflow-x-auto">
          {[
            { id: 'analysis', name: 'Analysis', icon: Brain, color: 'bg-blue-500' },
            { id: 'design', name: 'Design', icon: Lightbulb, color: 'bg-purple-500' },
            { id: 'development', name: 'Development', icon: Settings, color: 'bg-green-500' },
            { id: 'implementation', name: 'Implementation', icon: Zap, color: 'bg-orange-500' },
            { id: 'evaluation', name: 'Evaluation', icon: BarChart3, color: 'bg-red-500' }
          ].map((phase, index) => (
            <div key={phase.id} className="flex items-center">
              <div className={`flex items-center gap-2 px-4 py-2 rounded-lg text-white ${phase.color}`}>
                <phase.icon className="h-4 w-4" />
                <span className="text-sm font-medium whitespace-nowrap">{phase.name}</span>
              </div>
              {index < 4 && <ChevronRight className="h-4 w-4 text-muted-foreground mx-2" />}
            </div>
          ))}
        </div>

        <Tabs value="analysis" className="w-full">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="analysis">Analysis</TabsTrigger>
            <TabsTrigger value="design">Design</TabsTrigger>
            <TabsTrigger value="development">Development</TabsTrigger>
            <TabsTrigger value="implementation">Implementation</TabsTrigger>
            <TabsTrigger value="evaluation">Evaluation</TabsTrigger>
          </TabsList>

          <TabsContent value="analysis" className="space-y-4">
            <Card className="p-4">
              <h3 className="font-semibold mb-3">Business Need Analysis</h3>
              <div className="space-y-4">
                <div>
                  <Label>Performance Gap Description</Label>
                  <Textarea placeholder="Describe the current performance gap and business impact..." rows={3} />
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label>Target Audience Size</Label>
                    <Input type="number" placeholder="Number of learners" />
                  </div>
                  <div>
                    <Label>Current Skill Level</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="Select level" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="beginner">Beginner</SelectItem>
                        <SelectItem value="intermediate">Intermediate</SelectItem>
                        <SelectItem value="advanced">Advanced</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>
            </Card>

            <Card className="p-4">
              <h3 className="font-semibold mb-3">Goal Analysis Framework</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <Label>Business Goals</Label>
                  <Textarea placeholder="List business objectives..." rows={4} />
                </div>
                <div>
                  <Label>Learning Goals</Label>
                  <Textarea placeholder="List learning objectives..." rows={4} />
                </div>
                <div>
                  <Label>Performance Goals</Label>
                  <Textarea placeholder="List performance targets..." rows={4} />
                </div>
              </div>
            </Card>
          </TabsContent>

          <TabsContent value="design" className="space-y-4">
            <Card className="p-4">
              <h3 className="font-semibold mb-3">Instructional Design Strategy</h3>
              <div className="space-y-4">
                <div>
                  <Label>Learning Objectives (SMART)</Label>
                  <Textarea placeholder="Define specific, measurable, achievable, relevant, time-bound objectives..." rows={4} />
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label>Delivery Method</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="Select delivery method" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="elearning">E-Learning</SelectItem>
                        <SelectItem value="classroom">Classroom</SelectItem>
                        <SelectItem value="blended">Blended</SelectItem>
                        <SelectItem value="microlearning">Microlearning</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label>Assessment Strategy</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="Select assessment type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="formative">Formative</SelectItem>
                        <SelectItem value="summative">Summative</SelectItem>
                        <SelectItem value="both">Both</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>
            </Card>
          </TabsContent>

          <TabsContent value="evaluation" className="space-y-4">
            <Card className="p-4">
              <h3 className="font-semibold mb-3">Kirkpatrick Four-Level Evaluation</h3>
              <div className="space-y-4">
                {[
                  { level: 'Level 1', name: 'Reaction', description: 'Learner satisfaction and engagement', icon: ThumbsUp },
                  { level: 'Level 2', name: 'Learning', description: 'Knowledge, skills, and attitude acquisition', icon: Brain },
                  { level: 'Level 3', name: 'Behavior', description: 'Application of learning on the job', icon: Activity },
                  { level: 'Level 4', name: 'Results', description: 'Business impact and ROI', icon: TrendingUp }
                ].map((level) => (
                  <Card key={level.level} className="p-4">
                    <div className="flex items-start gap-3">
                      <level.icon className="h-5 w-5 text-primary mt-1" />
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <h4 className="font-medium">{level.level}: {level.name}</h4>
                          <Badge variant="outline">{level.level}</Badge>
                        </div>
                        <p className="text-sm text-muted-foreground mb-3">{level.description}</p>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <Label className="text-xs">Measurement Method</Label>
                            <Input placeholder="How will you measure this?" />
                          </div>
                          <div>
                            <Label className="text-xs">Target Metric</Label>
                            <Input placeholder="What's the target value?" />
                          </div>
                        </div>
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            </Card>
          </TabsContent>
        </Tabs>
      </Card>
    </div>
  );

  // Competency Mapping View
  const renderCompetencyView = () => (
    <div className="space-y-6">
      <Card className="p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-2xl font-bold">Competency Mapping & Skills Matrix</h2>
            <p className="text-muted-foreground">Define competencies and assess skill levels across your organization</p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export Matrix
            </Button>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              New Competency Model
            </Button>
          </div>
        </div>

        <Tabs value="models" className="w-full">
          <TabsList>
            <TabsTrigger value="models">Competency Models</TabsTrigger>
            <TabsTrigger value="matrix">Skills Matrix</TabsTrigger>
            <TabsTrigger value="assessment">Assessment Tools</TabsTrigger>
          </TabsList>

          <TabsContent value="models" className="space-y-4">
            {competencyModels.map((model) => (
              <Card key={model.id} className="p-4">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <h3 className="font-semibold">{model.name}</h3>
                      <Badge variant={model.businessCriticality === 'critical' ? 'destructive' : 'default'}>
                        {model.businessCriticality}
                      </Badge>
                      <Badge variant="outline">{model.category}</Badge>
                    </div>
                    <p className="text-sm text-muted-foreground mb-3">{model.description}</p>
                    {model.industryStandard && (
                      <p className="text-xs text-muted-foreground">Based on: {model.industryStandard}</p>
                    )}
                  </div>
                  <Button variant="ghost" size="sm">
                    <Edit className="h-4 w-4" />
                  </Button>
                </div>

                <div className="mt-4">
                  <h4 className="text-sm font-medium mb-2">Proficiency Levels</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                    {model.levels.map((level) => (
                      <div key={level.level} className="flex items-center gap-2 p-2 bg-muted/50 rounded text-sm">
                        <span className="font-medium">L{level.level}:</span>
                        <span>{level.name}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </Card>
            ))}
          </TabsContent>

          <TabsContent value="matrix" className="space-y-4">
            <Card className="p-4">
              <div className="flex items-center justify-between mb-4">
                <h3 className="font-semibold">Skills Assessment Matrix</h3>
                <div className="flex gap-2">
                  <Button variant="outline" size="sm">
                    <Filter className="h-4 w-4 mr-2" />
                    Filter
                  </Button>
                  <Button variant="outline" size="sm">
                    <Upload className="h-4 w-4 mr-2" />
                    Import Assessments
                  </Button>
                </div>
              </div>

              {/* Skills Matrix Table */}
              <div className="overflow-x-auto">
                <table className="w-full text-sm">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left p-2">Employee</th>
                      <th className="text-left p-2">Role</th>
                      <th className="text-left p-2">Department</th>
                      <th className="text-left p-2">Technical Leadership</th>
                      <th className="text-left p-2">Communication</th>
                      <th className="text-left p-2">Project Management</th>
                      <th className="text-left p-2">Overall Gap</th>
                      <th className="text-left p-2">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {[
                      { name: 'John Smith', role: 'Senior Dev', dept: 'Engineering', tech: 3, comm: 4, pm: 2, gap: 'High' },
                      { name: 'Sarah Johnson', role: 'Tech Lead', dept: 'Engineering', tech: 4, comm: 5, pm: 4, gap: 'Medium' },
                      { name: 'Mike Chen', role: 'Developer', dept: 'Engineering', tech: 2, comm: 3, pm: 1, gap: 'High' },
                    ].map((employee, index) => (
                      <tr key={index} className="border-b hover:bg-muted/50">
                        <td className="p-2 font-medium">{employee.name}</td>
                        <td className="p-2">{employee.role}</td>
                        <td className="p-2">{employee.dept}</td>
                        <td className="p-2">
                          <div className="flex items-center gap-1">
                            <span>{employee.tech}/5</span>
                            <Progress value={employee.tech * 20} className="w-12 h-2" />
                          </div>
                        </td>
                        <td className="p-2">
                          <div className="flex items-center gap-1">
                            <span>{employee.comm}/5</span>
                            <Progress value={employee.comm * 20} className="w-12 h-2" />
                          </div>
                        </td>
                        <td className="p-2">
                          <div className="flex items-center gap-1">
                            <span>{employee.pm}/5</span>
                            <Progress value={employee.pm * 20} className="w-12 h-2" />
                          </div>
                        </td>
                        <td className="p-2">
                          <Badge variant={employee.gap === 'High' ? 'destructive' : employee.gap === 'Medium' ? 'default' : 'secondary'}>
                            {employee.gap}
                          </Badge>
                        </td>
                        <td className="p-2">
                          <Button variant="ghost" size="sm">
                            <Eye className="h-4 w-4" />
                          </Button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </Card>
          </TabsContent>
        </Tabs>
      </Card>
    </div>
  );

  // Advanced Analytics View
  const renderAnalyticsView = () => (
    <div className="space-y-6">
      <Card className="p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-2xl font-bold">Training Analytics & ROI Dashboard</h2>
            <p className="text-muted-foreground">Advanced insights and performance metrics for your training programs</p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export Report
            </Button>
            <Button variant="outline">
              <Calendar className="h-4 w-4 mr-2" />
              Schedule Report
            </Button>
          </div>
        </div>

        {/* ROI Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <Card className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Total ROI</p>
                <p className="text-2xl font-bold text-green-600">234%</p>
              </div>
              <Percent className="h-8 w-8 text-green-600" />
            </div>
            <div className="mt-2 text-xs text-muted-foreground">
              +18% from last quarter
            </div>
          </Card>

          <Card className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Training Investment</p>
                <p className="text-2xl font-bold">$487K</p>
              </div>
              <DollarSign className="h-8 w-8 text-blue-600" />
            </div>
            <div className="mt-2 text-xs text-muted-foreground">
              73% of allocated budget
            </div>
          </Card>

          <Card className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Effectiveness Score</p>
                <p className="text-2xl font-bold">8.7</p>
              </div>
              <Star className="h-8 w-8 text-yellow-500" />
            </div>
            <div className="mt-2 text-xs text-muted-foreground">
              Out of 10 (Kirkpatrick L1-L2)
            </div>
          </Card>

          <Card className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Time to Proficiency</p>
                <p className="text-2xl font-bold">-23%</p>
              </div>
              <Clock className="h-8 w-8 text-purple-600" />
            </div>
            <div className="mt-2 text-xs text-muted-foreground">
              Reduction from baseline
            </div>
          </Card>
        </div>

        <Tabs value="kirkpatrick" className="w-full">
          <TabsList>
            <TabsTrigger value="kirkpatrick">Kirkpatrick Analysis</TabsTrigger>
            <TabsTrigger value="roi">ROI Deep Dive</TabsTrigger>
            <TabsTrigger value="effectiveness">Program Effectiveness</TabsTrigger>
            <TabsTrigger value="trends">Trends & Insights</TabsTrigger>
          </TabsList>

          <TabsContent value="kirkpatrick" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {[
                { level: 'L1: Reaction', score: 4.6, target: 4.0, color: 'blue' },
                { level: 'L2: Learning', score: 4.2, target: 4.0, color: 'green' },
                { level: 'L3: Behavior', score: 3.8, target: 4.0, color: 'orange' },
                { level: 'L4: Results', score: 4.1, target: 3.5, color: 'purple' }
              ].map((level) => (
                <Card key={level.level} className="p-4">
                  <h3 className="font-semibold text-sm mb-3">{level.level}</h3>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <span>Current</span>
                      <span className="font-medium">{level.score}/5</span>
                    </div>
                    <Progress value={level.score * 20} className="h-2" />
                    <div className="flex items-center justify-between text-xs text-muted-foreground">
                      <span>Target: {level.target}</span>
                      <span className={level.score >= level.target ? 'text-green-600' : 'text-orange-600'}>
                        {level.score >= level.target ? 'On Track' : 'Below Target'}
                      </span>
                    </div>
                  </div>
                </Card>
              ))}
            </div>

            <Card className="p-4">
              <h3 className="font-semibold mb-3">Kirkpatrick Level Trends</h3>
              <div className="h-64 bg-muted/20 rounded-lg flex items-center justify-center">
                <div className="text-center text-muted-foreground">
                  <LineChart className="h-12 w-12 mx-auto mb-2" />
                  <p>Interactive analytics chart would be rendered here</p>
                  <p className="text-sm">Showing trends across all four Kirkpatrick levels</p>
                </div>
              </div>
            </Card>
          </TabsContent>

          <TabsContent value="roi" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card className="p-4">
                <h3 className="font-semibold mb-3">ROI Calculation Breakdown</h3>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span>Training Investment</span>
                    <span className="font-medium">$487,000</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Productivity Gains</span>
                    <span className="font-medium text-green-600">$892,000</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Cost Savings</span>
                    <span className="font-medium text-green-600">$248,000</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Reduced Turnover</span>
                    <span className="font-medium text-green-600">$190,000</span>
                  </div>
                  <hr />
                  <div className="flex justify-between text-lg font-semibold">
                    <span>Total ROI</span>
                    <span className="text-green-600">234%</span>
                  </div>
                </div>
              </Card>

              <Card className="p-4">
                <h3 className="font-semibold mb-3">Payback Period Analysis</h3>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span>Investment Recovery</span>
                    <span className="font-medium">8.2 months</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Break-even Point</span>
                    <span className="font-medium">Month 9</span>
                  </div>
                  <div className="flex justify-between">
                    <span>NPV (12 months)</span>
                    <span className="font-medium text-green-600">$643,000</span>
                  </div>
                  <div className="flex justify-between">
                    <span>IRR</span>
                    <span className="font-medium">47.3%</span>
                  </div>
                </div>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </Card>
    </div>
  );

  // Template Library View
  const renderLibraryView = () => (
    <div className="space-y-6">
      <Card className="p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-2xl font-bold">Template Library & Best Practices</h2>
            <p className="text-muted-foreground">Industry-standard templates, methodologies, and best practices for L&D professionals</p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline">
              <Search className="h-4 w-4 mr-2" />
              Search Templates
            </Button>
            <Button>
              <Upload className="h-4 w-4 mr-2" />
              Upload Template
            </Button>
          </div>
        </div>

        <Tabs value="templates" className="w-full">
          <TabsList>
            <TabsTrigger value="templates">Templates</TabsTrigger>
            <TabsTrigger value="methodologies">Methodologies</TabsTrigger>
            <TabsTrigger value="assessments">Assessment Tools</TabsTrigger>
            <TabsTrigger value="frameworks">Frameworks</TabsTrigger>
          </TabsList>

          <TabsContent value="templates" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {[
                { 
                  name: 'ADDIE Project Template',
                  category: 'Instructional Design',
                  downloads: 1247,
                  rating: 4.8,
                  format: 'Excel',
                  description: 'Complete ADDIE framework project template with phases, deliverables, and timelines'
                },
                {
                  name: 'Skills Assessment Matrix',
                  category: 'Competency Mapping',
                  downloads: 892,
                  rating: 4.6,
                  format: 'Excel',
                  description: 'Comprehensive skills assessment template with competency levels and gap analysis'
                },
                {
                  name: 'Kirkpatrick Evaluation Form',
                  category: 'Training Evaluation',
                  downloads: 756,
                  rating: 4.7,
                  format: 'PDF',
                  description: 'Four-level evaluation framework with measurement tools and ROI calculation'
                },
                {
                  name: 'Learning Path Designer',
                  category: 'Curriculum Design',
                  downloads: 623,
                  rating: 4.5,
                  format: 'PowerPoint',
                  description: 'Visual learning path template with competency progression and milestones'
                },
                {
                  name: 'Training Needs Survey',
                  category: 'Needs Analysis',
                  downloads: 534,
                  rating: 4.4,
                  format: 'Word',
                  description: 'Comprehensive survey template for identifying training needs across roles'
                },
                {
                  name: 'SME Interview Guide',
                  category: 'Content Development',
                  downloads: 445,
                  rating: 4.6,
                  format: 'Word',
                  description: 'Structured interview guide for extracting knowledge from subject matter experts'
                }
              ].map((template, index) => (
                <Card key={index} className="p-4">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex-1">
                      <h3 className="font-semibold text-sm">{template.name}</h3>
                      <p className="text-xs text-muted-foreground mt-1">{template.category}</p>
                    </div>
                    <Badge variant="outline" className="text-xs">{template.format}</Badge>
                  </div>
                  
                  <p className="text-xs text-muted-foreground mb-3">{template.description}</p>
                  
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center gap-1">
                      <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                      <span className="text-xs">{template.rating}</span>
                    </div>
                    <span className="text-xs text-muted-foreground">{template.downloads} downloads</span>
                  </div>
                  
                  <div className="flex gap-2">
                    <Button size="sm" variant="outline" className="flex-1 text-xs">
                      <Eye className="h-3 w-3 mr-1" />
                      Preview
                    </Button>
                    <Button size="sm" className="flex-1 text-xs">
                      <Download className="h-3 w-3 mr-1" />
                      Download
                    </Button>
                  </div>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="methodologies" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {[
                {
                  name: 'ADDIE Model',
                  description: 'Systematic instructional design framework',
                  phases: ['Analysis', 'Design', 'Development', 'Implementation', 'Evaluation'],
                  bestFor: 'Formal training programs, complex learning initiatives',
                  timeframe: '3-12 months'
                },
                {
                  name: 'SAM (Successive Approximation Model)',
                  description: 'Agile, iterative approach to instructional design',
                  phases: ['Preparation', 'Iterative Design', 'Iterative Development'],
                  bestFor: 'Rapid development, changing requirements',
                  timeframe: '1-6 months'
                },
                {
                  name: 'Action Mapping',
                  description: 'Performance-focused design methodology',
                  phases: ['Business Goal', 'Behaviors', 'Practice', 'Information'],
                  bestFor: 'Performance improvement, behavior change',
                  timeframe: '2-8 weeks'
                },
                {
                  name: '70-20-10 Model',
                  description: 'Blended learning approach framework',
                  phases: ['70% Experience', '20% Social', '10% Formal'],
                  bestFor: 'Leadership development, skill building',
                  timeframe: 'Ongoing'
                }
              ].map((methodology, index) => (
                <Card key={index} className="p-4">
                  <h3 className="font-semibold mb-2">{methodology.name}</h3>
                  <p className="text-sm text-muted-foreground mb-4">{methodology.description}</p>
                  
                  <div className="space-y-3">
                    <div>
                      <Label className="text-xs">Key Phases</Label>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {methodology.phases.map((phase, i) => (
                          <Badge key={i} variant="secondary" className="text-xs">{phase}</Badge>
                        ))}
                      </div>
                    </div>
                    
                    <div>
                      <Label className="text-xs">Best For</Label>
                      <p className="text-sm mt-1">{methodology.bestFor}</p>
                    </div>
                    
                    <div>
                      <Label className="text-xs">Typical Timeframe</Label>
                      <p className="text-sm mt-1">{methodology.timeframe}</p>
                    </div>
                  </div>
                  
                  <Button variant="outline" size="sm" className="w-full mt-4">
                    <FileText className="h-4 w-4 mr-2" />
                    View Methodology Guide
                  </Button>
                </Card>
              ))}
            </div>
          </TabsContent>
        </Tabs>
      </Card>
    </div>
  );

  const renderCurrentView = () => {
    switch (activeView) {
      case 'dashboard':
        return renderDashboard();
      case 'addie':
        return renderADDIEView();
      case 'competency':
        return renderCompetencyView();
      case 'analytics':
        return renderAnalyticsView();
      case 'library':
        return renderLibraryView();
      default:
        return renderDashboard();
    }
  };

  return (
    <div className="h-full overflow-y-auto">
      <div className="container mx-auto p-6 max-w-7xl">
        {/* Header */}
        <motion.div 
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-6"
        >
          <div className="flex items-center justify-between mb-4">
            <div>
              <h1 className="text-3xl font-bold tracking-tight flex items-center gap-3">
                <GraduationCap className="h-8 w-8" />
                L&D Professional Training Needs Analysis
              </h1>
              <p className="mt-1 text-sm text-muted-foreground">
                Advanced tools and frameworks for learning and development professionals
              </p>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant="outline">ADDIE Framework</Badge>
              <Badge variant="outline">Kirkpatrick Model</Badge>
              <Badge variant="secondary">Professional</Badge>
            </div>
          </div>

          {/* Navigation */}
          <div className="flex items-center gap-2 overflow-x-auto">
            {[
              { id: 'dashboard', name: 'Dashboard', icon: BarChart3 },
              { id: 'addie', name: 'ADDIE Framework', icon: BookOpen },
              { id: 'competency', name: 'Competency Mapping', icon: Brain },
              { id: 'analytics', name: 'Analytics & ROI', icon: TrendingUp },
              { id: 'library', name: 'Template Library', icon: FileText },
              { id: 'collaboration', name: 'Collaboration', icon: MessageSquare }
            ].map((view) => (
              <button
                key={view.id}
                onClick={() => setActiveView(view.id as any)}
                className={`flex items-center gap-2 px-4 py-2 rounded-lg text-sm font-medium whitespace-nowrap transition-colors ${
                  activeView === view.id
                    ? 'bg-primary text-primary-foreground'
                    : 'bg-muted hover:bg-muted/80'
                }`}
              >
                <view.icon className="h-4 w-4" />
                {view.name}
              </button>
            ))}
          </div>
        </motion.div>

        {/* Main Content */}
        <AnimatePresence mode="wait">
          <motion.div
            key={activeView}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3 }}
          >
            {renderCurrentView()}
          </motion.div>
        </AnimatePresence>
      </div>
    </div>
  );
};

export default LDProfessionalTNA;
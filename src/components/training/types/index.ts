// Re-export all types from context
export type {
  TrainingNeed,
  TrainingProgram,
  SkillAssessment,
  TrainingMetrics
} from '@/contexts/TrainingContext';

// Dashboard specific types - removed unused DashboardTrainingProgram and LearningPath
// These were only used for mock data in removed tabs

// Analysis specific types
export interface AnalysisResult {
  totalEmployees: number;
  highPriorityNeeds: number;
  estimatedTotalCost: number;
  expectedROI: number;
  completionRate: number;
  feasibility: 'optimal' | 'viable' | 'risky' | 'infeasible';
  recommendations: string[];
}

// Workflow types
// ApprovalStage and ApprovalFlow interfaces removed as ApprovalWorkflow component was deleted

// Skills Gap Analysis types
export interface Skill {
  id: string;
  name: string;
  category: 'technical' | 'soft' | 'leadership' | 'compliance';
  currentLevel: number; // 1-10
  requiredLevel: number; // 1-10
  importance: 'critical' | 'high' | 'medium' | 'low';
  trend?: 'improving' | 'declining' | 'stable';
  description?: string;
}

export interface Employee {
  id: string;
  name: string;
  department: string;
  position: string;
  skills: Skill[];
  targetPosition?: string;
}

export interface EmployeeSkill {
  skillId: string;
  currentLevel: number;
  targetLevel: number;
  lastAssessed: Date;
}

export interface GapAnalysisResult {
  employee: Employee;
  overallGapScore: number; // 0-100 (0 = no gap, 100 = maximum gap)
  criticalGaps: Skill[];
  strengths: Skill[];
  recommendations: string[];
  estimatedTrainingHours: number;
  priority: 'urgent' | 'high' | 'medium' | 'low';
}

export interface SkillGap {
  skillId: string;
  skillName: string;
  currentLevel: number;
  targetLevel: number;
  gap: number;
  category: string;
}

export interface TrainingRecommendation {
  programId: string;
  programName: string;
  provider: string;
  relevantSkills: string[];
  estimatedDuration: string;
  cost: number;
  priority: number;
}

// Import/Export types
export interface CSVImportResult {
  success: boolean;
  imported: number;
  failed: number;
  errors: ImportError[];
}

export interface ImportError {
  row: number;
  field: string;
  message: string;
  value?: any;
}

export interface ExportOptions {
  format: 'csv' | 'excel' | 'pdf';
  includeFields: string[];
  filters?: ExportFilters;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface ExportFilters {
  departments?: string[];
  priorities?: string[];
  statuses?: string[];
  dateRange?: {
    start: Date;
    end: Date;
  };
}

// Collaboration types - SMECollaboration interface removed as component was deleted

// Filter and Sort types
export interface FilterOptions {
  departments: string[];
  priorities: string[];
  trainingTypes: string[];
  statuses: string[];
  managers: string[];
  providers: string[];
}

export interface SortOptions {
  field: 'employeeName' | 'department' | 'priority' | 'cost' | 'roi' | 'createdAt' | 'status';
  order: 'asc' | 'desc';
}

// Pagination types
export interface PaginationOptions {
  page: number;
  pageSize: number;
  total: number;
}

// Chart/Visualization types
export interface ChartData {
  labels: string[];
  datasets: ChartDataset[];
}

export interface ChartDataset {
  label: string;
  data: number[];
  backgroundColor?: string | string[];
  borderColor?: string | string[];
  borderWidth?: number;
}

// Notification types
export interface TrainingNotification {
  id: string;
  type: 'approval_required' | 'training_scheduled' | 'training_completed' | 'budget_alert' | 'deadline_approaching';
  title: string;
  message: string;
  priority: 'high' | 'medium' | 'low';
  timestamp: Date;
  read: boolean;
  actionRequired?: boolean;
  actionUrl?: string;
}
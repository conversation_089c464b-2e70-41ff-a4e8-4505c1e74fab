import React from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardHeader } from '@/components/ui/card';

export const ShimmerCard: React.FC<{ className?: string }> = ({ className = "" }) => {
  return (
    <Card className={`overflow-hidden ${className}`}>
      <CardHeader className="pb-2">
        <div className="h-4 w-32 bg-gradient-to-r from-transparent via-white/10 to-transparent rounded animate-shimmer" />
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          <div className="h-3 w-full bg-gradient-to-r from-transparent via-white/10 to-transparent rounded animate-shimmer" />
          <div className="h-3 w-4/5 bg-gradient-to-r from-transparent via-white/10 to-transparent rounded animate-shimmer animation-delay-150" />
          <div className="h-3 w-3/5 bg-gradient-to-r from-transparent via-white/10 to-transparent rounded animate-shimmer animation-delay-300" />
        </div>
      </CardContent>
    </Card>
  );
};

export const ShimmerList: React.FC<{ count?: number }> = ({ count = 3 }) => {
  return (
    <div className="space-y-2">
      {Array.from({ length: count }).map((_, i) => (
        <motion.div
          key={i}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: i * 0.1 }}
          className="flex items-center gap-3 p-3 rounded-lg bg-muted/50"
        >
          <div className="w-10 h-10 rounded-full bg-gradient-to-r from-transparent via-white/10 to-transparent animate-shimmer" />
          <div className="flex-1 space-y-2">
            <div className="h-3 w-32 bg-gradient-to-r from-transparent via-white/10 to-transparent rounded animate-shimmer" />
            <div className="h-2 w-24 bg-gradient-to-r from-transparent via-white/10 to-transparent rounded animate-shimmer animation-delay-150" />
          </div>
        </motion.div>
      ))}
    </div>
  );
};

export const ShimmerButton: React.FC<{ className?: string }> = ({ className = "" }) => {
  return (
    <div className={`h-9 px-4 rounded-md bg-gradient-to-r from-transparent via-white/10 to-transparent animate-shimmer ${className}`} />
  );
};
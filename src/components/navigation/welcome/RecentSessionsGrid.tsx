import React, { useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Clock, 
  RefreshCw, 
  Plus, 
  ChevronRight, 
  FolderOpen,
  Calendar,
  GitBranch,
  Eye,
  Grid3X3,
  List,
  LayoutGrid
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { cn } from '@/lib/utils';
import { type Session } from '@/lib/api';
import { AutoFitGrid } from './GridLayout';

interface RecentSessionsGridProps {
  sessions: Session[] | null;
  loading: boolean;
  error: any;
  isRefreshing: boolean;
  onOpenSession: (session: Session) => void;
  onNewSession: () => void;
  onRefresh: () => void;
  onViewAll: () => void;
  refetchSessions: () => void;
}

type ViewMode = 'grid' | 'list' | 'compact';

export const RecentSessionsGrid: React.FC<RecentSessionsGridProps> = ({
  sessions,
  loading,
  error,
  isRefreshing,
  onOpenSession,
  onNewSession,
  onRefresh,
  onViewAll,
  refetchSessions
}) => {
  const [viewMode, setViewMode] = useState<ViewMode>('grid');
  const [hoveredSession, setHoveredSession] = useState<string | null>(null);

  const formatDate = (timestamp: string | number) => {
    const date = new Date(Number(timestamp));
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMs / 3600000);
    const diffDays = Math.floor(diffMs / 86400000);

    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffHours < 24) return `${diffHours}h ago`;
    if (diffDays < 7) return `${diffDays}d ago`;
    return date.toLocaleDateString();
  };

  const getProjectName = (path: string) => {
    return path.split('/').pop() || 'Untitled';
  };

  const viewModeIcons = {
    grid: <Grid3X3 className="w-4 h-4" />,
    list: <List className="w-4 h-4" />,
    compact: <LayoutGrid className="w-4 h-4" />
  };

  const renderSessionCard = (session: Session) => {
    const isHovered = hoveredSession === session.id;
    
    return (
      <motion.div
        key={session.id}
        layout
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, scale: 0.95 }}
        whileHover={{ y: -4 }}
        onHoverStart={() => setHoveredSession(session.id)}
        onHoverEnd={() => setHoveredSession(null)}
        className={cn(
          "group relative",
          viewMode === 'list' && "mb-2"
        )}
      >
        <Card 
          className={cn(
            "cursor-pointer transition-all duration-200",
            "hover:shadow-lg hover:border-primary/30",
            isHovered && "ring-2 ring-primary/20 ring-offset-1",
            viewMode === 'compact' && "p-2"
          )}
          onClick={() => onOpenSession(session)}
        >
          {viewMode === 'grid' && (
            <>
              {/* Status Indicator */}
              <div className="absolute top-3 right-3 w-2 h-2 rounded-full bg-green-500 animate-pulse" />
              
              <CardContent className="p-4">
                <div className="space-y-3">
                  {/* Project Icon and Name */}
                  <div className="flex items-start gap-3">
                    <div className="p-2 rounded-lg bg-primary/10 text-primary">
                      <FolderOpen className="w-5 h-5" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <h3 className="font-semibold truncate text-sm lg:text-base">
                        {getProjectName(session.project_path)}
                      </h3>
                      <p className="text-xs text-muted-foreground truncate mt-0.5">
                        {session.project_path}
                      </p>
                    </div>
                  </div>

                  {/* Session Info */}
                  <div className="space-y-2">
                    <div className="flex items-center gap-2 text-xs text-muted-foreground">
                      <Calendar className="w-3 h-3" />
                      <span>{formatDate(session.created_at)}</span>
                    </div>
                    {session.branch && (
                      <div className="flex items-center gap-2 text-xs text-muted-foreground">
                        <GitBranch className="w-3 h-3" />
                        <span className="truncate">{session.branch}</span>
                      </div>
                    )}
                  </div>

                  {/* Tags */}
                  {session.tags && session.tags.length > 0 && (
                    <div className="flex flex-wrap gap-1">
                      {session.tags.slice(0, 3).map((tag, idx) => (
                        <Badge key={idx} variant="secondary" className="text-[10px] px-1.5 py-0">
                          {tag}
                        </Badge>
                      ))}
                      {session.tags.length > 3 && (
                        <Badge variant="outline" className="text-[10px] px-1.5 py-0">
                          +{session.tags.length - 3}
                        </Badge>
                      )}
                    </div>
                  )}

                  {/* Hover Action */}
                  <AnimatePresence>
                    {isHovered && (
                      <motion.div
                        initial={{ opacity: 0, height: 0 }}
                        animate={{ opacity: 1, height: 'auto' }}
                        exit={{ opacity: 0, height: 0 }}
                        className="pt-2 border-t"
                      >
                        <Button size="sm" className="w-full gap-2">
                          Open Session
                          <ChevronRight className="w-3 h-3" />
                        </Button>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>
              </CardContent>
            </>
          )}

          {viewMode === 'list' && (
            <CardContent className="p-3">
              <div className="flex items-center gap-3">
                <div className="p-2 rounded-lg bg-primary/10 text-primary">
                  <FolderOpen className="w-4 h-4" />
                </div>
                <div className="flex-1 min-w-0">
                  <h3 className="font-medium text-sm truncate">
                    {getProjectName(session.project_path)}
                  </h3>
                  <div className="flex items-center gap-3 text-xs text-muted-foreground mt-0.5">
                    <span>{formatDate(session.created_at)}</span>
                    {session.branch && (
                      <>
                        <span>•</span>
                        <span className="truncate">{session.branch}</span>
                      </>
                    )}
                  </div>
                </div>
                <ChevronRight className="w-4 h-4 text-muted-foreground group-hover:text-primary transition-colors" />
              </div>
            </CardContent>
          )}

          {viewMode === 'compact' && (
            <CardContent className="p-2">
              <div className="flex items-center gap-2">
                <FolderOpen className="w-3 h-3 text-primary" />
                <span className="text-xs font-medium truncate">
                  {getProjectName(session.project_path)}
                </span>
                <span className="text-[10px] text-muted-foreground ml-auto">
                  {formatDate(session.created_at)}
                </span>
              </div>
            </CardContent>
          )}
        </Card>
      </motion.div>
    );
  };

  return (
    <Card className="overflow-hidden">
      <CardHeader className="pb-3 px-4 sm:px-6">
        <div className="flex items-center justify-between gap-2">
          <div className="flex items-center gap-2">
            <CardTitle className="text-sm sm:text-base">Recent Sessions</CardTitle>
            {sessions && sessions.length > 0 && (
              <Badge variant="secondary" className="text-xs">
                {sessions.length}
              </Badge>
            )}
          </div>
          
          <div className="flex items-center gap-1 sm:gap-2">
            {/* View Mode Switcher */}
            <div className="flex items-center rounded-lg border p-0.5">
              {Object.entries(viewModeIcons).map(([mode, icon]) => (
                <TooltipProvider key={mode}>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        size="sm"
                        variant={viewMode === mode ? "default" : "ghost"}
                        className="h-7 w-7 p-0"
                        onClick={() => setViewMode(mode as ViewMode)}
                      >
                        {icon}
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="text-xs capitalize">{mode} View</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              ))}
            </div>

            {/* Action Buttons */}
            <Button 
              size="sm" 
              variant="ghost"
              onClick={onRefresh}
              disabled={isRefreshing}
              className="h-8 w-8 p-0"
            >
              <RefreshCw className={cn(
                "w-4 h-4",
                isRefreshing && "animate-spin"
              )} />
            </Button>
            
            <Button 
              size="sm" 
              variant="ghost"
              onClick={onViewAll}
              className="hidden sm:flex gap-1"
            >
              <Eye className="w-4 h-4" />
              <span className="hidden lg:inline">View All</span>
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="px-3 sm:px-6 pb-4">
        {loading ? (
          <div className="space-y-3">
            {[1, 2, 3].map((i) => (
              <Skeleton key={i} className="h-24 w-full" />
            ))}
          </div>
        ) : error ? (
          <div className="text-center py-8">
            <p className="text-sm text-muted-foreground mb-3">
              Failed to load sessions
            </p>
            <Button size="sm" variant="outline" onClick={refetchSessions}>
              Try Again
            </Button>
          </div>
        ) : !sessions || sessions.length === 0 ? (
          <div className="text-center py-8 sm:py-12">
            <Clock className="w-12 h-12 text-muted-foreground mx-auto mb-3" />
            <p className="text-sm text-muted-foreground mb-4">
              No recent sessions
            </p>
            <Button size="sm" onClick={onNewSession} className="gap-2">
              <Plus className="w-4 h-4" />
              Start New Session
            </Button>
          </div>
        ) : (
          <AnimatePresence mode="wait">
            {viewMode === 'grid' ? (
              <AutoFitGrid minItemWidth="280px" gap="1rem">
                {sessions.map(renderSessionCard)}
              </AutoFitGrid>
            ) : (
              <div className="space-y-2">
                {sessions.map(renderSessionCard)}
              </div>
            )}
          </AnimatePresence>
        )}
      </CardContent>
    </Card>
  );
};
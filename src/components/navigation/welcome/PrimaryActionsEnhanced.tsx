import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Plus, ListTodo, ArrowR<PERSON>, <PERSON>rk<PERSON>, TrendingUp } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { fadeInUp, springTransition } from './animations';

interface PrimaryAction {
  title: string;
  description: string;
  icon: React.ReactNode;
  onClick: () => void;
  gradient: string;
  stats: string;
  trend?: number;
  badge?: string;
  hotkey?: string;
}

interface PrimaryActionsProps {
  onNewSession: () => void;
  createTaskMasterTab: () => void;
  sessionCount: number;
}

export const PrimaryActionsEnhanced: React.FC<PrimaryActionsProps> = ({
  onNewSession,
  createTaskMasterTab,
  sessionCount
}) => {
  const [hoveredCard, setHoveredCard] = useState<string | null>(null);
  const [clickedCard, setClickedCard] = useState<string | null>(null);

  const handleCardClick = (action: PrimaryAction) => {
    setClickedCard(action.title);
    setTimeout(() => {
      action.onClick();
      setClickedCard(null);
    }, 200);
  };

  const primaryActions: PrimaryAction[] = [
    {
      title: "New Session",
      description: "Start coding with Claude",
      icon: <Plus className="w-4 h-4 sm:w-5 sm:h-5" />,
      onClick: onNewSession,
      gradient: "from-blue-500 to-purple-600",
      stats: `${sessionCount} active`,
      trend: sessionCount > 0 ? 12 : 0,
      badge: sessionCount === 0 ? "Start Here" : undefined,
      hotkey: "⌘N"
    },
    {
      title: "TaskMaster",
      description: "Manage your projects",
      icon: <ListTodo className="w-4 h-4 sm:w-5 sm:h-5" />,
      onClick: createTaskMasterTab,
      gradient: "from-orange-500 to-red-600",
      stats: "2 projects",
      trend: 5,
      badge: "Popular",
      hotkey: "⌘T"
    }
  ];

  return (
    <motion.div 
      className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4 mb-4 sm:mb-6"
      variants={{
        animate: {
          transition: {
            staggerChildren: 0.1
          }
        }
      }}
      initial="initial"
      animate="animate"
    >
      {primaryActions.map((action) => (
        <motion.div
          key={action.title}
          variants={fadeInUp}
          whileHover={{ y: -4 }}
          whileTap={{ scale: 0.98 }}
          onHoverStart={() => setHoveredCard(action.title)}
          onHoverEnd={() => setHoveredCard(null)}
        >
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Card 
                  className={`
                    relative overflow-hidden cursor-pointer group
                    border-2 transition-all duration-300
                    ${hoveredCard === action.title ? 'border-primary/50 shadow-xl' : 'border-transparent'}
                    ${clickedCard === action.title ? 'ring-2 ring-primary ring-offset-2 ring-offset-background' : ''}
                  `}
                  onClick={() => handleCardClick(action)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' || e.key === ' ') {
                      e.preventDefault();
                      handleCardClick(action);
                    }
                  }}
                  tabIndex={0}
                  role="button"
                  aria-label={`${action.title}: ${action.description}`}
                >
                  {/* Gradient Background */}
                  <div 
                    className={`absolute inset-0 bg-gradient-to-br ${action.gradient} opacity-0 group-hover:opacity-10 transition-opacity duration-500`}
                  />
                  
                  {/* Animated Background Pattern */}
                  <div className="absolute inset-0 opacity-5">
                    <div className="absolute inset-0 bg-grid-white/10" />
                  </div>

                  {/* Badge */}
                  {action.badge && (
                    <motion.div
                      initial={{ scale: 0, rotate: -12 }}
                      animate={{ scale: 1, rotate: 0 }}
                      className="absolute top-2 right-2 z-10"
                    >
                      <Badge className="bg-gradient-to-r from-yellow-500 to-orange-500 text-white border-0">
                        <Sparkles className="w-3 h-3 mr-1" />
                        {action.badge}
                      </Badge>
                    </motion.div>
                  )}

                  <CardContent className="p-4 sm:p-6 relative z-10">
                    <div className="flex items-start justify-between gap-2">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 sm:gap-3 mb-1 sm:mb-2">
                          <motion.div 
                            className={`p-2 sm:p-2.5 rounded-xl bg-gradient-to-br ${action.gradient} text-white flex-shrink-0 shadow-lg`}
                            animate={hoveredCard === action.title ? { rotate: [0, -10, 10, -10, 0] } : {}}
                            transition={{ duration: 0.5 }}
                          >
                            {action.icon}
                          </motion.div>
                          <div className="flex-1 min-w-0">
                            <h3 className="text-base sm:text-lg font-semibold truncate flex items-center gap-2">
                              {action.title}
                              {action.hotkey && (
                                <kbd className="hidden sm:inline-flex px-1.5 py-0.5 text-[10px] font-mono bg-muted rounded">
                                  {action.hotkey}
                                </kbd>
                              )}
                            </h3>
                            <p className="text-xs sm:text-sm text-muted-foreground line-clamp-1">
                              {action.description}
                            </p>
                          </div>
                        </div>
                      </div>

                      <div className="text-right flex-shrink-0">
                        <motion.p 
                          className="text-xl sm:text-2xl font-bold tabular-nums"
                          animate={hoveredCard === action.title ? { scale: 1.1 } : { scale: 1 }}
                        >
                          {action.stats.split(' ')[0]}
                        </motion.p>
                        <p className="text-xs text-muted-foreground">
                          {action.stats.split(' ')[1]}
                        </p>
                        {action.trend !== undefined && action.trend > 0 && (
                          <motion.div 
                            className="flex items-center justify-end gap-1 mt-1"
                            initial={{ opacity: 0, y: 5 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: 0.2 }}
                          >
                            <TrendingUp className="w-3 h-3 text-green-600" />
                            <span className="text-xs text-green-600 font-medium">
                              +{action.trend}%
                            </span>
                          </motion.div>
                        )}
                      </div>
                    </div>

                    {/* Hover Action Indicator */}
                    <AnimatePresence>
                      {hoveredCard === action.title && (
                        <motion.div
                          initial={{ opacity: 0, x: -10 }}
                          animate={{ opacity: 1, x: 0 }}
                          exit={{ opacity: 0, x: -10 }}
                          className="absolute bottom-4 right-4 flex items-center gap-1 text-xs text-muted-foreground"
                        >
                          <span>Open</span>
                          <ArrowRight className="w-3 h-3" />
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </CardContent>
                </Card>
              </TooltipTrigger>
              <TooltipContent side="bottom">
                <p className="text-xs">Click to {action.title.toLowerCase()}</p>
                {action.hotkey && (
                  <p className="text-xs text-muted-foreground mt-1">
                    Keyboard shortcut: {action.hotkey}
                  </p>
                )}
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </motion.div>
      ))}
    </motion.div>
  );
};
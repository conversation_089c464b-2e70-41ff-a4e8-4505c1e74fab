import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  CheckCircle, 
  Circle, 
  Sparkles, 
  Trophy, 
  Clock,
  ChevronRight,
  Info
} from 'lucide-react';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { celebration, listItem, staggerContainer } from './animations';
import confetti from 'canvas-confetti';

interface GettingStartedStep {
  id: string;
  title: string;
  description: string;
  completed: boolean;
  priority: 'high' | 'medium' | 'low';
  estimatedTime: string;
  action?: () => void;
}

interface GettingStartedProps {
  sessionCount: number;
  onStepClick?: (step: GettingStartedStep) => void;
}

export const GettingStartedEnhanced: React.FC<GettingStartedProps> = ({ 
  sessionCount,
  onStepClick 
}) => {
  const [expandedStep, setExpandedStep] = useState<string | null>(null);
  const [justCompleted, setJustCompleted] = useState<string | null>(null);
  const [showConfetti, setShowConfetti] = useState(false);

  const gettingStartedSteps: GettingStartedStep[] = [
    { 
      id: 'create-session',
      title: "Create a session", 
      description: "Start your first coding session with Claude",
      completed: sessionCount > 0,
      priority: "high",
      estimatedTime: "2 min"
    },
    { 
      id: 'configure-mcp',
      title: "Configure MCP servers", 
      description: "Set up Model Context Protocol for enhanced capabilities",
      completed: false,
      priority: "medium",
      estimatedTime: "5 min"
    },
    { 
      id: 'create-agent',
      title: "Create an agent", 
      description: "Build your first AI agent for automation",
      completed: false,
      priority: "low",
      estimatedTime: "10 min"
    },
    { 
      id: 'setup-taskmaster',
      title: "Set up TaskMaster", 
      description: "Organize and manage your projects efficiently",
      completed: false,
      priority: "low",
      estimatedTime: "3 min"
    }
  ];

  const completedCount = gettingStartedSteps.filter(step => step.completed).length;
  const progressPercentage = (completedCount / gettingStartedSteps.length) * 100;
  const allCompleted = completedCount === gettingStartedSteps.length;

  useEffect(() => {
    if (allCompleted && !showConfetti) {
      setShowConfetti(true);
      // Trigger confetti animation
      confetti({
        particleCount: 100,
        spread: 70,
        origin: { y: 0.6 }
      });
    }
  }, [allCompleted]);

  const handleStepClick = (step: GettingStartedStep) => {
    if (!step.completed) {
      setExpandedStep(expandedStep === step.id ? null : step.id);
      onStepClick?.(step);
    } else {
      // Show completion animation
      setJustCompleted(step.id);
      setTimeout(() => setJustCompleted(null), 1000);
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'text-red-600 bg-red-50 dark:bg-red-900/20';
      case 'medium': return 'text-yellow-600 bg-yellow-50 dark:bg-yellow-900/20';
      case 'low': return 'text-green-600 bg-green-50 dark:bg-green-900/20';
      default: return 'text-gray-600 bg-gray-50 dark:bg-gray-900/20';
    }
  };

  return (
    <Card className="relative overflow-hidden">
      {/* Celebration Overlay */}
      <AnimatePresence>
        {allCompleted && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute inset-0 pointer-events-none z-10"
          >
            <div className="absolute inset-0 bg-gradient-to-br from-yellow-400/10 to-purple-600/10" />
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
              className="absolute top-4 right-4"
            >
              <Trophy className="w-8 h-8 text-yellow-500" />
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      <CardHeader className="pb-2 sm:pb-3 px-4 sm:px-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <CardTitle className="text-sm sm:text-base">Getting Started</CardTitle>
            {allCompleted && (
              <motion.div
                variants={celebration}
                initial="initial"
                animate="animate"
              >
                <Badge className="bg-gradient-to-r from-yellow-500 to-orange-500 text-white border-0">
                  <Sparkles className="w-3 h-3 mr-1" />
                  Complete!
                </Badge>
              </motion.div>
            )}
          </div>
          <div className="flex items-center gap-2">
            <span className="text-xs text-muted-foreground">
              {completedCount}/{gettingStartedSteps.length}
            </span>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                    <Info className="h-3 w-3" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p className="text-xs">Complete these steps to get the most out of Claudia</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        </div>
        
        {/* Progress bar with animation */}
        <div className="mt-2 h-2 bg-muted rounded-full overflow-hidden relative">
          <motion.div 
            className="h-full bg-gradient-to-r from-primary to-primary/60 relative"
            initial={{ width: 0 }}
            animate={{ width: `${progressPercentage}%` }}
            transition={{ duration: 0.5, ease: "easeOut" }}
          >
            {/* Shimmer effect on progress bar */}
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer" />
          </motion.div>
        </div>
      </CardHeader>

      <CardContent className="px-4 sm:px-6 pb-4 sm:pb-6">
        <motion.div 
          className="space-y-2 sm:space-y-2.5"
          variants={staggerContainer}
          initial="initial"
          animate="animate"
        >
          {gettingStartedSteps.map((step) => (
            <motion.div
              key={step.id}
              variants={listItem}
              className="group"
            >
              <motion.div
                className={`
                  flex items-center gap-2 sm:gap-3 p-2 rounded-lg
                  transition-all duration-200 cursor-pointer
                  ${!step.completed ? 'hover:bg-muted/50' : ''}
                  ${expandedStep === step.id ? 'bg-muted/50' : ''}
                `}
                onClick={() => handleStepClick(step)}
                whileHover={!step.completed ? { x: 4 } : {}}
                whileTap={{ scale: 0.98 }}
                role="button"
                tabIndex={0}
                aria-expanded={expandedStep === step.id}
                aria-label={`${step.title} - ${step.completed ? 'Completed' : 'Not completed'}`}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    handleStepClick(step);
                  }
                }}
              >
                {/* Step Icon */}
                <motion.div 
                  className="flex-shrink-0"
                  animate={justCompleted === step.id ? celebration.animate : {}}
                >
                  {step.completed ? (
                    <CheckCircle className="w-4 h-4 sm:w-5 sm:h-5 text-green-600" />
                  ) : (
                    <Circle className="w-4 h-4 sm:w-5 sm:h-5 text-muted-foreground group-hover:text-primary transition-colors" />
                  )}
                </motion.div>

                {/* Step Content */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 flex-wrap">
                    <span className={`text-xs sm:text-sm font-medium ${
                      step.completed ? 'line-through text-muted-foreground' : ''
                    }`}>
                      {step.title}
                    </span>
                    
                    {!step.completed && (
                      <>
                        {step.priority === 'high' && (
                          <Badge variant="outline" className={`text-[10px] h-5 px-1.5 ${getPriorityColor(step.priority)}`}>
                            Recommended
                          </Badge>
                        )}
                        <div className="flex items-center gap-1 text-[10px] text-muted-foreground">
                          <Clock className="w-3 h-3" />
                          <span>{step.estimatedTime}</span>
                        </div>
                      </>
                    )}
                  </div>

                  {/* Expanded Description */}
                  <AnimatePresence>
                    {expandedStep === step.id && !step.completed && (
                      <motion.div
                        initial={{ opacity: 0, height: 0 }}
                        animate={{ opacity: 1, height: 'auto' }}
                        exit={{ opacity: 0, height: 0 }}
                        className="mt-1"
                      >
                        <p className="text-xs text-muted-foreground">
                          {step.description}
                        </p>
                        <Button 
                          size="sm" 
                          variant="ghost" 
                          className="h-6 mt-1 text-xs gap-1"
                          onClick={(e) => {
                            e.stopPropagation();
                            step.action?.();
                          }}
                        >
                          Start
                          <ChevronRight className="w-3 h-3" />
                        </Button>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>

                {/* Chevron Indicator */}
                {!step.completed && (
                  <motion.div
                    animate={{ rotate: expandedStep === step.id ? 90 : 0 }}
                    transition={{ duration: 0.2 }}
                  >
                    <ChevronRight className="w-3 h-3 text-muted-foreground" />
                  </motion.div>
                )}
              </motion.div>
            </motion.div>
          ))}
        </motion.div>

        {/* Completion Message */}
        <AnimatePresence>
          {allCompleted && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className="mt-4 p-3 rounded-lg bg-gradient-to-r from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20 border border-yellow-200 dark:border-yellow-800"
            >
              <div className="flex items-center gap-2">
                <Trophy className="w-5 h-5 text-yellow-600" />
                <div>
                  <p className="text-sm font-medium text-yellow-900 dark:text-yellow-100">
                    Congratulations! You're all set up! 🎉
                  </p>
                  <p className="text-xs text-yellow-700 dark:text-yellow-300 mt-0.5">
                    You've completed all the getting started steps. Happy coding!
                  </p>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </CardContent>
    </Card>
  );
};
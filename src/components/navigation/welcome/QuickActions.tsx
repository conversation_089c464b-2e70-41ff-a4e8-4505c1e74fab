import React from 'react';
import { Bot, Terminal, DollarSign, Settings as SettingsIcon } from 'lucide-react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

interface QuickAction {
  title: string;
  icon: React.ReactNode;
  onClick: () => void;
  count: number;
}

interface QuickActionsProps {
  createAgentsOverviewTab: () => void;
  createMCPOverviewTab: () => void;
  createTrainingBudgetTab: () => void;
  createSettingsOverviewTab: () => void;
}

export const QuickActions: React.FC<QuickActionsProps> = ({
  createAgentsOverviewTab,
  createMCPOverviewTab,
  createTrainingBudgetTab,
  createSettingsOverviewTab
}) => {
  const handleActionClick = (action: QuickAction) => {
    try {
      action.onClick();
    } catch (error) {
      console.error(`Failed to execute ${action.title} action:`, error);
    }
  };

  const secondaryActions: QuickAction[] = [
    {
      title: "CC Agents",
      icon: <Bot className="w-3.5 h-3.5 sm:w-4 sm:h-4" />,
      onClick: createAgentsOverviewTab,
      count: 3
    },
    {
      title: "MCP Servers",
      icon: <Terminal className="w-3.5 h-3.5 sm:w-4 sm:h-4" />,
      onClick: createMCPOverviewTab,
      count: 5
    },
    {
      title: "Training Budget",
      icon: <DollarSign className="w-3.5 h-3.5 sm:w-4 sm:h-4" />,
      onClick: createTrainingBudgetTab,
      count: 0
    },
    {
      title: "Settings",
      icon: <SettingsIcon className="w-3.5 h-3.5 sm:w-4 sm:h-4" />,
      onClick: createSettingsOverviewTab,
      count: 0
    }
  ];

  return (
    <Card>
      <CardHeader className="pb-2 sm:pb-3 px-4 sm:px-6">
        <CardTitle className="text-sm sm:text-base">Quick Actions</CardTitle>
      </CardHeader>
      <CardContent className="px-3 sm:px-6 pb-3 sm:pb-6">
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-2 sm:gap-3">
          {secondaryActions.map((action) => (
            <button
              key={action.title}
              onClick={() => handleActionClick(action)}
              className="p-2.5 sm:p-3 rounded-lg border hover:bg-accent transition-colors text-left group active:scale-[0.98] touch-manipulation"
              aria-label={`Open ${action.title}`}
            >
              <div className="flex items-center justify-between mb-1.5 sm:mb-2">
                <div className="text-muted-foreground group-hover:text-foreground transition-colors">
                  {action.icon}
                </div>
                {action.count > 0 && (
                  <Badge variant="secondary" className="text-[10px] sm:text-xs px-1 sm:px-1.5 py-0 h-4 sm:h-5">
                    {action.count}
                  </Badge>
                )}
              </div>
              <p className="text-xs sm:text-sm font-medium truncate">{action.title}</p>
            </button>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};
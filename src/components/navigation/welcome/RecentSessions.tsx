import React, { useState } from 'react';
import { <PERSON>older, <PERSON>R<PERSON>, ChevronRight, Refresh<PERSON>c<PERSON>, FileText, AlertCircle } from 'lucide-react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { SkeletonList } from '@/components/ui/skeleton';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { type Session } from '@/lib/api';

interface RecentSessionsProps {
  sessions: Session[] | null;
  loading: boolean;
  error: Error | null;
  isRefreshing: boolean;
  onOpenSession: (session: Session) => void;
  onNewSession: () => void;
  onRefresh: () => void;
  onViewAll: () => void;
  refetchSessions: () => void;
}

export const RecentSessions: React.FC<RecentSessionsProps> = ({
  sessions,
  loading,
  error,
  isRefreshing,
  onOpenSession,
  onNewSession,
  onRefresh,
  onViewAll,
  refetchSessions
}) => {
  const [sessionError, setSessionError] = useState<string | null>(null);

  const handleSessionClick = (session: Session) => {
    try {
      setSessionError(null);
      onOpenSession(session);
    } catch (err) {
      console.error('Failed to open session:', err);
      setSessionError(`Failed to open session: ${session.project_path.split('/').pop()}`);
    }
  };

  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      const now = new Date();
      const diffTime = Math.abs(now.getTime() - date.getTime());
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      
      if (diffDays === 0) return 'Today';
      if (diffDays === 1) return 'Yesterday';
      if (diffDays < 7) return `${diffDays} days ago`;
      
      return date.toLocaleDateString();
    } catch {
      return 'Unknown date';
    }
  };

  return (
    <Card>
      <CardHeader className="pb-2 sm:pb-3 px-4 sm:px-6">
        <div className="flex items-center justify-between">
          <CardTitle className="text-sm sm:text-base">Recent Sessions</CardTitle>
          <div className="flex items-center gap-1 sm:gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={onRefresh}
              disabled={isRefreshing || loading}
              className="h-7 w-7 sm:h-8 sm:w-8 p-0"
              aria-label="Refresh sessions"
            >
              <RefreshCcw className={`w-3.5 h-3.5 sm:w-4 sm:h-4 ${isRefreshing ? 'animate-spin' : ''}`} />
            </Button>
            <Button 
              variant="ghost" 
              size="sm"
              onClick={onViewAll}
              className="h-7 sm:h-8 text-xs sm:text-sm px-2 sm:px-3"
            >
              <span className="hidden sm:inline">View All</span>
              <span className="sm:hidden">All</span>
              <ChevronRight className="w-3.5 h-3.5 sm:w-4 sm:h-4 ml-1" />
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="p-0">
        {sessionError && (
          <div className="px-4 pt-2">
            <Alert variant="destructive" className="py-2">
              <AlertCircle className="h-3 w-3" />
              <AlertDescription className="text-xs">
                {sessionError}
              </AlertDescription>
            </Alert>
          </div>
        )}
        
        {loading ? (
          <SkeletonList count={3} className="p-3 sm:p-4" />
        ) : error ? (
          <div className="p-4 sm:p-6 text-center">
            <AlertCircle className="w-6 h-6 sm:w-8 sm:h-8 text-destructive mx-auto mb-2" />
            <p className="text-xs sm:text-sm text-muted-foreground mb-2">
              Failed to load sessions
            </p>
            <p className="text-xs text-muted-foreground mb-3">
              {error.message || 'Please check your connection'}
            </p>
            <Button 
              onClick={refetchSessions} 
              variant="outline" 
              size="sm" 
              className="text-xs sm:text-sm h-7 sm:h-8"
            >
              Try Again
            </Button>
          </div>
        ) : sessions && sessions.length > 0 ? (
          <div className="divide-y max-h-[300px] sm:max-h-[400px] overflow-y-auto">
            {sessions.map((session) => (
              <button
                key={session.id}
                onClick={() => handleSessionClick(session)}
                className="w-full flex items-center justify-between p-3 sm:p-4 hover:bg-accent/50 transition-colors text-left group active:bg-accent/70 touch-manipulation"
                aria-label={`Open session: ${session.project_path.split('/').pop() || 'Untitled'}`}
              >
                <div className="flex items-center gap-2 sm:gap-3 min-w-0 flex-1">
                  <Folder className="w-3.5 h-3.5 sm:w-4 sm:h-4 text-muted-foreground flex-shrink-0" />
                  <div className="min-w-0 flex-1">
                    <p className="text-xs sm:text-sm font-medium truncate">
                      {session.project_path.split('/').pop() || 'Untitled'}
                    </p>
                    <p className="text-[10px] sm:text-xs text-muted-foreground">
                      {formatDate(session.created_at.toString())}
                    </p>
                  </div>
                </div>
                <ArrowRight className="w-3.5 h-3.5 sm:w-4 sm:h-4 text-muted-foreground group-hover:translate-x-0.5 transition-transform flex-shrink-0" />
              </button>
            ))}
          </div>
        ) : (
          <div className="p-4 sm:p-6 text-center">
            <FileText className="w-6 h-6 sm:w-8 sm:h-8 text-muted-foreground mx-auto mb-2" />
            <p className="text-xs sm:text-sm text-muted-foreground mb-1">No recent sessions</p>
            <p className="text-[10px] sm:text-xs text-muted-foreground mb-3">
              Start a new session to begin coding with Claude
            </p>
            <Button 
              onClick={onNewSession} 
              size="sm" 
              className="text-xs sm:text-sm h-7 sm:h-9"
            >
              Start Your First Session
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
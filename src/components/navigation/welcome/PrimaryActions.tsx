import React from 'react';
import { motion } from 'framer-motion';
import { Plus, ListTodo } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';

interface PrimaryAction {
  title: string;
  description: string;
  icon: React.ReactNode;
  onClick: () => void;
  gradient: string;
  stats: string;
}

interface PrimaryActionsProps {
  onNewSession: () => void;
  createTaskMasterTab: () => void;
  sessionCount: number;
}

export const PrimaryActions: React.FC<PrimaryActionsProps> = ({
  onNewSession,
  createTaskMasterTab,
  sessionCount
}) => {
  const primaryActions: PrimaryAction[] = [
    {
      title: "New Session",
      description: "Start coding with <PERSON>",
      icon: <Plus className="w-4 h-4 sm:w-5 sm:h-5" />,
      onClick: onNewSession,
      gradient: "from-blue-500 to-purple-600",
      stats: `${sessionCount} active`
    },
    {
      title: "TaskMaster",
      description: "Manage your projects",
      icon: <ListTodo className="w-4 h-4 sm:w-5 sm:h-5" />,
      onClick: createTaskMasterTab,
      gradient: "from-orange-500 to-red-600",
      stats: "2 projects"
    }
  ];

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4 mb-4 sm:mb-6">
      {primaryActions.map((action, index) => (
        <motion.div
          key={action.title}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: index * 0.1 }}
        >
          <Card 
            className="relative overflow-hidden hover:shadow-lg transition-all duration-200 cursor-pointer group active:scale-[0.98]"
            onClick={(e) => {
              e.preventDefault();
              try {
                action.onClick();
              } catch (error) {
                console.error(`Failed to execute ${action.title} action:`, error);
              }
            }}
          >
            <div className={`absolute inset-0 bg-gradient-to-br ${action.gradient} opacity-5 group-hover:opacity-10 transition-opacity`} />
            <CardContent className="p-4 sm:p-6">
              <div className="flex items-start justify-between gap-2">
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 sm:gap-3 mb-1 sm:mb-2">
                    <div className={`p-1.5 sm:p-2 rounded-lg bg-gradient-to-br ${action.gradient} text-white flex-shrink-0`}>
                      {action.icon}
                    </div>
                    <h3 className="text-base sm:text-lg font-semibold truncate">{action.title}</h3>
                  </div>
                  <p className="text-xs sm:text-sm text-muted-foreground line-clamp-2">{action.description}</p>
                </div>
                <div className="text-right flex-shrink-0">
                  <p className="text-xl sm:text-2xl font-bold">{action.stats.split(' ')[0]}</p>
                  <p className="text-xs text-muted-foreground">{action.stats.split(' ')[1]}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      ))}
    </div>
  );
};
import React, { useState } from 'react';
import {
  Activity,
  Wifi,
  WifiOff,
  CheckCircle,
  AlertCircle,
  Server,
  HardDrive,
  Menu,
  X
} from 'lucide-react';
import { Button } from '@/components/ui/button';

export type WelcomeTabVariant = 'standard' | 'professional';

interface WelcomeHeaderProps {
  systemHealth: any;
  variant?: WelcomeTabVariant;
}

export const WelcomeHeader: React.FC<WelcomeHeaderProps> = ({ 
  systemHealth, 
  variant = 'standard' 
}) => {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  const getStatusColor = (status: string) => {
    const darkModeClasses = variant === 'standard' ? ' dark:bg-green-900/20' : '';
    switch (status) {
      case 'connected':
      case 'online':
        return `text-green-600 bg-green-50${darkModeClasses}`;
      case 'slow':
        return `text-yellow-600 bg-yellow-50${variant === 'standard' ? ' dark:bg-yellow-900/20' : ''}`;
      case 'error':
      case 'offline':
        return `text-red-600 bg-red-50${variant === 'standard' ? ' dark:bg-red-900/20' : ''}`;
      default:
        return `text-gray-600 bg-gray-50${variant === 'standard' ? ' dark:bg-gray-900/20' : ''}`;
    }
  };

  const getHealthIcon = () => {
    if (systemHealth?.healthScore >= 80) {
      return <CheckCircle className="w-4 h-4 text-green-600" />;
    }
    return <AlertCircle className="w-4 h-4 text-yellow-600" />;
  };

  return (
    <div className="bg-background border-b border-border/50">
      {/* Desktop Header */}
      <div className="hidden md:block">
        <div className="container mx-auto px-4 lg:px-6 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3 lg:gap-6">
              <h1 className="text-lg font-semibold">Claudia</h1>
              <div className="flex items-center gap-2 lg:gap-4 text-sm">
                <div className={`flex items-center gap-1.5 px-2 py-1 rounded ${getStatusColor(systemHealth?.claudeCodeStatus || 'checking')}`}>
                  <Server className="w-3 h-3" />
                  <span className="font-medium hidden lg:inline">Claude</span>
                  <span>{systemHealth?.claudeCodeStatus || 'Checking'}</span>
                </div>
                <div className={`flex items-center gap-1.5 px-2 py-1 rounded ${getStatusColor(systemHealth?.apiConnection || 'checking')}`}>
                  {systemHealth?.apiConnection === 'online' ? <Wifi className="w-3 h-3" /> : <WifiOff className="w-3 h-3" />}
                  <span className="hidden lg:inline">API</span>
                  <span>{systemHealth?.responseTime ? `${systemHealth.responseTime}ms` : '--'}</span>
                </div>
              </div>
            </div>

            <div className="flex items-center gap-3 lg:gap-6 text-sm">
              <div className="flex items-center gap-2">
                <Activity className="w-4 h-4 text-muted-foreground" />
                <span className="text-muted-foreground hidden lg:inline">Processes:</span>
                <span className="font-medium">{systemHealth?.activeProcesses || 0}</span>
              </div>
              <div className="flex items-center gap-2">
                <HardDrive className="w-4 h-4 text-muted-foreground" />
                <span className="text-muted-foreground hidden lg:inline">Storage:</span>
                <span className="font-medium">{systemHealth?.diskSpace?.percentage?.toFixed(0) || '--'}%</span>
              </div>
              <div className="flex items-center gap-2">
                {getHealthIcon()}
                <span className="text-muted-foreground hidden lg:inline">Health:</span>
                <span className="font-medium">{systemHealth?.healthScore || '--'}%</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile Header */}
      <div className="md:hidden">
        <div className="px-4 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <h1 className="text-lg font-semibold">Claudia</h1>
              <div className="flex items-center gap-2">
                {getHealthIcon()}
                <span className="text-sm font-medium">{systemHealth?.healthScore || '--'}%</span>
              </div>
            </div>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
              className="p-2"
            >
              {mobileMenuOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
            </Button>
          </div>

          {/* Mobile Menu Dropdown */}
          {mobileMenuOpen && (
            <div className="mt-3 space-y-2 pb-2 animate-in slide-in-from-top-2">
              <div className="grid grid-cols-2 gap-2 text-sm">
                <div className={`flex items-center gap-1.5 px-2 py-1.5 rounded ${getStatusColor(systemHealth?.claudeCodeStatus || 'checking')}`}>
                  <Server className="w-3 h-3" />
                  <span>{systemHealth?.claudeCodeStatus || 'Checking'}</span>
                </div>
                <div className={`flex items-center gap-1.5 px-2 py-1.5 rounded ${getStatusColor(systemHealth?.apiConnection || 'checking')}`}>
                  {systemHealth?.apiConnection === 'online' ? <Wifi className="w-3 h-3" /> : <WifiOff className="w-3 h-3" />}
                  <span>{systemHealth?.responseTime ? `${systemHealth.responseTime}ms` : '--'}</span>
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-2 text-sm">
                <div className="flex items-center gap-2 px-2 py-1">
                  <Activity className="w-3.5 h-3.5 text-muted-foreground" />
                  <span className="text-muted-foreground">Processes:</span>
                  <span className="font-medium">{systemHealth?.activeProcesses || 0}</span>
                </div>
                <div className="flex items-center gap-2 px-2 py-1">
                  <HardDrive className="w-3.5 h-3.5 text-muted-foreground" />
                  <span className="text-muted-foreground">Storage:</span>
                  <span className="font-medium">{systemHealth?.diskSpace?.percentage?.toFixed(0) || '--'}%</span>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
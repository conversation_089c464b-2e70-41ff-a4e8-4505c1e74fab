import React from 'react';
import { cn } from '@/lib/utils';

interface GridLayoutProps {
  children: React.ReactNode;
  className?: string;
  variant?: 'default' | 'compact' | 'spacious';
}

interface GridItemProps {
  children: React.ReactNode;
  className?: string;
  span?: 'full' | 'half' | 'third' | 'two-thirds' | 'quarter' | 'three-quarters';
}

export const GridLayout: React.FC<GridLayoutProps> = ({ 
  children, 
  className,
  variant = 'default' 
}) => {
  const gapClasses = {
    default: 'gap-3 sm:gap-4 lg:gap-6',
    compact: 'gap-2 sm:gap-3 lg:gap-4',
    spacious: 'gap-4 sm:gap-6 lg:gap-8'
  };

  return (
    <div className={cn(
      "grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-12",
      gapClasses[variant],
      className
    )}>
      {children}
    </div>
  );
};

export const GridItem: React.FC<GridItemProps> = ({ 
  children, 
  className,
  span = 'full'
}) => {
  const spanClasses = {
    'full': 'col-span-1 sm:col-span-2 lg:col-span-12',
    'half': 'col-span-1 sm:col-span-1 lg:col-span-6',
    'third': 'col-span-1 sm:col-span-1 lg:col-span-4',
    'two-thirds': 'col-span-1 sm:col-span-2 lg:col-span-8',
    'quarter': 'col-span-1 sm:col-span-1 lg:col-span-3',
    'three-quarters': 'col-span-1 sm:col-span-2 lg:col-span-9'
  };

  return (
    <div className={cn(spanClasses[span], className)}>
      {children}
    </div>
  );
};

// Responsive Grid Component for Cards
interface ResponsiveGridProps {
  children: React.ReactNode;
  className?: string;
  columns?: {
    xs?: number;
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
    '2xl'?: number;
  };
  gap?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
}

export const ResponsiveGrid: React.FC<ResponsiveGridProps> = ({
  children,
  className,
  columns = { xs: 1, sm: 2, md: 3, lg: 4, xl: 4, '2xl': 5 },
  gap = 'md'
}) => {
  const gapSizes = {
    xs: 'gap-1 sm:gap-2',
    sm: 'gap-2 sm:gap-3',
    md: 'gap-3 sm:gap-4 lg:gap-5',
    lg: 'gap-4 sm:gap-5 lg:gap-6',
    xl: 'gap-5 sm:gap-6 lg:gap-8'
  };

  const gridColumns = cn(
    'grid',
    columns.xs && `grid-cols-${columns.xs}`,
    columns.sm && `sm:grid-cols-${columns.sm}`,
    columns.md && `md:grid-cols-${columns.md}`,
    columns.lg && `lg:grid-cols-${columns.lg}`,
    columns.xl && `xl:grid-cols-${columns.xl}`,
    columns['2xl'] && `2xl:grid-cols-${columns['2xl']}`
  );

  return (
    <div className={cn(gridColumns, gapSizes[gap], className)}>
      {children}
    </div>
  );
};

// Masonry Grid for Dynamic Content
interface MasonryGridProps {
  children: React.ReactNode;
  className?: string;
  columns?: number;
  gap?: number;
}

export const MasonryGrid: React.FC<MasonryGridProps> = ({
  children,
  className,
  columns = 3,
  gap = 16
}) => {
  return (
    <div 
      className={cn("masonry-grid", className)}
      style={{
        columnCount: columns,
        columnGap: `${gap}px`
      }}
    >
      {React.Children.map(children, (child, index) => (
        <div 
          key={index}
          style={{ 
            breakInside: 'avoid',
            marginBottom: `${gap}px`
          }}
        >
          {child}
        </div>
      ))}
    </div>
  );
};

// Auto-fit Grid for Dynamic Number of Items
interface AutoFitGridProps {
  children: React.ReactNode;
  className?: string;
  minItemWidth?: string;
  gap?: string;
}

export const AutoFitGrid: React.FC<AutoFitGridProps> = ({
  children,
  className,
  minItemWidth = '250px',
  gap = '1rem'
}) => {
  return (
    <div 
      className={cn("grid", className)}
      style={{
        gridTemplateColumns: `repeat(auto-fit, minmax(${minItemWidth}, 1fr))`,
        gap
      }}
    >
      {children}
    </div>
  );
};
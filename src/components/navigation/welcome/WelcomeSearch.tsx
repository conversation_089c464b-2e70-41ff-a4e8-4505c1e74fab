import React from 'react';
import { Search, X } from 'lucide-react';
import { Input } from '@/components/ui/input';

interface WelcomeSearchProps {
  searchQuery: string;
  onSearchChange: (value: string) => void;
}

export const WelcomeSearch: React.FC<WelcomeSearchProps> = ({
  searchQuery,
  onSearchChange
}) => {
  return (
    <div className="mb-6">
      <div className="max-w-xl relative">
        <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
        <Input
          placeholder="Search sessions, projects, or commands..."
          value={searchQuery}
          onChange={(e) => onSearchChange(e.target.value)}
          className="pl-10 h-10 bg-background"
          onKeyDown={(e) => {
            if (e.key === 'Escape') onSearchChange('');
          }}
        />
        {searchQuery && (
          <button
            onClick={() => onSearchChange('')}
            className="absolute right-3 top-1/2 -translate-y-1/2 p-1 hover:bg-accent rounded"
          >
            <X className="h-3 w-3" />
          </button>
        )}
      </div>
    </div>
  );
};
import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Activity,
  Wifi,
  WifiOff,
  CheckCircle,
  AlertCircle,
  Server,
  HardDrive,
  Menu,
  X,
  Bell,
  TrendingUp,
  TrendingDown,
  Minus
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { fadeInScale, pulseAnimation } from './animations';

export type WelcomeTabVariant = 'standard' | 'professional';

interface WelcomeHeaderProps {
  systemHealth: any;
  variant?: WelcomeTabVariant;
  notifications?: number;
  onNotificationClick?: () => void;
}

export const WelcomeHeaderEnhanced: React.FC<WelcomeHeaderProps> = ({ 
  systemHealth, 
  variant = 'standard',
  notifications = 0,
  onNotificationClick
}) => {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [prevHealth, setPrevHealth] = useState<number | null>(null);

  useEffect(() => {
    if (prevHealth !== null && systemHealth?.healthScore) {
      // Trigger animation when health changes
    }
    setPrevHealth(systemHealth?.healthScore || null);
  }, [systemHealth?.healthScore]);

  const getStatusColor = (status: string) => {
    const colors = {
      connected: 'bg-green-500/10 text-green-600 dark:bg-green-500/20 border-green-500/20',
      online: 'bg-green-500/10 text-green-600 dark:bg-green-500/20 border-green-500/20',
      slow: 'bg-yellow-500/10 text-yellow-600 dark:bg-yellow-500/20 border-yellow-500/20',
      error: 'bg-red-500/10 text-red-600 dark:bg-red-500/20 border-red-500/20',
      offline: 'bg-red-500/10 text-red-600 dark:bg-red-500/20 border-red-500/20',
      checking: 'bg-gray-500/10 text-gray-600 dark:bg-gray-500/20 border-gray-500/20'
    };
    return colors[status as keyof typeof colors] || colors.checking;
  };

  const getHealthIcon = () => {
    const score = systemHealth?.healthScore || 0;
    if (score >= 80) {
      return <CheckCircle className="w-4 h-4 text-green-600" />;
    } else if (score >= 50) {
      return <AlertCircle className="w-4 h-4 text-yellow-600" />;
    }
    return <AlertCircle className="w-4 h-4 text-red-600" />;
  };

  const getTrendIcon = () => {
    if (!prevHealth || !systemHealth?.healthScore) return null;
    const diff = systemHealth.healthScore - prevHealth;
    if (diff > 0) return <TrendingUp className="w-3 h-3 text-green-600" />;
    if (diff < 0) return <TrendingDown className="w-3 h-3 text-red-600" />;
    return <Minus className="w-3 h-3 text-gray-600" />;
  };

  const StatusBadge: React.FC<{ 
    icon: React.ReactNode; 
    label?: string; 
    value: string; 
    status: string;
    tooltip?: string;
  }> = ({ icon, label, value, status, tooltip }) => (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className={`flex items-center gap-1.5 px-2.5 py-1.5 rounded-lg border transition-all cursor-help ${getStatusColor(status)}`}
          >
            <motion.div
              animate={status === 'checking' ? { rotate: 360 } : {}}
              transition={{ duration: 1, repeat: status === 'checking' ? Infinity : 0, ease: "linear" }}
            >
              {icon}
            </motion.div>
            {label && <span className="font-medium hidden lg:inline text-xs">{label}</span>}
            <span className="text-xs font-mono">{value}</span>
          </motion.div>
        </TooltipTrigger>
        {tooltip && (
          <TooltipContent>
            <p className="text-xs">{tooltip}</p>
          </TooltipContent>
        )}
      </Tooltip>
    </TooltipProvider>
  );

  return (
    <motion.div 
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-background/95 backdrop-blur-sm border-b border-border/50 sticky top-0 z-40"
    >
      {/* Desktop Header */}
      <div className="hidden md:block">
        <div className="container mx-auto px-4 lg:px-6 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3 lg:gap-6">
              <motion.h1 
                className="text-lg font-bold bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent"
                whileHover={{ scale: 1.05 }}
              >
                Claudia
              </motion.h1>
              
              <div className="flex items-center gap-2 lg:gap-3">
                <StatusBadge
                  icon={<Server className="w-3 h-3" />}
                  label="Claude"
                  value={systemHealth?.claudeCodeStatus || 'Checking'}
                  status={systemHealth?.claudeCodeStatus || 'checking'}
                  tooltip="Claude Code connection status"
                />
                
                <StatusBadge
                  icon={systemHealth?.apiConnection === 'online' ? <Wifi className="w-3 h-3" /> : <WifiOff className="w-3 h-3" />}
                  label="API"
                  value={systemHealth?.responseTime ? `${systemHealth.responseTime}ms` : '--'}
                  status={systemHealth?.apiConnection || 'checking'}
                  tooltip={`API Response Time: ${systemHealth?.responseTime || 'N/A'}ms`}
                />
              </div>
            </div>

            <div className="flex items-center gap-3 lg:gap-4">
              <div className="flex items-center gap-3 text-sm">
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <motion.div 
                        className="flex items-center gap-2 px-2 py-1 rounded-lg bg-muted/50"
                        whileHover={{ scale: 1.05 }}
                      >
                        <Activity className="w-3.5 h-3.5 text-muted-foreground" />
                        <span className="font-mono text-xs">{systemHealth?.activeProcesses || 0}</span>
                      </motion.div>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="text-xs">Active Processes</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <motion.div 
                        className="flex items-center gap-2 px-2 py-1 rounded-lg bg-muted/50"
                        whileHover={{ scale: 1.05 }}
                      >
                        <HardDrive className="w-3.5 h-3.5 text-muted-foreground" />
                        <span className="font-mono text-xs">{systemHealth?.diskSpace?.percentage?.toFixed(0) || '--'}%</span>
                      </motion.div>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="text-xs">Storage Used</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <motion.div 
                        className="flex items-center gap-2 px-2 py-1 rounded-lg bg-muted/50"
                        whileHover={{ scale: 1.05 }}
                      >
                        {getHealthIcon()}
                        <span className="font-mono text-xs">{systemHealth?.healthScore || '--'}%</span>
                        {getTrendIcon()}
                      </motion.div>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="text-xs">System Health Score</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>

              {/* Notification Bell */}
              <motion.div whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.9 }}>
                <Button
                  variant="ghost"
                  size="sm"
                  className="relative p-2"
                  onClick={onNotificationClick}
                >
                  <Bell className="h-4 w-4" />
                  {notifications > 0 && (
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      className="absolute -top-1 -right-1"
                    >
                      <Badge 
                        variant="destructive" 
                        className="h-5 w-5 p-0 flex items-center justify-center text-[10px]"
                      >
                        {notifications > 9 ? '9+' : notifications}
                      </Badge>
                    </motion.div>
                  )}
                </Button>
              </motion.div>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile Header */}
      <div className="md:hidden">
        <div className="px-4 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <motion.h1 
                className="text-lg font-bold bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent"
                whileHover={{ scale: 1.05 }}
              >
                Claudia
              </motion.h1>
              <motion.div 
                className="flex items-center gap-2 px-2 py-1 rounded-lg bg-muted/50"
                animate={systemHealth?.healthScore >= 80 ? {} : pulseAnimation}
              >
                {getHealthIcon()}
                <span className="text-sm font-mono">{systemHealth?.healthScore || '--'}%</span>
              </motion.div>
            </div>
            
            <div className="flex items-center gap-2">
              {/* Mobile Notification Bell */}
              <motion.div whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.9 }}>
                <Button
                  variant="ghost"
                  size="sm"
                  className="relative p-2"
                  onClick={onNotificationClick}
                >
                  <Bell className="h-4 w-4" />
                  {notifications > 0 && (
                    <Badge 
                      variant="destructive" 
                      className="absolute -top-1 -right-1 h-4 w-4 p-0 flex items-center justify-center text-[9px]"
                    >
                      {notifications}
                    </Badge>
                  )}
                </Button>
              </motion.div>

              <Button
                variant="ghost"
                size="sm"
                onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
                className="p-2"
              >
                <AnimatePresence mode="wait">
                  {mobileMenuOpen ? (
                    <motion.div
                      key="close"
                      initial={{ rotate: -90 }}
                      animate={{ rotate: 0 }}
                      exit={{ rotate: 90 }}
                    >
                      <X className="h-5 w-5" />
                    </motion.div>
                  ) : (
                    <motion.div
                      key="menu"
                      initial={{ rotate: 90 }}
                      animate={{ rotate: 0 }}
                      exit={{ rotate: -90 }}
                    >
                      <Menu className="h-5 w-5" />
                    </motion.div>
                  )}
                </AnimatePresence>
              </Button>
            </div>
          </div>

          {/* Mobile Menu Dropdown */}
          <AnimatePresence>
            {mobileMenuOpen && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                className="mt-3 space-y-2 pb-2 overflow-hidden"
              >
                <div className="grid grid-cols-2 gap-2">
                  <StatusBadge
                    icon={<Server className="w-3 h-3" />}
                    value={systemHealth?.claudeCodeStatus || 'Checking'}
                    status={systemHealth?.claudeCodeStatus || 'checking'}
                  />
                  <StatusBadge
                    icon={systemHealth?.apiConnection === 'online' ? <Wifi className="w-3 h-3" /> : <WifiOff className="w-3 h-3" />}
                    value={systemHealth?.responseTime ? `${systemHealth.responseTime}ms` : '--'}
                    status={systemHealth?.apiConnection || 'checking'}
                  />
                </div>
                
                <div className="grid grid-cols-2 gap-2">
                  <div className="flex items-center gap-2 px-2 py-1.5 rounded-lg bg-muted/50">
                    <Activity className="w-3.5 h-3.5 text-muted-foreground" />
                    <span className="text-xs">Processes:</span>
                    <span className="font-mono text-xs">{systemHealth?.activeProcesses || 0}</span>
                  </div>
                  <div className="flex items-center gap-2 px-2 py-1.5 rounded-lg bg-muted/50">
                    <HardDrive className="w-3.5 h-3.5 text-muted-foreground" />
                    <span className="text-xs">Storage:</span>
                    <span className="font-mono text-xs">{systemHealth?.diskSpace?.percentage?.toFixed(0) || '--'}%</span>
                  </div>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>
    </motion.div>
  );
};
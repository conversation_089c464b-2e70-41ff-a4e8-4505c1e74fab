import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

interface GettingStartedStep {
  title: string;
  completed: boolean;
  priority: 'high' | 'medium' | 'low';
}

interface GettingStartedProps {
  sessionCount: number;
}

export const GettingStarted: React.FC<GettingStartedProps> = ({ sessionCount }) => {
  const gettingStartedSteps: GettingStartedStep[] = [
    { 
      title: "Create a session", 
      completed: sessionCount > 0,
      priority: "high"
    },
    { 
      title: "Configure MCP servers", 
      completed: false,
      priority: "medium"
    },
    { 
      title: "Create an agent", 
      completed: false,
      priority: "low"
    },
    { 
      title: "Set up TaskMaster", 
      completed: false,
      priority: "low"
    }
  ];

  const completedCount = gettingStartedSteps.filter(step => step.completed).length;
  const progressPercentage = (completedCount / gettingStartedSteps.length) * 100;

  return (
    <Card>
      <CardHeader className="pb-2 sm:pb-3 px-4 sm:px-6">
        <div className="flex items-center justify-between">
          <CardTitle className="text-sm sm:text-base">Getting Started</CardTitle>
          <span className="text-xs text-muted-foreground">
            {completedCount}/{gettingStartedSteps.length} completed
          </span>
        </div>
        {/* Progress bar */}
        <div className="mt-2 h-1.5 bg-muted rounded-full overflow-hidden">
          <div 
            className="h-full bg-primary transition-all duration-300 ease-out"
            style={{ width: `${progressPercentage}%` }}
          />
        </div>
      </CardHeader>
      <CardContent className="px-4 sm:px-6 pb-4 sm:pb-6">
        <div className="space-y-2 sm:space-y-2.5">
          {gettingStartedSteps.map((step, index) => (
            <div key={index} className="flex items-center gap-2 sm:gap-3">
              <div className={`w-4 h-4 sm:w-5 sm:h-5 rounded-full border-2 flex items-center justify-center flex-shrink-0 transition-all ${
                step.completed 
                  ? 'bg-green-500 border-green-500 scale-100' 
                  : 'border-muted-foreground hover:border-primary hover:scale-110'
              }`}>
                {step.completed && (
                  <div className="w-1.5 h-1.5 sm:w-2 sm:h-2 bg-white rounded-full" />
                )}
              </div>
              <span className={`text-xs sm:text-sm flex-1 ${
                step.completed ? 'line-through text-muted-foreground' : ''
              }`}>
                {step.title}
              </span>
              {!step.completed && step.priority === 'high' && (
                <Badge variant="outline" className="text-[10px] sm:text-xs h-5 sm:h-6 px-1.5 sm:px-2">
                  <span className="hidden sm:inline">Recommended</span>
                  <span className="sm:hidden">Rec</span>
                </Badge>
              )}
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};
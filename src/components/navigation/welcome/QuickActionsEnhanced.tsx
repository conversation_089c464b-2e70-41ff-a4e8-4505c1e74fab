import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Bot, Terminal, DollarSign, Settings as SettingsIcon, TrendingUp, Activity, ChevronRight } from 'lucide-react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';

interface QuickAction {
  title: string;
  description: string;
  icon: React.ReactNode;
  onClick: () => void;
  count: number;
  status?: 'active' | 'inactive' | 'warning';
  trend?: number;
  color: string;
}

interface QuickActionsProps {
  createAgentsOverviewTab: () => void;
  createMCPOverviewTab: () => void;
  createTrainingBudgetTab: () => void;
  createSettingsOverviewTab: () => void;
}

export const QuickActionsEnhanced: React.FC<QuickActionsProps> = ({
  createAgentsOverviewTab,
  createMCPOverviewTab,
  createTrainingBudgetTab,
  createSettingsOverviewTab
}) => {
  const [hoveredAction, setHoveredAction] = useState<string | null>(null);
  const [pressedAction, setPressedAction] = useState<string | null>(null);

  const handleActionClick = (action: QuickAction) => {
    setPressedAction(action.title);
    setTimeout(() => {
      try {
        action.onClick();
      } catch (error) {
        console.error(`Failed to execute ${action.title} action:`, error);
      } finally {
        setPressedAction(null);
      }
    }, 150);
  };

  const secondaryActions: QuickAction[] = [
    {
      title: "CC Agents",
      description: "Manage AI agents",
      icon: <Bot className="w-4 h-4 sm:w-5 sm:h-5" />,
      onClick: createAgentsOverviewTab,
      count: 3,
      status: 'active',
      trend: 15,
      color: "from-purple-500 to-pink-500"
    },
    {
      title: "MCP Servers",
      description: "Server connections",
      icon: <Terminal className="w-4 h-4 sm:w-5 sm:h-5" />,
      onClick: createMCPOverviewTab,
      count: 5,
      status: 'active',
      trend: 8,
      color: "from-green-500 to-teal-500"
    },
    {
      title: "Training Budget",
      description: "Usage & costs",
      icon: <DollarSign className="w-4 h-4 sm:w-5 sm:h-5" />,
      onClick: createTrainingBudgetTab,
      count: 0,
      status: 'warning',
      color: "from-yellow-500 to-orange-500"
    },
    {
      title: "Settings",
      description: "Configuration",
      icon: <SettingsIcon className="w-4 h-4 sm:w-5 sm:h-5" />,
      onClick: createSettingsOverviewTab,
      count: 0,
      status: 'inactive',
      color: "from-gray-500 to-slate-500"
    }
  ];

  const getStatusColor = (status?: string) => {
    switch (status) {
      case 'active': return 'bg-green-500';
      case 'warning': return 'bg-yellow-500';
      case 'inactive': return 'bg-gray-400';
      default: return 'bg-gray-400';
    }
  };

  const gridVariants = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.05
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0 }
  };

  return (
    <Card className="overflow-hidden">
      <CardHeader className="pb-3 sm:pb-4 px-4 sm:px-6">
        <div className="flex items-center justify-between">
          <CardTitle className="text-sm sm:text-base font-semibold">Quick Actions</CardTitle>
          <Badge variant="outline" className="text-xs">
            <Activity className="w-3 h-3 mr-1" />
            {secondaryActions.filter(a => a.status === 'active').length} Active
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="px-3 sm:px-6 pb-3 sm:pb-6">
        <motion.div 
          className="grid grid-cols-2 sm:grid-cols-2 lg:grid-cols-4 gap-2 sm:gap-3 lg:gap-4"
          variants={gridVariants}
          initial="hidden"
          animate="show"
        >
          {secondaryActions.map((action) => (
            <motion.button
              key={action.title}
              variants={itemVariants}
              onClick={() => handleActionClick(action)}
              onMouseEnter={() => setHoveredAction(action.title)}
              onMouseLeave={() => setHoveredAction(null)}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className={cn(
                "relative p-3 sm:p-4 rounded-xl border-2 text-left group",
                "transition-all duration-200 touch-manipulation",
                "hover:shadow-lg hover:border-primary/30",
                "focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2",
                hoveredAction === action.title && "bg-accent/50",
                pressedAction === action.title && "ring-2 ring-primary ring-offset-2"
              )}
              aria-label={`Open ${action.title}: ${action.description}`}
            >
              {/* Background Gradient on Hover */}
              <div 
                className={cn(
                  "absolute inset-0 rounded-xl bg-gradient-to-br opacity-0 transition-opacity duration-300",
                  action.color,
                  hoveredAction === action.title && "opacity-5"
                )}
              />

              {/* Status Indicator */}
              <div className="absolute top-3 right-3">
                <div className={cn(
                  "w-2 h-2 rounded-full",
                  getStatusColor(action.status),
                  action.status === 'active' && "animate-pulse"
                )} />
              </div>

              {/* Icon Container */}
              <div className="relative z-10">
                <motion.div 
                  className={cn(
                    "inline-flex p-2 sm:p-2.5 rounded-lg mb-2 sm:mb-3",
                    "bg-gradient-to-br text-white shadow-md",
                    action.color
                  )}
                  animate={hoveredAction === action.title ? { rotate: [0, -5, 5, 0] } : {}}
                  transition={{ duration: 0.3 }}
                >
                  {action.icon}
                </motion.div>

                {/* Title and Description */}
                <div className="space-y-0.5 sm:space-y-1">
                  <p className="text-xs sm:text-sm font-semibold truncate flex items-center gap-1">
                    {action.title}
                    {hoveredAction === action.title && (
                      <motion.span
                        initial={{ opacity: 0, x: -4 }}
                        animate={{ opacity: 1, x: 0 }}
                        className="inline-block"
                      >
                        <ChevronRight className="w-3 h-3 text-muted-foreground" />
                      </motion.span>
                    )}
                  </p>
                  <p className="text-[10px] sm:text-xs text-muted-foreground truncate">
                    {action.description}
                  </p>
                </div>

                {/* Stats Row */}
                <div className="flex items-center justify-between mt-2 sm:mt-3">
                  {action.count > 0 && (
                    <Badge 
                      variant="secondary" 
                      className="text-[10px] sm:text-xs px-1.5 py-0 h-4 sm:h-5"
                    >
                      {action.count}
                    </Badge>
                  )}
                  {action.trend && action.trend > 0 && (
                    <div className="flex items-center gap-0.5 text-green-600">
                      <TrendingUp className="w-3 h-3" />
                      <span className="text-[10px] sm:text-xs font-medium">
                        +{action.trend}%
                      </span>
                    </div>
                  )}
                </div>
              </div>
            </motion.button>
          ))}
        </motion.div>

        {/* Mobile Optimization: Horizontal Scroll Hint */}
        <div className="mt-3 sm:hidden text-center">
          <p className="text-xs text-muted-foreground">Swipe for more actions →</p>
        </div>
      </CardContent>
    </Card>
  );
};
import React from 'react';
import { <PERSON>, Card<PERSON>ontent, <PERSON>Header, CardTitle } from '@/components/ui/card';
import { SystemHealthWidget } from '@/components/dashboard/SystemHealthWidget';
import { TokenUsageWidget } from '@/components/dashboard/TokenUsageWidget';
import { ActivityFeed } from '@/components/dashboard/ActivityFeed';
import { Book, Users } from 'lucide-react';

export const WelcomeSidebar: React.FC = () => {
  const handleResourceClick = (resource: string) => {
    try {
      console.log(`Opening ${resource} resource`);
      // Resource opening logic would go here
    } catch (error) {
      console.error(`Failed to open ${resource}:`, error);
    }
  };

  return (
    <div className="space-y-4 lg:space-y-6">
      {/* Widgets are responsive by design, we just need to handle spacing */}
      <div className="hidden lg:block">
        <SystemHealthWidget />
      </div>
      
      <div className="hidden lg:block">
        <TokenUsageWidget />
      </div>
      
      <div className="hidden lg:block">
        <ActivityFeed />
      </div>
      
      {/* Mobile-first layout: show resources always, widgets only on desktop */}
      <Card className="lg:mt-6">
        <CardHeader className="pb-2 sm:pb-3 px-4 sm:px-6">
          <CardTitle className="text-sm sm:text-base">Resources</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2 px-3 sm:px-6 pb-3 sm:pb-6">
          <button 
            onClick={() => handleResourceClick('documentation')}
            className="w-full p-2.5 sm:p-3 rounded-lg border hover:bg-accent transition-colors text-left group active:scale-[0.98] touch-manipulation"
            aria-label="Open documentation"
          >
            <div className="flex items-start gap-2">
              <Book className="w-4 h-4 text-muted-foreground group-hover:text-foreground mt-0.5" />
              <div className="flex-1">
                <p className="text-xs sm:text-sm font-medium">Documentation</p>
                <p className="text-[10px] sm:text-xs text-muted-foreground">Read the guide</p>
              </div>
            </div>
          </button>
          <button 
            onClick={() => handleResourceClick('community')}
            className="w-full p-2.5 sm:p-3 rounded-lg border hover:bg-accent transition-colors text-left group active:scale-[0.98] touch-manipulation"
            aria-label="Join community"
          >
            <div className="flex items-start gap-2">
              <Users className="w-4 h-4 text-muted-foreground group-hover:text-foreground mt-0.5" />
              <div className="flex-1">
                <p className="text-xs sm:text-sm font-medium">Community</p>
                <p className="text-[10px] sm:text-xs text-muted-foreground">Join discussion</p>
              </div>
            </div>
          </button>
        </CardContent>
      </Card>

      {/* Show widgets on mobile in a collapsed/simplified view */}
      <div className="lg:hidden space-y-3">
        <SystemHealthWidget />
        <TokenUsageWidget />
      </div>
    </div>
  );
};
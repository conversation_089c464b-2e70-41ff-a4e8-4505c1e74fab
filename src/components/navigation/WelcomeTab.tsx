import React, { useState, useCallback, useEffect } from 'react';
import { AlertCircle } from 'lucide-react';
import { type Session } from '@/lib/api';
import { useTabState } from '@/hooks/useTabState';
import { useDataFetch } from '@/hooks/useDataFetch';
import { useSystemHealth, useWelcomeData } from '@/hooks/useWelcomeData';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import {
  WelcomeHeaderEnhanced as WelcomeHeader,
  WelcomeSearch,
  PrimaryActionsEnhanced as PrimaryActions,
  QuickActionsEnhanced as QuickActions,
  RecentSessions,
  GettingStartedEnhanced as GettingStarted,
  WelcomeSidebar,
  type WelcomeTabVariant
} from './welcome';
import { ErrorBoundary } from './welcome/ErrorBoundary';
import './welcome/styles.css';

export type { WelcomeTabVariant };

interface WelcomeTabProps {
  onNewSession: () => void;
  variant?: WelcomeTabVariant;
}

interface ErrorState {
  message: string;
  code?: string;
  retry?: () => void;
}

export const WelcomeTab: React.FC<WelcomeTabProps> = ({ 
  onNewSession, 
  variant = 'standard' 
}) => {
  const {
    createAgentsOverviewTab,
    createMCPOverviewTab,
    createSettingsOverviewTab,
    createTrainingBudgetTab,
    createTaskMasterTab,
    createProjectsTab,
    updateTab,
    createChatTab
  } = useTabState();

  const [searchQuery, setSearchQuery] = useState('');
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [globalError, setGlobalError] = useState<ErrorState | null>(null);
  const [componentErrors, setComponentErrors] = useState<Record<string, boolean>>({});

  const fetchSystemHealth = useSystemHealth();
  const { fetchRecentSessions } = useWelcomeData();

  const { 
    data: systemHealth, 
    error: healthError,
    refetch: refetchHealth 
  } = useDataFetch(fetchSystemHealth, [], {
    cacheTime: 60000,
    retryCount: 2,
    onError: (error) => {
      console.error('Failed to fetch system health:', error);
    }
  });

  const { 
    data: recentSessions, 
    loading, 
    error: sessionsError,
    refetch: refetchSessions 
  } = useDataFetch(fetchRecentSessions, [], {
    cacheTime: 60000,
    retryCount: 2,
    onError: (error) => {
      console.error('Failed to fetch recent sessions:', error);
    }
  });

  // Error recovery mechanism
  useEffect(() => {
    if (healthError && !componentErrors.health) {
      setComponentErrors(prev => ({ ...prev, health: true }));
      // Retry after 5 seconds
      const timer = setTimeout(() => {
        refetchHealth();
        setComponentErrors(prev => ({ ...prev, health: false }));
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [healthError, componentErrors.health, refetchHealth]);

  const handleOpenSession = useCallback((session: Session) => {
    try {
      const projectName = session.project_path.split('/').pop() || 'Session';
      const newTabId = createChatTab(session.id, projectName);
      updateTab(newTabId, {
        sessionData: session,
        initialProjectPath: session.project_path
      });
    } catch (error) {
      console.error('Failed to open session:', error);
      setGlobalError({
        message: 'Failed to open session. Please try again.',
        code: 'SESSION_OPEN_ERROR',
        retry: () => {
          setGlobalError(null);
          handleOpenSession(session);
        }
      });
    }
  }, [createChatTab, updateTab]);

  const handleRefresh = useCallback(async () => {
    try {
      setIsRefreshing(true);
      await refetchSessions();
    } catch (error) {
      console.error('Failed to refresh sessions:', error);
      setGlobalError({
        message: 'Failed to refresh sessions. Please check your connection.',
        code: 'REFRESH_ERROR',
        retry: handleRefresh
      });
    } finally {
      setTimeout(() => setIsRefreshing(false), 500);
    }
  }, [refetchSessions]);

  const handleComponentError = useCallback((componentName: string, error: Error) => {
    console.error(`Error in ${componentName}:`, error);
    setComponentErrors(prev => ({ ...prev, [componentName]: true }));
  }, []);

  const safeCreateTab = useCallback((createFn: () => void, tabName: string) => {
    return () => {
      try {
        createFn();
      } catch (error) {
        console.error(`Failed to create ${tabName} tab:`, error);
        setGlobalError({
          message: `Failed to open ${tabName}. Please try again.`,
          code: 'TAB_CREATE_ERROR',
          retry: () => {
            setGlobalError(null);
            createFn();
          }
        });
      }
    };
  }, []);

  return (
    <div className="h-full overflow-y-auto bg-background">
      {globalError && (
        <div className="sticky top-0 z-50 p-4 bg-background border-b">
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription className="flex items-center justify-between">
              <span>{globalError.message}</span>
              {globalError.retry && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={globalError.retry}
                  className="ml-4"
                >
                  Retry
                </Button>
              )}
            </AlertDescription>
          </Alert>
        </div>
      )}

      <ErrorBoundary 
        componentName="WelcomeHeader"
        onError={(error) => handleComponentError('header', error)}
      >
        <WelcomeHeader 
          systemHealth={systemHealth} 
          variant={variant}
          notifications={3}
          onNotificationClick={() => console.log('Notifications clicked')}
        />
      </ErrorBoundary>

      <div className="container mx-auto px-4 sm:px-6 py-4 sm:py-6 max-w-7xl">
        <ErrorBoundary componentName="WelcomeSearch">
          <WelcomeSearch 
            searchQuery={searchQuery} 
            onSearchChange={setSearchQuery} 
          />
        </ErrorBoundary>

        <ErrorBoundary componentName="PrimaryActions">
          <PrimaryActions
            onNewSession={onNewSession}
            createTaskMasterTab={safeCreateTab(createTaskMasterTab, 'TaskMaster')}
            sessionCount={recentSessions?.length || 0}
          />
        </ErrorBoundary>

        <div className="grid grid-cols-1 lg:grid-cols-12 gap-4 lg:gap-6">
          <div className="lg:col-span-8 space-y-4 lg:space-y-6">
            <ErrorBoundary componentName="QuickActions">
              <QuickActions
                createAgentsOverviewTab={safeCreateTab(createAgentsOverviewTab, 'Agents')}
                createMCPOverviewTab={safeCreateTab(createMCPOverviewTab, 'MCP')}
                createTrainingBudgetTab={safeCreateTab(createTrainingBudgetTab, 'Training Budget')}
                createSettingsOverviewTab={safeCreateTab(createSettingsOverviewTab, 'Settings')}
              />
            </ErrorBoundary>

            <ErrorBoundary componentName="RecentSessions">
              <RecentSessions
                sessions={recentSessions}
                loading={loading}
                error={sessionsError}
                isRefreshing={isRefreshing}
                onOpenSession={handleOpenSession}
                onNewSession={onNewSession}
                onRefresh={handleRefresh}
                onViewAll={safeCreateTab(createProjectsTab, 'Projects')}
                refetchSessions={refetchSessions}
              />
            </ErrorBoundary>

            <ErrorBoundary componentName="GettingStarted">
              <GettingStarted sessionCount={recentSessions?.length || 0} />
            </ErrorBoundary>
          </div>

          <div className="lg:col-span-4">
            <ErrorBoundary componentName="WelcomeSidebar">
              <WelcomeSidebar />
            </ErrorBoundary>
          </div>
        </div>
      </div>
    </div>
  );
};

export const UnifiedWelcomeTab = WelcomeTab;
export const ProfessionalWelcomeTab = WelcomeTab;

export default WelcomeTab;
// Prevents additional console window on Windows in release, DO NOT REMOVE!!
#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

mod checkpoint;
mod claude_binary;
// mod commands; // Use from lib instead
// mod process; // Use from lib instead
// mod db; // Use from lib instead
mod mock_data;
mod usage_tracker;
mod training;
mod context_watcher;
mod mcp;
mod workflow;

use checkpoint::state::CheckpointState;
use claudia_lib::process::ProcessRegistryState;
use std::sync::{Arc, Mutex};
use tokio::sync::RwLock;
use tauri::Manager;
use context_watcher::ContextWatcherState;

// Import commands directly from their modules

// Import all other commands from the crate root (re-exported in lib.rs)
use claudia_lib::{
    // Agent commands
    init_database, AgentDb, ClaudeProcessState, apply_proxy_settings, ProxySettings, TaskMasterState,
    
    // State types
    ModelSwitcherState, SummarizerState, ErrorRecoveryState, TrainingState, initialize_enhanced_mcp, initialize_workflow,
};

// Import non-command types from specific modules
use claudia_lib::commands::conversation_summary::ConversationSummarizer;
use claudia_lib::commands::error_recovery::ErrorRecoverySystem;
use claudia_lib::commands::model_switcher::SmartModelSwitcher;

fn main() {
    // Initialize logger
    env_logger::init();


    tauri::Builder::default()
        .plugin(tauri_plugin_dialog::init())
        .plugin(tauri_plugin_shell::init())
        .setup(|app| {
            // Initialize agents database
            let conn = init_database(&app.handle()).expect("Failed to initialize agents database");
            
            // Initialize training budget DB
            let app_dir = app
                .path()
                .app_data_dir()
                .map_err(|e| anyhow::anyhow!(e))
                .expect("Failed to resolve app data dir");
            claudia_lib::db::init_db(app_dir).expect("Failed to init training budget DB");
            
            // Load and apply proxy settings from the database
            {
                let db = AgentDb(Mutex::new(conn));
                let proxy_settings = match db.0.lock() {
                    Ok(conn) => {
                        // Directly query proxy settings from the database
                        let mut settings = ProxySettings::default();
                        
                        let keys = vec![
                            ("proxy_enabled", "enabled"),
                            ("proxy_http", "http_proxy"),
                            ("proxy_https", "https_proxy"),
                            ("proxy_no", "no_proxy"),
                            ("proxy_all", "all_proxy"),
                        ];
                        
                        for (db_key, field) in keys {
                            if let Ok(value) = conn.query_row(
                                "SELECT value FROM app_settings WHERE key = ?1",
                                rusqlite::params![db_key],
                                |row| row.get::<_, String>(0),
                            ) {
                                match field {
                                    "enabled" => settings.enabled = value == "true",
                                    "http_proxy" => settings.http_proxy = Some(value).filter(|s: &String| !s.is_empty()),
                                    "https_proxy" => settings.https_proxy = Some(value).filter(|s: &String| !s.is_empty()),
                                    "no_proxy" => settings.no_proxy = Some(value).filter(|s: &String| !s.is_empty()),
                                    "all_proxy" => settings.all_proxy = Some(value).filter(|s: &String| !s.is_empty()),
                                    _ => {}
                                }
                            }
                        }
                        
                        log::info!("Loaded proxy settings: enabled={}", settings.enabled);
                        settings
                    }
                    Err(e) => {
                        log::warn!("Failed to lock database for proxy settings: {}", e);
                        ProxySettings::default()
                    }
                };
                
                // Apply the proxy settings
                apply_proxy_settings(&proxy_settings);
            }
            
            // Re-open the connection for the app to manage
            let conn = init_database(&app.handle()).expect("Failed to initialize agents database");
            app.manage(AgentDb(Mutex::new(conn)));

            // Initialize checkpoint state
            let checkpoint_state = CheckpointState::new();

            // Set the Claude directory path
            if let Ok(claude_dir) = dirs::home_dir()
                .ok_or_else(|| "Could not find home directory")
                .and_then(|home| {
                    let claude_path = home.join(".claude");
                    claude_path
                        .canonicalize()
                        .map_err(|_| "Could not find ~/.claude directory")
                })
            {
                let state_clone = checkpoint_state.clone();
                tauri::async_runtime::spawn(async move {
                    state_clone.set_claude_dir(claude_dir).await;
                });
            }

            app.manage(checkpoint_state);

            // Initialize process registry
            app.manage(ProcessRegistryState::default());

            // Initialize Claude process state
            app.manage(ClaudeProcessState::default());
            
            // Initialize Task Master state
            app.manage(TaskMasterState::default());
            
            // Initialize Model Switcher state
            app.manage(Arc::new(RwLock::new(SmartModelSwitcher::default())) as ModelSwitcherState);
            
            // Initialize Conversation Summary state  
            app.manage(Arc::new(RwLock::new(ConversationSummarizer::default())) as SummarizerState);
            
            // Initialize Error Recovery state
            app.manage(Arc::new(RwLock::new(ErrorRecoverySystem::default())) as ErrorRecoveryState);

            // Initialize Training state
            app.manage(TrainingState::new());
            
            // Initialize Health Monitor state
            app.manage(Arc::new(RwLock::new(None)) as claudia_lib::commands::health_monitor_commands::HealthMonitorState);
            
            // Initialize Workflow Executor state
            app.manage(Arc::new(RwLock::new(None)) as claudia_lib::commands::workflow_executor_commands::WorkflowExecutorState);
            
            // Initialize Git Hook Manager state
            app.manage(Arc::new(RwLock::new(None)) as claudia_lib::commands::git_hook_commands::GitHookManagerState);
            
            // Initialize Agent Orchestrator state
            app.manage(Arc::new(RwLock::new(None)) as claudia_lib::commands::agent_orchestration_commands::AgentOrchestratorState);

            // Initialize Context Watcher state
            let context_watcher_state = ContextWatcherState::new();
            context_watcher_state.initialize(app.handle().clone());
            app.manage(context_watcher_state);

            // Initialize Enhanced MCP state
            let enhanced_mcp_state = tauri::async_runtime::block_on(
                initialize_enhanced_mcp(&app.handle())
            ).expect("Failed to initialize Enhanced MCP");
            app.manage(enhanced_mcp_state);

            // Initialize Workflow state
            let workflow_state = tauri::async_runtime::block_on(
                initialize_workflow(&app.handle())
            ).expect("Failed to initialize Workflow");
            app.manage(workflow_state);

            Ok(())
        })
        .invoke_handler(tauri::generate_handler![
            // Claude & Project Management
            claudia_lib::commands::claude::list_projects,
            
            // Training Budget
            claudia_lib::commands::budget::get_budget,
            claudia_lib::commands::budget::set_budget,
            claudia_lib::commands::budget::get_stats,
            claudia_lib::commands::budget::list_requests,
            claudia_lib::commands::budget::create_request,
            claudia_lib::commands::budget::update_request,
            claudia_lib::commands::budget::delete_request,
            claudia_lib::commands::budget::approve_request,
            claudia_lib::commands::budget::reject_request,
            // Expenses (database-backed)
            claudia_lib::commands::expenses::get_expenses,
            claudia_lib::commands::expenses::add_expense,
            claudia_lib::commands::expenses::update_expense,
            claudia_lib::commands::expenses::delete_expense,
            claudia_lib::commands::expenses::update_expense_status,
            // Basic Training Commands (keeping existing)
            claudia_lib::commands::training::list_training_needs, 
            claudia_lib::commands::training::create_training_need, 
            claudia_lib::commands::training::update_training_need, 
            claudia_lib::commands::training::delete_training_need,
            claudia_lib::commands::training::list_training_programs, 
            claudia_lib::commands::training::create_training_program, 
            claudia_lib::commands::training::update_training_program, 
            claudia_lib::commands::training::delete_training_program,
            claudia_lib::commands::training::list_training_analyses, 
            claudia_lib::commands::training::create_training_analysis, 
            claudia_lib::commands::training::update_training_analysis, 
            claudia_lib::commands::training::delete_training_analysis,
            
            // Enhanced Training Commands
            claudia_lib::commands::training_enhanced::create_training_need_enhanced,
            claudia_lib::commands::training_enhanced::get_training_needs_enhanced,
            claudia_lib::commands::training_enhanced::update_training_need_enhanced,
            claudia_lib::commands::training_enhanced::delete_training_need_enhanced,
            claudia_lib::commands::training_enhanced::analyze_skills_gap,
            claudia_lib::commands::training_enhanced::get_skill_recommendations,
            claudia_lib::commands::training_enhanced::create_training_program_enhanced,
            claudia_lib::commands::training_enhanced::get_training_programs_enhanced,
            claudia_lib::commands::training_enhanced::match_programs_to_needs,
            claudia_lib::commands::training_enhanced::create_learning_path,
            claudia_lib::commands::training_enhanced::update_module_progress,
            claudia_lib::commands::training_enhanced::get_learning_paths,
            claudia_lib::commands::training_enhanced::create_approval_workflow,
            claudia_lib::commands::training_enhanced::process_approval,
            claudia_lib::commands::training_enhanced::get_pending_approvals,
            claudia_lib::commands::training_enhanced::get_training_metrics,
            claudia_lib::commands::training_enhanced::get_department_metrics,
            claudia_lib::commands::training_enhanced::get_roi_analysis,
            claudia_lib::commands::training_enhanced::import_training_needs_csv,
            claudia_lib::commands::training_enhanced::export_training_needs_csv,
            claudia_lib::commands::training_enhanced::generate_csv_template,
            claudia_lib::commands::training_enhanced::batch_create_training_needs,
            claudia_lib::commands::training_enhanced::batch_update_status,
            claudia_lib::commands::training_enhanced::search_training_needs,
            claudia_lib::commands::training_enhanced::get_training_suggestions,
            claudia_lib::commands::training_enhanced::send_training_reminder,
            claudia_lib::commands::training_enhanced::schedule_training_notifications, 
            claudia_lib::commands::claude::get_project_sessions,
            claudia_lib::commands::claude::get_claude_settings,
            claudia_lib::commands::claude::open_new_session,
            claudia_lib::commands::claude::get_system_prompt,
            claudia_lib::commands::claude::check_claude_version,
            claudia_lib::commands::claude::save_system_prompt,
            claudia_lib::commands::claude::save_claude_settings,
            claudia_lib::commands::claude::find_claude_md_files,
            claudia_lib::commands::claude::read_claude_md_file,
            claudia_lib::commands::claude::save_claude_md_file,
            claudia_lib::commands::claude::load_session_history,
            claudia_lib::commands::claude::execute_claude_code,
            claudia_lib::commands::claude::continue_claude_code,
            claudia_lib::commands::claude::resume_claude_code,
            claudia_lib::commands::claude::cancel_claude_execution,
            claudia_lib::commands::claude::list_running_claude_sessions,
            claudia_lib::commands::claude::get_claude_session_output,
            claudia_lib::commands::claude::list_directory_contents,
            claudia_lib::commands::claude::search_files,
            claudia_lib::commands::claude::get_recently_modified_files,
            claudia_lib::commands::claude::get_hooks_config,
            claudia_lib::commands::claude::update_hooks_config,
            claudia_lib::commands::claude::validate_hook_command,
            
            // Context System Commands
            claudia_lib::commands::claude::discover_claude_contexts,
            claudia_lib::commands::claude::get_context_templates,
            claudia_lib::commands::claude::load_project_context,
            claudia_lib::commands::claude::create_context_from_template,
            claudia_lib::commands::claude::get_context_history,
            claudia_lib::commands::claude::update_context_inheritance,
            claudia_lib::commands::claude::initialize_context_templates,
            
            // Checkpoint Management
            claudia_lib::commands::claude::create_checkpoint,
            claudia_lib::commands::claude::restore_checkpoint,
            claudia_lib::commands::claude::list_checkpoints,
            claudia_lib::commands::claude::fork_from_checkpoint,
            claudia_lib::commands::claude::get_session_timeline,
            claudia_lib::commands::claude::update_checkpoint_settings,
            claudia_lib::commands::claude::get_checkpoint_diff,
            claudia_lib::commands::claude::track_checkpoint_message,
            claudia_lib::commands::claude::track_session_messages,
            claudia_lib::commands::claude::check_auto_checkpoint,
            claudia_lib::commands::claude::cleanup_old_checkpoints,
            claudia_lib::commands::claude::get_checkpoint_settings,
            claudia_lib::commands::claude::clear_checkpoint_manager,
            claudia_lib::commands::claude::get_checkpoint_state_stats,
            
            // Agent Management
            claudia_lib::commands::agents::list_agents,
            claudia_lib::commands::agents::create_agent,
            claudia_lib::commands::agents::update_agent,
            claudia_lib::commands::agents::delete_agent,
            claudia_lib::commands::agents::get_agent,
            claudia_lib::commands::agents::execute_agent,
            claudia_lib::commands::agents::list_agent_runs,
            claudia_lib::commands::agents::get_agent_run,
            claudia_lib::commands::agents::list_agent_runs_with_metrics,
            claudia_lib::commands::agents::get_agent_run_with_real_time_metrics,
            claudia_lib::commands::agents::list_running_sessions,
            claudia_lib::commands::agents::kill_agent_session,
            claudia_lib::commands::agents::get_session_status,
            claudia_lib::commands::agents::cleanup_finished_processes,
            claudia_lib::commands::agents::get_session_output,
            claudia_lib::commands::agents::get_live_session_output,
            claudia_lib::commands::agents::stream_session_output,
            claudia_lib::commands::agents::load_agent_session_history,
            claudia_lib::commands::agents::get_claude_binary_path,
            claudia_lib::commands::agents::set_claude_binary_path,
            claudia_lib::commands::agents::list_claude_installations,
            claudia_lib::commands::agents::list_processes,
            claudia_lib::commands::agents::export_agent,
            claudia_lib::commands::agents::export_agent_to_file,
            claudia_lib::commands::agents::import_agent,
            claudia_lib::commands::agents::import_agent_from_file,
            claudia_lib::commands::agents::fetch_github_agents,
            claudia_lib::commands::agents::fetch_github_agent_content,
            claudia_lib::commands::agents::import_agent_from_github,
            
            // Usage & Analytics
            claudia_lib::commands::usage::get_usage_stats,
            claudia_lib::commands::usage::get_usage_by_date_range,
            claudia_lib::commands::usage::get_usage_details,
            claudia_lib::commands::usage::get_session_stats,
            claudia_lib::commands::usage::get_model_usage_info,
            claudia_lib::commands::usage::update_model_usage_settings,
            claudia_lib::commands::usage::reset_model_usage,
            claudia_lib::commands::usage::get_model_to_use,
            
            // MCP (Model Context Protocol)
            claudia_lib::commands::mcp::mcp_add,
            claudia_lib::commands::mcp::mcp_list,
            claudia_lib::commands::mcp::mcp_get,
            claudia_lib::commands::mcp::mcp_remove,
            claudia_lib::commands::mcp::mcp_add_json,
            claudia_lib::commands::mcp::mcp_add_from_claude_desktop,
            claudia_lib::commands::mcp::mcp_serve,
            claudia_lib::commands::mcp::mcp_test_connection,
            claudia_lib::commands::mcp::mcp_reset_project_choices,
            claudia_lib::commands::mcp::mcp_get_server_status,
            claudia_lib::commands::mcp::mcp_read_project_config,
            claudia_lib::commands::mcp::mcp_save_project_config,
            
            // Storage Management
            claudia_lib::commands::storage::storage_list_tables,
            claudia_lib::commands::storage::storage_read_table,
            claudia_lib::commands::storage::storage_update_row,
            claudia_lib::commands::storage::storage_delete_row,
            claudia_lib::commands::storage::storage_insert_row,
            claudia_lib::commands::storage::storage_execute_sql,
            claudia_lib::commands::storage::storage_reset_database,
            
            // Slash Commands
            claudia_lib::commands::slash_commands::slash_commands_list,
            claudia_lib::commands::slash_commands::slash_command_get,
            claudia_lib::commands::slash_commands::slash_command_save,
            claudia_lib::commands::slash_commands::slash_command_delete,
            
            // Enhanced Slash Commands
            claudia_lib::commands::slash_commands::enhanced_slash_commands_list,
            claudia_lib::commands::slash_commands::get_command_categories,
            claudia_lib::commands::slash_commands::execute_enhanced_command,
            claudia_lib::commands::slash_commands::execute_enhanced_command_with_context,
            claudia_lib::commands::slash_commands::get_context_aware_suggestions,
            
            // Proxy Settings
            claudia_lib::commands::proxy::get_proxy_settings,
            claudia_lib::commands::proxy::save_proxy_settings,
            
            // Budget Extended Commands (Real Backend)
            claudia_lib::commands::budget_extended::get_quarterly_allocations,
            claudia_lib::commands::budget_extended::update_quarterly_allocations,
            claudia_lib::commands::budget_extended::get_department_allocations,
            claudia_lib::commands::budget_extended::update_department_allocation,
            claudia_lib::commands::budget_extended::add_department,
            claudia_lib::commands::budget_extended::remove_department,
            claudia_lib::commands::budget_extended::get_category_limits,
            claudia_lib::commands::budget_extended::set_category_limit,
            claudia_lib::commands::budget_extended::remove_category_limit,
            // Commented out mock_api expense commands - using database-backed ones instead
            // get_expenses,
            // add_expense,
            // update_expense,
            // delete_expense,
            // update_expense_status,
            claudia_lib::commands::budget_extended::get_allocation_rules,
            claudia_lib::commands::budget_extended::add_allocation_rule,
            claudia_lib::commands::budget_extended::update_allocation_rule,
            claudia_lib::commands::budget_extended::delete_allocation_rule,
            claudia_lib::commands::budget_extended::toggle_allocation_rule,
            claudia_lib::commands::budget_extended::execute_allocation_rule,
            
            // Mock API Commands (for features still using mock)
            claudia_lib::commands::mock_api::get_budget_analytics,
            claudia_lib::commands::mock_api::get_budget_templates,
            claudia_lib::commands::mock_api::save_budget_template,
            claudia_lib::commands::mock_api::apply_budget_template,
            claudia_lib::commands::mock_api::delete_budget_template,
            claudia_lib::commands::mock_api::export_analytics,
            
            // Task Master Commands
            claudia_lib::commands::taskmaster::load_taskmaster_file,
            claudia_lib::commands::taskmaster::save_taskmaster_file,
            claudia_lib::commands::taskmaster::create_taskmaster_file,
            claudia_lib::commands::taskmaster::list_taskmaster_files,
            claudia_lib::commands::taskmaster::validate_taskmaster_file,
            claudia_lib::commands::taskmaster::export_taskmaster_csv,
            claudia_lib::commands::taskmaster::export_taskmaster_markdown,
            claudia_lib::commands::taskmaster::watch_taskmaster_file,
            claudia_lib::commands::taskmaster::stop_watching_taskmaster_file,
            
            // Task Master AI Commands
            claudia_lib::commands::taskmaster_ai::execute_task_with_ai,
            claudia_lib::commands::taskmaster_ai::continue_task_execution,
            claudia_lib::commands::taskmaster_ai::analyze_task_with_ai,
            claudia_lib::commands::taskmaster_ai::generate_subtasks_with_ai,
            claudia_lib::commands::taskmaster_ai::optimize_task_schedule_with_ai,
            
            // Model Switching Commands
            claudia_lib::commands::model_switcher_commands::analyze_task_complexity,
            claudia_lib::commands::model_switcher_commands::get_model_recommendation,
            claudia_lib::commands::model_switcher_commands::should_switch_model,
            claudia_lib::commands::model_switcher_commands::record_task_result,
            claudia_lib::commands::model_switcher_commands::get_model_switching_config,
            claudia_lib::commands::model_switcher_commands::update_model_switching_config,
            claudia_lib::commands::model_switcher_commands::get_model_performance_stats,
            claudia_lib::commands::model_switcher_commands::get_task_history,
            claudia_lib::commands::model_switcher_commands::clear_task_history,
            claudia_lib::commands::model_switcher_commands::get_smart_model_for_prompt,
            claudia_lib::commands::model_switcher_commands::get_model_switch_explanation,
            
            // Conversation Summary Commands
            claudia_lib::commands::conversation_summary_commands::generate_conversation_summary,
            claudia_lib::commands::conversation_summary_commands::get_conversation_summaries,
            claudia_lib::commands::conversation_summary_commands::add_message_to_summary,
            claudia_lib::commands::conversation_summary_commands::get_summary_config,
            claudia_lib::commands::conversation_summary_commands::update_summary_config,
            claudia_lib::commands::conversation_summary_commands::get_current_session_metrics,
            claudia_lib::commands::conversation_summary_commands::create_new_summarizer_session,
            
            // Error Recovery Commands
            claudia_lib::commands::error_recovery_commands::attempt_error_recovery,
            claudia_lib::commands::error_recovery_commands::get_error_recovery_config,
            claudia_lib::commands::error_recovery_commands::update_error_recovery_config,
            claudia_lib::commands::error_recovery_commands::get_recovery_statistics,
            claudia_lib::commands::error_recovery_commands::get_recovery_history,
            claudia_lib::commands::error_recovery_commands::clear_recovery_history,
            
            // Context Watcher Commands
            context_watcher::start_context_watching,
            context_watcher::stop_context_watching,
            context_watcher::get_watched_projects,
            
            // Enhanced MCP Commands
            claudia_lib::commands::enhanced_mcp::get_enhanced_mcp_servers,
            claudia_lib::commands::enhanced_mcp::create_enhanced_mcp_server,
            claudia_lib::commands::enhanced_mcp::start_mcp_server,
            claudia_lib::commands::enhanced_mcp::stop_mcp_server,
            claudia_lib::commands::enhanced_mcp::delete_mcp_server,
            claudia_lib::commands::enhanced_mcp::get_mcp_server_health,
            claudia_lib::commands::enhanced_mcp::update_mcp_performance_metrics,
            claudia_lib::commands::enhanced_mcp::get_mcp_analytics,
            claudia_lib::commands::enhanced_mcp::record_mcp_tool_usage,
            claudia_lib::commands::enhanced_mcp::mcp_memory_store,
            claudia_lib::commands::enhanced_mcp::mcp_memory_retrieve,
            claudia_lib::commands::enhanced_mcp::mcp_memory_delete,
            claudia_lib::commands::enhanced_mcp::mcp_memory_query,
            claudia_lib::commands::enhanced_mcp::mcp_memory_cleanup_expired,
            claudia_lib::commands::enhanced_mcp::mcp_knowledge_add_node,
            claudia_lib::commands::enhanced_mcp::mcp_knowledge_get_node,
            claudia_lib::commands::enhanced_mcp::mcp_knowledge_query_nodes,
            claudia_lib::commands::enhanced_mcp::mcp_knowledge_export,
            claudia_lib::commands::enhanced_mcp::mcp_knowledge_import,
            claudia_lib::commands::enhanced_mcp::generate_mcp_tests,
            claudia_lib::commands::enhanced_mcp::run_mcp_coverage_analysis,
            
            // Workflow Commands
            claudia_lib::commands::workflow::create_workflow,
            claudia_lib::commands::workflow::get_workflow,
            claudia_lib::commands::workflow::list_workflows,
            claudia_lib::commands::workflow::update_workflow,
            claudia_lib::commands::workflow::delete_workflow,
            claudia_lib::commands::workflow::execute_workflow,
            claudia_lib::commands::workflow::get_execution,
            claudia_lib::commands::workflow::list_executions,
            claudia_lib::commands::workflow::cancel_execution,
            claudia_lib::commands::workflow::create_template,
            claudia_lib::commands::workflow::get_template,
            claudia_lib::commands::workflow::list_templates,
            claudia_lib::commands::workflow::search_templates,
            claudia_lib::commands::workflow::instantiate_template,
            claudia_lib::commands::workflow::update_template,
            claudia_lib::commands::workflow::delete_template,
            claudia_lib::commands::workflow::export_template,
            claudia_lib::commands::workflow::import_template,
            claudia_lib::commands::workflow::get_popular_templates,
            claudia_lib::commands::workflow::get_template_categories,
            claudia_lib::commands::workflow::create_trigger,
            claudia_lib::commands::workflow::list_triggers,
            claudia_lib::commands::workflow::stop_trigger,
            claudia_lib::commands::workflow::delete_trigger,
            claudia_lib::commands::workflow::manual_trigger_workflow,
            claudia_lib::commands::workflow::create_workflow_from_template,
            claudia_lib::commands::workflow::validate_workflow,
            claudia_lib::commands::workflow::get_workflow_statistics,
            
            // Mock Data Commands
            claudia_lib::commands::mock_data_commands::generate_mock_budget_data,
            claudia_lib::commands::mock_data_commands::generate_mock_training_data,
            claudia_lib::commands::mock_data_commands::generate_mock_expenses_data,
            claudia_lib::commands::mock_data_commands::generate_mock_department_allocations,
            claudia_lib::commands::mock_data_commands::generate_mock_quarterly_allocations,
            claudia_lib::commands::mock_data_commands::generate_mock_category_limits,
            claudia_lib::commands::mock_data_commands::generate_mock_allocation_rules,
            claudia_lib::commands::mock_data_commands::get_mock_analytics,
            
            // Health Monitor Commands
            claudia_lib::commands::health_monitor_commands::start_health_monitoring,
            claudia_lib::commands::health_monitor_commands::stop_health_monitoring,
            claudia_lib::commands::health_monitor_commands::get_server_health,
            claudia_lib::commands::health_monitor_commands::get_all_servers_health,
            claudia_lib::commands::health_monitor_commands::perform_health_check,
            claudia_lib::commands::health_monitor_commands::reset_health_statistics,
            claudia_lib::commands::health_monitor_commands::set_health_check_interval,
            
            // Workflow Executor Commands
            claudia_lib::commands::workflow_executor_commands::init_workflow_executor,
            claudia_lib::commands::workflow_executor_commands::execute_workflow_async,
            claudia_lib::commands::workflow_executor_commands::get_workflow_executor_status,
            claudia_lib::commands::workflow_executor_commands::stop_workflow_execution,
            claudia_lib::commands::workflow_executor_commands::pause_workflow_execution,
            claudia_lib::commands::workflow_executor_commands::resume_workflow_execution,
            claudia_lib::commands::workflow_executor_commands::get_running_workflows,
            
            // Git Hook Commands
            claudia_lib::commands::git_hook_commands::init_git_hook_manager,
            claudia_lib::commands::git_hook_commands::create_git_hook,
            claudia_lib::commands::git_hook_commands::install_git_hook,
            claudia_lib::commands::git_hook_commands::uninstall_git_hook,
            claudia_lib::commands::git_hook_commands::test_git_hook,
            claudia_lib::commands::git_hook_commands::list_git_hooks,
            claudia_lib::commands::git_hook_commands::get_git_hook,
            claudia_lib::commands::git_hook_commands::update_git_hook,
            claudia_lib::commands::git_hook_commands::delete_git_hook,
            claudia_lib::commands::git_hook_commands::get_git_hook_executions,
            
            // Agent Orchestration Commands
            claudia_lib::commands::agent_orchestration_commands::init_agent_orchestrator,
            claudia_lib::commands::agent_orchestration_commands::create_orchestration_session,
            claudia_lib::commands::agent_orchestration_commands::add_agent_to_orchestration,
            claudia_lib::commands::agent_orchestration_commands::start_orchestration,
            claudia_lib::commands::agent_orchestration_commands::stop_orchestration,
            claudia_lib::commands::agent_orchestration_commands::get_orchestration_status,
            claudia_lib::commands::agent_orchestration_commands::list_orchestration_sessions,
            claudia_lib::commands::agent_orchestration_commands::coordinate_task,
            claudia_lib::commands::agent_orchestration_commands::get_orchestration_metrics,
            claudia_lib::commands::agent_orchestration_commands::optimize_orchestration,
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}

// Learn more about <PERSON>ri commands at https://tauri.app/develop/calling-rust/

// Declare modules
pub mod agents;
pub mod checkpoint;
pub mod claude_binary;
pub mod commands;
pub mod process;
pub mod db;
pub mod db_budget_extensions;
pub mod mock_data;
pub mod usage_tracker;
pub mod training;
pub mod context_watcher;
pub mod git;
pub mod integration;
pub mod mcp;
pub mod workflow;

// Re-export all Tauri commands for easier access
pub use commands::agents::*;
pub use commands::claude::{self, *};
pub use commands::mcp::*;
pub use commands::usage::*;
pub use commands::storage::*;
pub use commands::proxy::*;
pub use commands::mock_api::*;
pub use commands::taskmaster::{self, Project as TaskmasterProject, TaskMasterState};
pub use commands::taskmaster::{load_taskmaster_file, save_taskmaster_file, create_taskmaster_file, list_taskmaster_files, validate_taskmaster_file, export_taskmaster_csv, export_taskmaster_markdown, watch_taskmaster_file, stop_watching_taskmaster_file};
pub use commands::taskmaster_ai::*;
pub use commands::model_switcher_commands::*;
pub use commands::conversation_summary_commands::*;
pub use commands::error_recovery_commands::*;
pub use commands::slash_commands::*;
pub use commands::budget::*;
pub use commands::budget_extended::*;
pub use commands::expenses::*;
pub use commands::training::*;
pub use commands::training_enhanced::*;
pub use commands::enhanced_mcp::*;
pub use commands::workflow::*;
pub use commands::agent_marketplace::*;
pub use commands::agent_collaboration::*;

#[cfg(test)]
mod template_tests;

#[cfg(test)]
mod inheritance_tests;

#[cfg(test)]
mod slash_commands_tests;

#[cfg(test)]
mod error_handling_tests;

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    tauri::Builder::default()
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}

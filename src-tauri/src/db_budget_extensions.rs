use anyhow::Result;
use chrono::Utc;
use rusqlite::{params, Connection};
use serde::{Deserialize, Serialize};

// ============= Extended Budget Models =============

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct QuarterlyAllocation {
    pub id: i64,
    pub year: i64,
    pub quarter: i32,
    pub allocated: f64,
    pub spent: f64,
    pub created_at: String,
    pub updated_at: String,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct DepartmentAllocation {
    pub id: String,
    pub name: String,
    pub year: i64,
    pub allocated_amount: f64,
    pub spent_amount: f64,
    pub percentage: f64,
    pub created_at: String,
    pub updated_at: String,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct CategoryLimit {
    pub id: String,
    pub category: String,
    pub year: i64,
    pub limit_amount: f64,
    pub spent_amount: f64,
    pub created_at: String,
    pub updated_at: String,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct AllocationRule {
    pub id: String,
    pub name: String,
    pub description: String,
    pub rule_type: String, // percentage, fixed, dynamic
    pub value: f64,
    pub target: String,
    pub target_id: Option<String>,
    pub enabled: bool,
    pub priority: i32,
    pub conditions: String, // JSON
    pub actions: String, // JSON
    pub created_at: String,
    pub updated_at: String,
}

// ============= Database Schema Extensions =============

pub fn init_budget_extensions(conn: &Connection) -> Result<()> {
    // Quarterly Allocations Table
    conn.execute(
        "CREATE TABLE IF NOT EXISTS quarterly_allocations (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            year INTEGER NOT NULL,
            quarter INTEGER NOT NULL CHECK (quarter >= 1 AND quarter <= 4),
            allocated REAL NOT NULL DEFAULT 0,
            spent REAL NOT NULL DEFAULT 0,
            created_at TEXT NOT NULL,
            updated_at TEXT NOT NULL,
            UNIQUE(year, quarter)
        )",
        [],
    )?;

    // Department Allocations Table
    conn.execute(
        "CREATE TABLE IF NOT EXISTS department_allocations (
            id TEXT PRIMARY KEY,
            name TEXT NOT NULL,
            year INTEGER NOT NULL,
            allocated_amount REAL NOT NULL DEFAULT 0,
            spent_amount REAL NOT NULL DEFAULT 0,
            percentage REAL NOT NULL DEFAULT 0,
            created_at TEXT NOT NULL,
            updated_at TEXT NOT NULL,
            UNIQUE(name, year)
        )",
        [],
    )?;

    // Category Limits Table
    conn.execute(
        "CREATE TABLE IF NOT EXISTS category_limits (
            id TEXT PRIMARY KEY,
            category TEXT NOT NULL,
            year INTEGER NOT NULL,
            limit_amount REAL NOT NULL DEFAULT 0,
            spent_amount REAL NOT NULL DEFAULT 0,
            created_at TEXT NOT NULL,
            updated_at TEXT NOT NULL,
            UNIQUE(category, year)
        )",
        [],
    )?;

    // Allocation Rules Table
    conn.execute(
        "CREATE TABLE IF NOT EXISTS allocation_rules (
            id TEXT PRIMARY KEY,
            name TEXT NOT NULL,
            description TEXT,
            rule_type TEXT NOT NULL,
            value REAL NOT NULL,
            target TEXT NOT NULL,
            target_id TEXT,
            enabled BOOLEAN NOT NULL DEFAULT 1,
            priority INTEGER NOT NULL DEFAULT 0,
            conditions TEXT, -- JSON
            actions TEXT, -- JSON
            created_at TEXT NOT NULL,
            updated_at TEXT NOT NULL
        )",
        [],
    )?;

    Ok(())
}

// ============= Quarterly Allocations Functions =============

pub fn get_quarterly_allocations(conn: &Connection, year: i64) -> Result<Vec<QuarterlyAllocation>> {
    let mut stmt = conn.prepare(
        "SELECT id, year, quarter, allocated, spent, created_at, updated_at 
         FROM quarterly_allocations 
         WHERE year = ?1 
         ORDER BY quarter"
    )?;

    let allocations = stmt.query_map(params![year], |row| {
        Ok(QuarterlyAllocation {
            id: row.get(0)?,
            year: row.get(1)?,
            quarter: row.get(2)?,
            allocated: row.get(3)?,
            spent: row.get(4)?,
            created_at: row.get(5)?,
            updated_at: row.get(6)?,
        })
    })?
    .collect::<Result<Vec<_>, _>>()?;

    // If no allocations exist for the year, create default ones
    if allocations.is_empty() {
        create_default_quarterly_allocations(conn, year)?;
        return get_quarterly_allocations(conn, year);
    }

    Ok(allocations)
}

fn create_default_quarterly_allocations(conn: &Connection, year: i64) -> Result<()> {
    let now = Utc::now().to_rfc3339();
    
    // Get total budget for the year
    let total_budget: f64 = conn.query_row(
        "SELECT total_amount FROM budgets WHERE year = ?1",
        params![year],
        |row| row.get(0)
    ).unwrap_or(100000.0);
    
    let quarterly_amount = total_budget / 4.0;
    
    for quarter in 1..=4 {
        conn.execute(
            "INSERT OR IGNORE INTO quarterly_allocations (year, quarter, allocated, spent, created_at, updated_at) 
             VALUES (?1, ?2, ?3, 0, ?4, ?5)",
            params![year, quarter, quarterly_amount, &now, &now],
        )?;
    }
    
    Ok(())
}

pub fn update_quarterly_allocations(
    conn: &Connection,
    year: i64,
    allocations: Vec<QuarterlyAllocation>
) -> Result<()> {
    let now = Utc::now().to_rfc3339();
    
    for allocation in allocations {
        conn.execute(
            "UPDATE quarterly_allocations 
             SET allocated = ?1, updated_at = ?2 
             WHERE year = ?3 AND quarter = ?4",
            params![allocation.allocated, &now, year, allocation.quarter],
        )?;
    }
    
    Ok(())
}

// ============= Department Allocations Functions =============

pub fn get_department_allocations(conn: &Connection, year: i64) -> Result<Vec<DepartmentAllocation>> {
    let mut stmt = conn.prepare(
        "SELECT id, name, year, allocated_amount, spent_amount, percentage, created_at, updated_at 
         FROM department_allocations 
         WHERE year = ?1 
         ORDER BY allocated_amount DESC"
    )?;

    let allocations = stmt.query_map(params![year], |row| {
        Ok(DepartmentAllocation {
            id: row.get(0)?,
            name: row.get(1)?,
            year: row.get(2)?,
            allocated_amount: row.get(3)?,
            spent_amount: row.get(4)?,
            percentage: row.get(5)?,
            created_at: row.get(6)?,
            updated_at: row.get(7)?,
        })
    })?
    .collect::<Result<Vec<_>, _>>()?;

    // If no departments exist, create default ones
    if allocations.is_empty() {
        create_default_departments(conn, year)?;
        return get_department_allocations(conn, year);
    }

    Ok(allocations)
}

fn create_default_departments(conn: &Connection, year: i64) -> Result<()> {
    let now = Utc::now().to_rfc3339();
    
    let departments = vec![
        ("Engineering", 40.0, 40000.0),
        ("Sales", 30.0, 30000.0),
        ("Marketing", 20.0, 20000.0),
        ("HR", 10.0, 10000.0),
    ];
    
    for (name, percentage, amount) in departments {
        let id = format!("dept-{}-{}", name.to_lowercase(), year);
        conn.execute(
            "INSERT OR IGNORE INTO department_allocations 
             (id, name, year, allocated_amount, spent_amount, percentage, created_at, updated_at) 
             VALUES (?1, ?2, ?3, ?4, 0, ?5, ?6, ?7)",
            params![id, name, year, amount, percentage, &now, &now],
        )?;
    }
    
    Ok(())
}

pub fn update_department_allocation(
    conn: &Connection,
    department_id: &str,
    allocation: f64
) -> Result<()> {
    let now = Utc::now().to_rfc3339();
    
    // Get year from the department
    let year: i64 = conn.query_row(
        "SELECT year FROM department_allocations WHERE id = ?1",
        params![department_id],
        |row| row.get(0)
    )?;
    
    // Update the allocation
    conn.execute(
        "UPDATE department_allocations 
         SET allocated_amount = ?1, updated_at = ?2 
         WHERE id = ?3",
        params![allocation, &now, department_id],
    )?;
    
    // Recalculate percentages
    recalculate_department_percentages(conn, year)?;
    
    Ok(())
}

pub fn add_department(conn: &Connection, name: &str, allocation: f64, year: i64) -> Result<DepartmentAllocation> {
    let now = Utc::now().to_rfc3339();
    let id = format!("dept-{}-{}", name.to_lowercase().replace(" ", "-"), Utc::now().timestamp());
    
    conn.execute(
        "INSERT INTO department_allocations 
         (id, name, year, allocated_amount, spent_amount, percentage, created_at, updated_at) 
         VALUES (?1, ?2, ?3, ?4, 0, 0, ?5, ?6)",
        params![&id, name, year, allocation, &now, &now],
    )?;
    
    // Recalculate percentages
    recalculate_department_percentages(conn, year)?;
    
    // Return the created department
    let result = conn.query_row(
        "SELECT id, name, year, allocated_amount, spent_amount, percentage, created_at, updated_at 
         FROM department_allocations WHERE id = ?1",
        params![&id],
        |row| {
            Ok(DepartmentAllocation {
                id: row.get(0)?,
                name: row.get(1)?,
                year: row.get(2)?,
                allocated_amount: row.get(3)?,
                spent_amount: row.get(4)?,
                percentage: row.get(5)?,
                created_at: row.get(6)?,
                updated_at: row.get(7)?,
            })
        }
    )?;
    
    Ok(result)
}

pub fn remove_department(conn: &Connection, department_id: &str) -> Result<()> {
    // Get year before deletion
    let year: i64 = conn.query_row(
        "SELECT year FROM department_allocations WHERE id = ?1",
        params![department_id],
        |row| row.get(0)
    )?;
    
    conn.execute(
        "DELETE FROM department_allocations WHERE id = ?1",
        params![department_id],
    )?;
    
    // Recalculate percentages
    recalculate_department_percentages(conn, year)?;
    
    Ok(())
}

fn recalculate_department_percentages(conn: &Connection, year: i64) -> Result<()> {
    let total: f64 = conn.query_row(
        "SELECT SUM(allocated_amount) FROM department_allocations WHERE year = ?1",
        params![year],
        |row| row.get(0)
    )?;
    
    if total > 0.0 {
        conn.execute(
            "UPDATE department_allocations 
             SET percentage = (allocated_amount / ?1) * 100 
             WHERE year = ?2",
            params![total, year],
        )?;
    }
    
    Ok(())
}

// ============= Category Limits Functions =============

pub fn get_category_limits(conn: &Connection, year: i64) -> Result<Vec<CategoryLimit>> {
    let mut stmt = conn.prepare(
        "SELECT id, category, year, limit_amount, spent_amount, created_at, updated_at 
         FROM category_limits 
         WHERE year = ?1 
         ORDER BY limit_amount DESC"
    )?;

    let limits = stmt.query_map(params![year], |row| {
        Ok(CategoryLimit {
            id: row.get(0)?,
            category: row.get(1)?,
            year: row.get(2)?,
            limit_amount: row.get(3)?,
            spent_amount: row.get(4)?,
            created_at: row.get(5)?,
            updated_at: row.get(6)?,
        })
    })?
    .collect::<Result<Vec<_>, _>>()?;

    Ok(limits)
}

pub fn set_category_limit(conn: &Connection, category: &str, limit: f64, year: i64) -> Result<CategoryLimit> {
    let now = Utc::now().to_rfc3339();
    let id = format!("cat-{}-{}", category.to_lowercase().replace(" ", "-"), Utc::now().timestamp());
    
    // Calculate current spent amount from expenses
    let spent: f64 = conn.query_row(
        "SELECT COALESCE(SUM(amount), 0) FROM expenses 
         WHERE category = ?1 AND strftime('%Y', date) = ?2",
        params![category, year.to_string()],
        |row| row.get(0)
    ).unwrap_or(0.0);
    
    conn.execute(
        "INSERT OR REPLACE INTO category_limits 
         (id, category, year, limit_amount, spent_amount, created_at, updated_at) 
         VALUES (?1, ?2, ?3, ?4, ?5, ?6, ?7)",
        params![&id, category, year, limit, spent, &now, &now],
    )?;
    
    Ok(CategoryLimit {
        id,
        category: category.to_string(),
        year,
        limit_amount: limit,
        spent_amount: spent,
        created_at: now.clone(),
        updated_at: now,
    })
}

pub fn remove_category_limit(conn: &Connection, category_id: &str) -> Result<()> {
    conn.execute(
        "DELETE FROM category_limits WHERE id = ?1",
        params![category_id],
    )?;
    
    Ok(())
}

// ============= Allocation Rules Functions =============

pub fn get_allocation_rules(conn: &Connection) -> Result<Vec<AllocationRule>> {
    let mut stmt = conn.prepare(
        "SELECT id, name, description, rule_type, value, target, target_id, 
                enabled, priority, conditions, actions, created_at, updated_at 
         FROM allocation_rules 
         ORDER BY priority DESC, created_at DESC"
    )?;

    let rules = stmt.query_map([], |row| {
        Ok(AllocationRule {
            id: row.get(0)?,
            name: row.get(1)?,
            description: row.get(2)?,
            rule_type: row.get(3)?,
            value: row.get(4)?,
            target: row.get(5)?,
            target_id: row.get(6)?,
            enabled: row.get(7)?,
            priority: row.get(8)?,
            conditions: row.get(9)?,
            actions: row.get(10)?,
            created_at: row.get(11)?,
            updated_at: row.get(12)?,
        })
    })?
    .collect::<Result<Vec<_>, _>>()?;

    Ok(rules)
}

pub fn add_allocation_rule(conn: &Connection, rule: AllocationRule) -> Result<AllocationRule> {
    let now = Utc::now().to_rfc3339();
    let id = format!("rule-{}", Utc::now().timestamp());
    
    conn.execute(
        "INSERT INTO allocation_rules 
         (id, name, description, rule_type, value, target, target_id, 
          enabled, priority, conditions, actions, created_at, updated_at) 
         VALUES (?1, ?2, ?3, ?4, ?5, ?6, ?7, ?8, ?9, ?10, ?11, ?12, ?13)",
        params![
            &id, &rule.name, &rule.description, &rule.rule_type, rule.value,
            &rule.target, &rule.target_id, rule.enabled, rule.priority,
            &rule.conditions, &rule.actions, &now, &now
        ],
    )?;
    
    Ok(AllocationRule {
        id,
        created_at: now.clone(),
        updated_at: now,
        ..rule
    })
}

pub fn update_allocation_rule(conn: &Connection, id: &str, rule: AllocationRule) -> Result<AllocationRule> {
    let now = Utc::now().to_rfc3339();
    
    conn.execute(
        "UPDATE allocation_rules 
         SET name = ?2, description = ?3, rule_type = ?4, value = ?5, 
             target = ?6, target_id = ?7, enabled = ?8, priority = ?9, 
             conditions = ?10, actions = ?11, updated_at = ?12 
         WHERE id = ?1",
        params![
            id, &rule.name, &rule.description, &rule.rule_type, rule.value,
            &rule.target, &rule.target_id, rule.enabled, rule.priority,
            &rule.conditions, &rule.actions, &now
        ],
    )?;
    
    Ok(AllocationRule {
        id: id.to_string(),
        updated_at: now,
        ..rule
    })
}

pub fn delete_allocation_rule(conn: &Connection, id: &str) -> Result<()> {
    conn.execute(
        "DELETE FROM allocation_rules WHERE id = ?1",
        params![id],
    )?;
    
    Ok(())
}

pub fn toggle_allocation_rule(conn: &Connection, id: &str, enabled: bool) -> Result<AllocationRule> {
    let now = Utc::now().to_rfc3339();
    
    conn.execute(
        "UPDATE allocation_rules SET enabled = ?2, updated_at = ?3 WHERE id = ?1",
        params![id, enabled, &now],
    )?;
    
    let result = conn.query_row(
        "SELECT id, name, description, rule_type, value, target, target_id, 
                enabled, priority, conditions, actions, created_at, updated_at 
         FROM allocation_rules WHERE id = ?1",
        params![id],
        |row| {
            Ok(AllocationRule {
                id: row.get(0)?,
                name: row.get(1)?,
                description: row.get(2)?,
                rule_type: row.get(3)?,
                value: row.get(4)?,
                target: row.get(5)?,
                target_id: row.get(6)?,
                enabled: row.get(7)?,
                priority: row.get(8)?,
                conditions: row.get(9)?,
                actions: row.get(10)?,
                created_at: row.get(11)?,
                updated_at: row.get(12)?,
            })
        }
    )?;
    
    Ok(result)
}
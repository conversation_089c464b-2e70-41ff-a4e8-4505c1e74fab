use anyhow::Result;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct SignificanceMetrics {
    pub conversation_depth: u32,
    pub file_changes_count: u32,
    pub tool_usage_count: u32,
    pub error_recovery_events: u32,
    pub context_switches: u32,
    pub complexity_score: f32,
    pub significance_score: f32,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ConversationContext {
    pub topic_keywords: Vec<String>,
    pub current_task_type: TaskType,
    pub session_phase: SessionPhase,
    pub last_checkpoint_significance: f32,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub enum TaskType {
    Coding,
    Debugging,
    Refactoring,
    Testing,
    Documentation,
    Analysis,
    Planning,
    Unknown,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub enum SessionPhase {
    Exploration,
    Development,
    Refinement,
    Finalization,
}

#[derive(<PERSON><PERSON>)]
pub struct SignificanceAnalyzer {
    context: ConversationContext,
    message_history: Vec<(DateTime<Utc>, String)>,
    keyword_weights: HashMap<String, f32>,
}

impl SignificanceAnalyzer {
    pub fn new() -> Self {
        let mut keyword_weights = HashMap::new();
        
        // High significance keywords
        keyword_weights.insert("implement".to_string(), 0.9);
        keyword_weights.insert("create".to_string(), 0.8);
        keyword_weights.insert("refactor".to_string(), 0.8);
        keyword_weights.insert("fix".to_string(), 0.7);
        keyword_weights.insert("debug".to_string(), 0.7);
        keyword_weights.insert("optimize".to_string(), 0.7);
        keyword_weights.insert("deploy".to_string(), 0.9);
        keyword_weights.insert("release".to_string(), 0.9);
        keyword_weights.insert("test".to_string(), 0.6);
        keyword_weights.insert("build".to_string(), 0.6);
        
        // Medium significance keywords
        keyword_weights.insert("update".to_string(), 0.5);
        keyword_weights.insert("modify".to_string(), 0.5);
        keyword_weights.insert("change".to_string(), 0.4);
        keyword_weights.insert("add".to_string(), 0.4);
        keyword_weights.insert("remove".to_string(), 0.5);
        
        // Low significance keywords
        keyword_weights.insert("explain".to_string(), 0.2);
        keyword_weights.insert("show".to_string(), 0.2);
        keyword_weights.insert("help".to_string(), 0.2);
        keyword_weights.insert("what".to_string(), 0.1);
        keyword_weights.insert("how".to_string(), 0.1);

        Self {
            context: ConversationContext {
                topic_keywords: Vec::new(),
                current_task_type: TaskType::Unknown,
                session_phase: SessionPhase::Exploration,
                last_checkpoint_significance: 0.0,
            },
            message_history: Vec::new(),
            keyword_weights,
        }
    }

    pub fn analyze_message_significance(&mut self, message: &str) -> Result<SignificanceMetrics> {
        self.message_history.push((Utc::now(), message.to_string()));
        
        let metrics = if let Ok(parsed) = serde_json::from_str::<serde_json::Value>(message) {
            self.analyze_structured_message(&parsed)?
        } else {
            self.analyze_text_message(message)?
        };

        // Update context based on analysis
        self.update_context(&metrics);
        
        Ok(metrics)
    }

    fn analyze_structured_message(&mut self, msg: &serde_json::Value) -> Result<SignificanceMetrics> {
        let mut metrics = SignificanceMetrics {
            conversation_depth: self.message_history.len() as u32,
            file_changes_count: 0,
            tool_usage_count: 0,
            error_recovery_events: 0,
            context_switches: 0,
            complexity_score: 0.0,
            significance_score: 0.0,
        };

        // Analyze user messages
        if msg.get("type").and_then(|t| t.as_str()) == Some("user") {
            if let Some(content) = msg.get("message").and_then(|m| m.get("content")) {
                if let Some(content_array) = content.as_array() {
                    for item in content_array {
                        if item.get("type").and_then(|t| t.as_str()) == Some("text") {
                            if let Some(text) = item.get("text").and_then(|t| t.as_str()) {
                                metrics.complexity_score += self.analyze_text_complexity(text);
                            }
                        }
                    }
                }
            }
        }

        // Analyze assistant messages with tool usage
        if msg.get("type").and_then(|t| t.as_str()) == Some("assistant") {
            if let Some(content) = msg.get("message").and_then(|m| m.get("content")) {
                if let Some(content_array) = content.as_array() {
                    for item in content_array {
                        if item.get("type").and_then(|t| t.as_str()) == Some("tool_use") {
                            metrics.tool_usage_count += 1;
                            
                            if let Some(tool_name) = item.get("name").and_then(|n| n.as_str()) {
                                metrics.complexity_score += self.get_tool_complexity_weight(tool_name);
                                
                                // Count file changes
                                if matches!(tool_name.to_lowercase().as_str(), 
                                    "write" | "edit" | "multiedit" | "create" | "delete") {
                                    metrics.file_changes_count += 1;
                                }
                            }
                        }
                    }
                }
            }
            
            // Check for stop reason indicating errors
            if let Some(stop_reason) = msg.get("message").and_then(|m| m.get("stop_reason")) {
                if stop_reason.as_str() == Some("tool_error") {
                    metrics.error_recovery_events += 1;
                }
            }
        }

        // Analyze tool result messages for errors
        if msg.get("type").and_then(|t| t.as_str()) == Some("tool_result") {
            if let Some(is_error) = msg.get("message").and_then(|m| m.get("is_error")) {
                if is_error.as_bool() == Some(true) {
                    metrics.error_recovery_events += 1;
                }
            }
        }

        // Calculate final significance score
        metrics.significance_score = self.calculate_significance_score(&metrics);
        
        Ok(metrics)
    }

    fn analyze_text_message(&mut self, text: &str) -> Result<SignificanceMetrics> {
        // Update keyword weights based on important terms
        self.update_keyword_weights(text);
        
        let mut metrics = SignificanceMetrics {
            conversation_depth: self.message_history.len() as u32,
            file_changes_count: 0,
            tool_usage_count: 0,
            error_recovery_events: 0,
            context_switches: 0,
            complexity_score: self.analyze_text_complexity(text),
            significance_score: 0.0,
        };
        
        // Detect context switches
        if text.contains("let's") || text.contains("now") || text.contains("next") {
            metrics.context_switches = 1;
        }

        metrics.significance_score = self.calculate_significance_score(&metrics);
        Ok(metrics)
    }
    
    fn update_keyword_weights(&mut self, text: &str) {
        // Important keywords that indicate significant moments
        let important_keywords = [
            ("implement", 1.5),
            ("fix", 1.2),
            ("bug", 1.3),
            ("error", 1.4),
            ("refactor", 1.6),
            ("optimize", 1.3),
            ("test", 1.1),
            ("deploy", 2.0),
            ("production", 2.0),
            ("critical", 1.8),
            ("urgent", 1.7),
            ("complete", 1.5),
            ("finished", 1.5),
        ];
        
        for (keyword, weight) in &important_keywords {
            if text.to_lowercase().contains(keyword) {
                *self.keyword_weights.entry(keyword.to_string()).or_insert(0.0) += weight;
            }
        }
    }

    fn analyze_text_complexity(&self, text: &str) -> f32 {
        let words: Vec<&str> = text.split_whitespace().collect();
        let mut complexity = 0.0;

        // Base complexity from length
        complexity += (words.len() as f32).sqrt() * 0.1;

        // Keyword-based complexity
        for word in &words {
            let word_lower = word.to_lowercase();
            if let Some(weight) = self.keyword_weights.get(&word_lower) {
                complexity += weight;
            }
        }

        // Technical terms increase complexity
        let technical_indicators = [
            "function", "class", "method", "variable", "import", "export",
            "async", "await", "promise", "callback", "interface", "type",
            "component", "hook", "state", "props", "render", "lifecycle",
            "dependency", "module", "package", "library", "framework",
            "database", "query", "schema", "migration", "api", "endpoint",
            "authentication", "authorization", "security", "performance",
            "optimization", "caching", "memory", "storage", "network",
        ];

        for indicator in &technical_indicators {
            if text.to_lowercase().contains(indicator) {
                complexity += 0.3;
            }
        }

        // Code snippets increase complexity
        if text.contains("```") || text.contains("```") {
            complexity += 0.5;
        }

        // File paths increase complexity
        if text.contains("/") && text.contains(".") {
            complexity += 0.2;
        }

        complexity.min(10.0) // Cap at 10.0
    }

    fn get_tool_complexity_weight(&self, tool_name: &str) -> f32 {
        match tool_name.to_lowercase().as_str() {
            "write" | "edit" | "multiedit" => 0.8,
            "bash" => 0.7,
            "read" => 0.2,
            "grep" | "glob" => 0.3,
            "ls" => 0.1,
            "task" => 0.5,
            "webfetch" => 0.4,
            _ => 0.3,
        }
    }

    fn calculate_significance_score(&self, metrics: &SignificanceMetrics) -> f32 {
        let mut score = 0.0;

        // File changes are highly significant
        score += metrics.file_changes_count as f32 * 0.4;

        // Tool usage indicates active work  
        score += metrics.tool_usage_count as f32 * 0.2;

        // Error recovery is significant for learning
        score += metrics.error_recovery_events as f32 * 0.6;

        // Complexity contributes to significance
        score += metrics.complexity_score * 0.3;

        // Apply keyword weights if they exist
        let keyword_boost = self.keyword_weights.values().sum::<f32>() * 0.1;
        score += keyword_boost;

        // Conversation depth (longer conversations need checkpoints)
        if metrics.conversation_depth > 20 {
            score += 0.5;
        } else if metrics.conversation_depth > 10 {
            score += 0.3;
        }

        // Context switches are significant
        score += metrics.context_switches as f32 * 0.4;

        // Consider message history length
        let history_factor = (self.message_history.len() as f32 / 100.0).min(1.0);
        score += history_factor * 0.2;

        // Normalize to 0-10 scale
        score.min(10.0)
    }

    pub fn should_create_checkpoint(&self, metrics: &SignificanceMetrics) -> bool {
        let threshold = match self.context.session_phase {
            SessionPhase::Exploration => 3.0,
            SessionPhase::Development => 2.5,
            SessionPhase::Refinement => 2.0,
            SessionPhase::Finalization => 1.5,
        };

        // Always checkpoint if significance is very high
        if metrics.significance_score >= 7.0 {
            return true;
        }

        // Check against phase-specific threshold
        if metrics.significance_score >= threshold {
            return true;
        }

        // Special cases for automatic checkpointing
        if metrics.file_changes_count >= 3 {
            return true;
        }

        if metrics.error_recovery_events >= 2 {
            return true;
        }

        // Check time since last significant checkpoint
        if metrics.significance_score > self.context.last_checkpoint_significance * 1.5 {
            return true;
        }

        false
    }

    pub fn generate_checkpoint_name(&self, metrics: &SignificanceMetrics) -> String {
        let task_prefix = match self.context.current_task_type {
            TaskType::Coding => "Code",
            TaskType::Debugging => "Fix",
            TaskType::Refactoring => "Refactor",
            TaskType::Testing => "Test",
            TaskType::Documentation => "Doc",
            TaskType::Analysis => "Analysis",
            TaskType::Planning => "Plan",
            TaskType::Unknown => "Work",
        };

        let significance_level = if metrics.significance_score >= 7.0 {
            "Major"
        } else if metrics.significance_score >= 4.0 {
            "Significant"
        } else {
            "Minor"
        };

        let detail = if metrics.file_changes_count > 0 {
            format!(" ({} files)", metrics.file_changes_count)
        } else if metrics.tool_usage_count > 0 {
            format!(" ({} tools)", metrics.tool_usage_count)
        } else {
            String::new()
        };

        format!("{}: {} Progress{}", task_prefix, significance_level, detail)
    }

    fn update_context(&mut self, metrics: &SignificanceMetrics) {
        // Update task type based on recent activity
        if metrics.file_changes_count > 0 {
            self.context.current_task_type = TaskType::Coding;
        } else if metrics.error_recovery_events > 0 {
            self.context.current_task_type = TaskType::Debugging;
        }

        // Update session phase based on conversation depth and activity
        self.context.session_phase = match metrics.conversation_depth {
            0..=5 => SessionPhase::Exploration,
            6..=20 => SessionPhase::Development,
            21..=50 => SessionPhase::Refinement,
            _ => SessionPhase::Finalization,
        };

        self.context.last_checkpoint_significance = metrics.significance_score;
    }

    pub fn get_context(&self) -> &ConversationContext {
        &self.context
    }

    pub fn reset_context(&mut self) {
        self.context = ConversationContext {
            topic_keywords: Vec::new(),
            current_task_type: TaskType::Unknown,
            session_phase: SessionPhase::Exploration,
            last_checkpoint_significance: 0.0,
        };
        self.message_history.clear();
    }
}

impl Default for SignificanceAnalyzer {
    fn default() -> Self {
        Self::new()
    }
}
use serde::{Deserialize, Serialize};
use chrono::Datelike;
use crate::db_budget_extensions::{self as db_ext, QuarterlyAllocation, DepartmentAllocation, CategoryLimit, AllocationRule};

// ============= Command Structures =============

#[derive(Debug, Serialize, Deserialize)]
pub struct QuarterlyAllocationInput {
    pub quarter: i32,
    pub allocated: f64,
    pub spent: f64,
}

// ============= Quarterly Allocations Commands =============

#[tauri::command]
pub fn get_quarterly_allocations(year: i64) -> Result<Vec<QuarterlyAllocation>, String> {
    let conn = crate::db::get_connection()
        .map_err(|e| format!("Failed to get database connection: {}", e))?;
    
    db_ext::get_quarterly_allocations(&conn, year)
        .map_err(|e| format!("Failed to get quarterly allocations: {}", e))
}

#[tauri::command]
pub fn update_quarterly_allocations(
    year: i64,
    allocations: Vec<QuarterlyAllocationInput>
) -> Result<bool, String> {
    let conn = crate::db::get_connection()
        .map_err(|e| format!("Failed to get database connection: {}", e))?;
    
    let allocations: Vec<QuarterlyAllocation> = allocations.into_iter().map(|a| {
        QuarterlyAllocation {
            id: 0, // Will be ignored in update
            year,
            quarter: a.quarter,
            allocated: a.allocated,
            spent: a.spent,
            created_at: String::new(),
            updated_at: String::new(),
        }
    }).collect();
    
    db_ext::update_quarterly_allocations(&conn, year, allocations)
        .map(|_| true)
        .map_err(|e| format!("Failed to update quarterly allocations: {}", e))
}

// ============= Department Allocations Commands =============

#[tauri::command]
pub fn get_department_allocations(year: i64) -> Result<Vec<DepartmentAllocation>, String> {
    let conn = crate::db::get_connection()
        .map_err(|e| format!("Failed to get database connection: {}", e))?;
    
    db_ext::get_department_allocations(&conn, year)
        .map_err(|e| format!("Failed to get department allocations: {}", e))
}

#[tauri::command]
pub fn update_department_allocation(id: String, allocation: f64) -> Result<bool, String> {
    let conn = crate::db::get_connection()
        .map_err(|e| format!("Failed to get database connection: {}", e))?;
    
    db_ext::update_department_allocation(&conn, &id, allocation)
        .map(|_| true)
        .map_err(|e| format!("Failed to update department allocation: {}", e))
}

#[tauri::command]
pub fn add_department(name: String, allocation: f64) -> Result<DepartmentAllocation, String> {
    let conn = crate::db::get_connection()
        .map_err(|e| format!("Failed to get database connection: {}", e))?;
    
    let year = chrono::Utc::now().year() as i64;
    
    db_ext::add_department(&conn, &name, allocation, year)
        .map_err(|e| format!("Failed to add department: {}", e))
}

#[tauri::command]
pub fn remove_department(id: String) -> Result<bool, String> {
    let conn = crate::db::get_connection()
        .map_err(|e| format!("Failed to get database connection: {}", e))?;
    
    db_ext::remove_department(&conn, &id)
        .map(|_| true)
        .map_err(|e| format!("Failed to remove department: {}", e))
}

// ============= Category Limits Commands =============

#[tauri::command]
pub fn get_category_limits(year: i64) -> Result<Vec<CategoryLimit>, String> {
    let conn = crate::db::get_connection()
        .map_err(|e| format!("Failed to get database connection: {}", e))?;
    
    db_ext::get_category_limits(&conn, year)
        .map_err(|e| format!("Failed to get category limits: {}", e))
}

#[tauri::command]
pub fn set_category_limit(category: String, limit: f64) -> Result<CategoryLimit, String> {
    let conn = crate::db::get_connection()
        .map_err(|e| format!("Failed to get database connection: {}", e))?;
    
    let year = chrono::Utc::now().year() as i64;
    
    db_ext::set_category_limit(&conn, &category, limit, year)
        .map_err(|e| format!("Failed to set category limit: {}", e))
}

#[tauri::command]
pub fn remove_category_limit(id: String) -> Result<bool, String> {
    let conn = crate::db::get_connection()
        .map_err(|e| format!("Failed to get database connection: {}", e))?;
    
    db_ext::remove_category_limit(&conn, &id)
        .map(|_| true)
        .map_err(|e| format!("Failed to remove category limit: {}", e))
}

// ============= Allocation Rules Commands =============

#[tauri::command]
pub fn get_allocation_rules() -> Result<Vec<AllocationRule>, String> {
    let conn = crate::db::get_connection()
        .map_err(|e| format!("Failed to get database connection: {}", e))?;
    
    db_ext::get_allocation_rules(&conn)
        .map_err(|e| format!("Failed to get allocation rules: {}", e))
}

#[tauri::command]
pub fn add_allocation_rule(rule: AllocationRule) -> Result<AllocationRule, String> {
    let conn = crate::db::get_connection()
        .map_err(|e| format!("Failed to get database connection: {}", e))?;
    
    db_ext::add_allocation_rule(&conn, rule)
        .map_err(|e| format!("Failed to add allocation rule: {}", e))
}

#[tauri::command]
pub fn update_allocation_rule(id: String, rule: AllocationRule) -> Result<AllocationRule, String> {
    let conn = crate::db::get_connection()
        .map_err(|e| format!("Failed to get database connection: {}", e))?;
    
    db_ext::update_allocation_rule(&conn, &id, rule)
        .map_err(|e| format!("Failed to update allocation rule: {}", e))
}

#[tauri::command]
pub fn delete_allocation_rule(id: String) -> Result<bool, String> {
    let conn = crate::db::get_connection()
        .map_err(|e| format!("Failed to get database connection: {}", e))?;
    
    db_ext::delete_allocation_rule(&conn, &id)
        .map(|_| true)
        .map_err(|e| format!("Failed to delete allocation rule: {}", e))
}

#[tauri::command]
pub fn toggle_allocation_rule(id: String, enabled: bool) -> Result<AllocationRule, String> {
    let conn = crate::db::get_connection()
        .map_err(|e| format!("Failed to get database connection: {}", e))?;
    
    db_ext::toggle_allocation_rule(&conn, &id, enabled)
        .map_err(|e| format!("Failed to toggle allocation rule: {}", e))
}

#[tauri::command]
pub fn execute_allocation_rule(_id: String) -> Result<bool, String> {
    // This would implement the actual rule execution logic
    // For now, we'll just return success
    Ok(true)
}
use crate::mock_data::*;
use std::collections::HashMap;
use std::sync::Mutex;
use lazy_static::lazy_static;
use chrono::{Utc, Datelike};
use serde_json;

// Global state for mock data
lazy_static! {
    static ref MOCK_STATE: Mutex<MockState> = Mutex::new(MockState::new());
}

struct MockState {
    quarterly_allocations: HashMap<i64, Vec<QuarterlyAllocation>>,
    department_allocations: HashMap<i64, Vec<DepartmentAllocation>>,
    category_limits: HashMap<i64, Vec<CategoryLimit>>,
    expenses: HashMap<i64, Vec<Expense>>,
    allocation_rules: Vec<AllocationRule>,
    analytics: HashMap<i64, BudgetAnalytics>,
}

impl MockState {
    fn new() -> Self {
        let mut state = Self {
            quarterly_allocations: HashMap::new(),
            department_allocations: HashMap::new(),
            category_limits: HashMap::new(),
            expenses: HashMap::new(),
            allocation_rules: Vec::new(),
            analytics: HashMap::new(),
        };
        
        // Initialize with default data for current year
        let current_year = Utc::now().year() as i64;
        state.initialize_year(current_year);
        
        state
    }
    
    fn initialize_year(&mut self, year: i64) {
        // Initialize allocations for the year if not present
        if !self.quarterly_allocations.contains_key(&year) {
            self.quarterly_allocations.insert(
                year,
                MockDataGenerator::generate_quarterly_allocations(100000.0)
            );
        }
        
        if !self.department_allocations.contains_key(&year) {
            self.department_allocations.insert(
                year,
                MockDataGenerator::generate_department_allocations(100000.0)
            );
        }
        
        if !self.category_limits.contains_key(&year) {
            self.category_limits.insert(
                year,
                MockDataGenerator::generate_category_limits()
            );
        }
        
        if !self.expenses.contains_key(&year) {
            self.expenses.insert(
                year,
                MockDataGenerator::generate_sample_expenses()
            );
        }
        
        if self.allocation_rules.is_empty() {
            self.allocation_rules = MockDataGenerator::generate_allocation_rules();
        }
        
        if !self.analytics.contains_key(&year) {
            self.analytics.insert(
                year,
                MockDataGenerator::generate_analytics(year)
            );
        }
    }
}

// Quarterly Allocations Commands
// Commented out - now using real backend in budget_extended.rs
/*
#[tauri::command]
pub fn get_quarterly_allocations(year: i64) -> Result<Vec<QuarterlyAllocation>, String> {
    let mut state = MOCK_STATE.lock().unwrap();
    state.initialize_year(year);
    
    Ok(state.quarterly_allocations
        .get(&year)
        .cloned()
        .unwrap_or_default())
}
*/

// Commented out - now using real backend in budget_extended.rs
/*
#[tauri::command]
pub fn update_quarterly_allocations(
    year: i64,
    allocations: Vec<QuarterlyAllocation>
) -> Result<(), String> {
    let mut state = MOCK_STATE.lock().unwrap();
    state.quarterly_allocations.insert(year, allocations);
    Ok(())
}
*/

// Department Allocations Commands
#[tauri::command]
pub fn get_department_allocations(year: i64) -> Result<Vec<DepartmentAllocation>, String> {
    let mut state = MOCK_STATE.lock().unwrap();
    state.initialize_year(year);
    
    Ok(state.department_allocations
        .get(&year)
        .cloned()
        .unwrap_or_default())
}

#[tauri::command]
pub fn update_department_allocation(
    year: i64,
    department_id: String,
    amount: f64
) -> Result<(), String> {
    let mut state = MOCK_STATE.lock().unwrap();
    state.initialize_year(year);
    
    if let Some(departments) = state.department_allocations.get_mut(&year) {
        // First calculate the total
        let total: f64 = departments.iter().map(|d| {
            if d.id == department_id { amount } else { d.allocated_amount }
        }).sum();
        
        // Then update the department
        if let Some(dept) = departments.iter_mut().find(|d| d.id == department_id) {
            dept.allocated_amount = amount;
            dept.percentage = (amount / total) * 100.0;
        }
    }
    
    Ok(())
}

#[tauri::command]
pub fn add_department(name: String, amount: f64) -> Result<(), String> {
    let mut state = MOCK_STATE.lock().unwrap();
    let year = Utc::now().year() as i64;
    state.initialize_year(year);
    
    let new_dept = DepartmentAllocation {
        id: format!("dept-{}", Utc::now().timestamp()),
        name,
        allocated_amount: amount,
        spent_amount: 0.0,
        percentage: 0.0, // Will be recalculated
    };
    
    if let Some(departments) = state.department_allocations.get_mut(&year) {
        departments.push(new_dept);
        
        // Recalculate percentages
        let total: f64 = departments.iter().map(|d| d.allocated_amount).sum();
        for dept in departments.iter_mut() {
            dept.percentage = (dept.allocated_amount / total) * 100.0;
        }
    }
    
    Ok(())
}

#[tauri::command]
pub fn remove_department(department_id: String) -> Result<(), String> {
    let mut state = MOCK_STATE.lock().unwrap();
    let year = Utc::now().year() as i64;
    
    if let Some(departments) = state.department_allocations.get_mut(&year) {
        departments.retain(|d| d.id != department_id);
        
        // Recalculate percentages
        let total: f64 = departments.iter().map(|d| d.allocated_amount).sum();
        for dept in departments.iter_mut() {
            dept.percentage = (dept.allocated_amount / total) * 100.0;
        }
    }
    
    Ok(())
}

// Category Limits Commands
#[tauri::command]
pub fn get_category_limits(year: i64) -> Result<Vec<CategoryLimit>, String> {
    let mut state = MOCK_STATE.lock().unwrap();
    state.initialize_year(year);
    
    Ok(state.category_limits
        .get(&year)
        .cloned()
        .unwrap_or_default())
}

#[tauri::command]
pub fn set_category_limit(
    year: i64,
    category: String,
    limit: f64
) -> Result<(), String> {
    let mut state = MOCK_STATE.lock().unwrap();
    state.initialize_year(year);
    
    if let Some(limits) = state.category_limits.get_mut(&year) {
        if let Some(cat_limit) = limits.iter_mut().find(|l| l.category == category) {
            cat_limit.limit_amount = limit;
            cat_limit.remaining = limit - cat_limit.spent_amount;
        } else {
            // Add new category limit
            limits.push(CategoryLimit {
                category,
                limit_amount: limit,
                spent_amount: 0.0,
                remaining: limit,
            });
        }
    }
    
    Ok(())
}

#[tauri::command]
pub fn remove_category_limit(year: i64, category: String) -> Result<(), String> {
    let mut state = MOCK_STATE.lock().unwrap();
    
    if let Some(limits) = state.category_limits.get_mut(&year) {
        limits.retain(|l| l.category != category);
    }
    
    Ok(())
}

// Expense Commands - Commented out in favor of database-backed expense commands
// #[tauri::command]
// pub fn get_expenses(year: i64) -> Result<Vec<Expense>, String> {
//     let mut state = MOCK_STATE.lock().unwrap();
//     state.initialize_year(year);
    
//     Ok(state.expenses
//         .get(&year)
//         .cloned()
//         .unwrap_or_default())
// }

// #[tauri::command]
// pub fn add_expense(expense: Expense) -> Result<Expense, String> {
//     let mut state = MOCK_STATE.lock().unwrap();
//     let year = Utc::now().year() as i64;
//     state.initialize_year(year);
    
//     let mut new_expense = expense;
//     new_expense.id = format!("exp-{}", Utc::now().timestamp());
//     new_expense.created_at = Utc::now().to_rfc3339();
//     new_expense.updated_at = Utc::now().to_rfc3339();
    
//     if let Some(expenses) = state.expenses.get_mut(&year) {
//         expenses.push(new_expense.clone());
//     }
    
//     Ok(new_expense)
// }

// #[tauri::command]
// pub fn update_expense(id: String, updates: serde_json::Value) -> Result<(), String> {
//     let mut state = MOCK_STATE.lock().unwrap();
//     let year = Utc::now().year() as i64;
    
//     if let Some(expenses) = state.expenses.get_mut(&year) {
//         if let Some(expense) = expenses.iter_mut().find(|e| e.id == id) {
//             // Update fields based on the updates object
//             if let Some(obj) = updates.as_object() {
//                 if let Some(title) = obj.get("title").and_then(|v| v.as_str()) {
//                     expense.title = title.to_string();
//                 }
//                 if let Some(description) = obj.get("description").and_then(|v| v.as_str()) {
//                     expense.description = description.to_string();
//                 }
//                 if let Some(amount) = obj.get("amount").and_then(|v| v.as_f64()) {
//                     expense.amount = amount;
//                 }
//                 if let Some(category) = obj.get("category").and_then(|v| v.as_str()) {
//                     expense.category = category.to_string();
//                 }
//                 if let Some(status) = obj.get("status").and_then(|v| v.as_str()) {
//                     expense.status = status.to_string();
//                 }
//                 expense.updated_at = Utc::now().to_rfc3339();
//             }
//         }
//     }
    
//     Ok(())
// }

// #[tauri::command]
// pub fn delete_expense(id: String) -> Result<(), String> {
//     let mut state = MOCK_STATE.lock().unwrap();
//     let year = Utc::now().year() as i64;
    
//     if let Some(expenses) = state.expenses.get_mut(&year) {
//         expenses.retain(|e| e.id != id);
//     }
    
//     Ok(())
// }

// #[tauri::command]
// pub fn update_expense_status(id: String, status: String) -> Result<(), String> {
//     let mut state = MOCK_STATE.lock().unwrap();
//     let year = Utc::now().year() as i64;
    
//     if let Some(expenses) = state.expenses.get_mut(&year) {
//         if let Some(expense) = expenses.iter_mut().find(|e| e.id == id) {
//             expense.status = status;
//             expense.updated_at = Utc::now().to_rfc3339();
//         }
//     }
    
//     Ok(())
// }

// Allocation Rules Commands
#[tauri::command]
pub fn get_allocation_rules() -> Result<Vec<AllocationRule>, String> {
    let state = MOCK_STATE.lock().unwrap();
    Ok(state.allocation_rules.clone())
}

#[tauri::command]
pub fn add_allocation_rule(rule: AllocationRule) -> Result<AllocationRule, String> {
    let mut state = MOCK_STATE.lock().unwrap();
    
    let mut new_rule = rule;
    new_rule.id = format!("rule-{}", Utc::now().timestamp());
    new_rule.created_at = Utc::now().to_rfc3339();
    new_rule.updated_at = Utc::now().to_rfc3339();
    
    state.allocation_rules.push(new_rule.clone());
    
    Ok(new_rule)
}

#[tauri::command]
pub fn update_allocation_rule(id: String, updates: serde_json::Value) -> Result<(), String> {
    let mut state = MOCK_STATE.lock().unwrap();
    
    if let Some(rule) = state.allocation_rules.iter_mut().find(|r| r.id == id) {
        if let Some(obj) = updates.as_object() {
            if let Some(name) = obj.get("name").and_then(|v| v.as_str()) {
                rule.name = name.to_string();
            }
            if let Some(description) = obj.get("description").and_then(|v| v.as_str()) {
                rule.description = description.to_string();
            }
            if let Some(enabled) = obj.get("enabled").and_then(|v| v.as_bool()) {
                rule.enabled = enabled;
            }
            if let Some(priority) = obj.get("priority").and_then(|v| v.as_i64()) {
                rule.priority = priority as i32;
            }
            rule.updated_at = Utc::now().to_rfc3339();
        }
    }
    
    Ok(())
}

#[tauri::command]
pub fn delete_allocation_rule(id: String) -> Result<(), String> {
    let mut state = MOCK_STATE.lock().unwrap();
    state.allocation_rules.retain(|r| r.id != id);
    Ok(())
}

#[tauri::command]
pub fn toggle_allocation_rule(id: String, enabled: bool) -> Result<(), String> {
    let mut state = MOCK_STATE.lock().unwrap();
    
    if let Some(rule) = state.allocation_rules.iter_mut().find(|r| r.id == id) {
        rule.enabled = enabled;
        rule.updated_at = Utc::now().to_rfc3339();
    }
    
    Ok(())
}

#[tauri::command]
pub fn execute_allocation_rule(id: String) -> Result<(), String> {
    let mut state = MOCK_STATE.lock().unwrap();
    
    if let Some(rule) = state.allocation_rules.iter_mut().find(|r| r.id == id) {
        rule.last_triggered = Some(Utc::now().to_rfc3339());
        rule.updated_at = Utc::now().to_rfc3339();
    }
    
    Ok(())
}

// Analytics Commands
#[tauri::command]
pub fn get_budget_analytics(year: i64) -> Result<BudgetAnalytics, String> {
    let mut state = MOCK_STATE.lock().unwrap();
    state.initialize_year(year);
    
    Ok(state.analytics
        .get(&year)
        .cloned()
        .unwrap_or_else(|| MockDataGenerator::generate_analytics(year)))
}

// Template Commands (Simple implementations for now)
#[tauri::command]
pub fn get_budget_templates() -> Result<Vec<serde_json::Value>, String> {
    // Return empty array for now
    Ok(vec![])
}

#[tauri::command]
pub fn save_budget_template(template: serde_json::Value) -> Result<(), String> {
    // Mock implementation - just log and return success
    println!("Saving budget template: {:?}", template);
    Ok(())
}

#[tauri::command]
pub fn apply_budget_template(template_id: String, year: i64) -> Result<(), String> {
    println!("Applying template {} to year {}", template_id, year);
    Ok(())
}

#[tauri::command]
pub fn delete_budget_template(template_id: String) -> Result<(), String> {
    println!("Deleting template {}", template_id);
    Ok(())
}

// Export Analytics (Mock implementation)
#[tauri::command]
pub fn export_analytics(
    _analytics: serde_json::Value,
    year: i64,
    format: String
) -> Result<(), String> {
    println!("Exporting analytics for year {} in format {}", year, format);
    Ok(())
}
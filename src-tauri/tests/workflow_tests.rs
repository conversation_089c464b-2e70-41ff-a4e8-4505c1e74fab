#[cfg(test)]
mod workflow_tests {
    use claudia_lib::workflow::{
        WorkflowEngine, executor::WorkflowExecutor, TriggerManager, TemplateManager,
        Workflow, WorkflowStep, StepType, StepConfig, ExecutionStatus,
        CreateWorkflowRequest, UpdateWorkflowRequest, ExecuteWorkflowRequest,
        WorkflowError, TriggerType, WorkflowTemplate, WorkflowExecution,
        Condition, RetryConfig, ErrorHandling, TriggerConfig, ExecutionLogEntry,
        StepStatus,
    };
    use std::collections::HashMap;
    use std::path::PathBuf;
    use tempfile::TempDir;
    use serde_json::json;

    fn setup_test_db() -> (TempDir, PathBuf) {
        let temp_dir = TempDir::new().unwrap();
        let db_path = temp_dir.path().join("test.db");
        (temp_dir, db_path)
    }

    fn create_test_workflow() -> Workflow {
        Workflow {
            id: 0,
            name: "Test Workflow".to_string(),
            description: Some("Test workflow description".to_string()),
            category: "Testing".to_string(),
            trigger: claudia_lib::workflow::WorkflowTrigger::Manual,
            trigger_config: None,
            steps: vec![
                WorkflowStep {
                    id: "step1".to_string(),
                    name: "Echo Step".to_string(),
                    step_type: StepType::Command {
                        command: "echo".to_string(),
                        args: vec!["Hello, World!".to_string()],
                    },
                    config: StepConfig {
                        input_mapping: HashMap::new(),
                        output_mapping: HashMap::from([
                            ("output".to_string(), "echo_output".to_string())
                        ]),
                        error_handling: claudia_lib::workflow::ErrorHandling::Fail,
                    },
                    conditions: None,
                    retry_config: None,
                    timeout_ms: Some(5000),
                    on_success: Some(vec!["step2".to_string()]),
                    on_failure: None,
                },
                WorkflowStep {
                    id: "step2".to_string(),
                    name: "Script Step".to_string(),
                    step_type: StepType::Script {
                        language: "bash".to_string(),
                        code: "echo \"Step 2 executed\"".to_string(),
                    },
                    config: StepConfig {
                        input_mapping: HashMap::from([
                            ("previous_output".to_string(), "echo_output".to_string())
                        ]),
                        output_mapping: HashMap::new(),
                        error_handling: claudia_lib::workflow::ErrorHandling::Continue,
                    },
                    conditions: None,
                    retry_config: None,
                    timeout_ms: None,
                    on_success: None,
                    on_failure: None,
                },
            ],
            variables: HashMap::from([
                ("test_var".to_string(), json!("test_value"))
            ]),
            is_active: true,
            is_template: false,
            created_by: None,
            created_at: chrono::Utc::now(),
            updated_at: chrono::Utc::now(),
        }
    }

    #[tokio::test]
    async fn test_workflow_engine_create() {
        let (_temp_dir, db_path) = setup_test_db();
        let engine = WorkflowEngine::new(db_path);

        let request = CreateWorkflowRequest {
            name: "Test Workflow".to_string(),
            description: Some("Test description".to_string()),
            category: "Testing".to_string(),
            steps: vec![],
            variables: HashMap::new(),
            trigger_config: None,
        };

        let result = engine.create_workflow(request).await;
        assert!(result.is_ok());
        
        let workflow = result.unwrap();
        assert_eq!(workflow.name, "Test Workflow");
        assert_eq!(workflow.category, "Testing");
        assert!(workflow.id > 0);
    }

    #[tokio::test]
    async fn test_workflow_engine_update() {
        let (_temp_dir, db_path) = setup_test_db();
        let engine = WorkflowEngine::new(db_path);

        // Create workflow
        let create_request = CreateWorkflowRequest {
            name: "Original Name".to_string(),
            description: None,
            category: "Testing".to_string(),
            steps: vec![],
            variables: HashMap::new(),
            trigger_config: None,
        };
        let workflow = engine.create_workflow(create_request).await.unwrap();

        // Update workflow
        let update_request = UpdateWorkflowRequest {
            name: Some("Updated Name".to_string()),
            description: Some("New description".to_string()),
            steps: None,
            variables: None,
            is_active: Some(false),
        };
        let updated = engine.update_workflow(workflow.id, update_request).await.unwrap();

        assert_eq!(updated.name, "Updated Name");
        assert_eq!(updated.description, Some("New description".to_string()));
        assert_eq!(updated.is_active, false);
    }

    #[tokio::test]
    async fn test_workflow_engine_delete() {
        let (_temp_dir, db_path) = setup_test_db();
        let engine = WorkflowEngine::new(db_path);

        // Create workflow
        let request = CreateWorkflowRequest {
            name: "To Delete".to_string(),
            description: None,
            category: "Testing".to_string(),
            steps: vec![],
            variables: HashMap::new(),
            trigger_config: None,
        };
        let workflow = engine.create_workflow(request).await.unwrap();

        // Delete workflow
        let result = engine.delete_workflow(workflow.id).await;
        assert!(result.is_ok());

        // Try to get deleted workflow
        let get_result = engine.get_workflow(workflow.id).await;
        assert!(get_result.is_err());
    }

    #[tokio::test]
    async fn test_workflow_executor_simple() {
        let (_temp_dir, db_path) = setup_test_db();
        let executor = WorkflowExecutor::new(db_path.clone());
        
        let workflow = create_test_workflow();
        let variables = HashMap::from([
            ("input_value".to_string(), json!("test"))
        ]);

        let execution = WorkflowExecution {
            id: 1,
            workflow_id: workflow.id,
            workflow: Some(workflow),
            trigger_data: None,
            status: ExecutionStatus::Pending,
            current_step: 0,
            started_at: chrono::Utc::now(),
            completed_at: None,
            error_message: None,
            execution_log: Vec::new(),
            variables,
        };

        let result = executor.execute(execution).await;
        assert!(result.is_ok());
    }

    #[tokio::test]
    async fn test_workflow_executor_with_conditions() {
        let (_temp_dir, db_path) = setup_test_db();
        let executor = WorkflowExecutor::new(db_path);
        
        let mut workflow = create_test_workflow();
        workflow.steps[0].conditions = Some(vec![
            Condition {
                field: "should_run".to_string(),
                operator: claudia_lib::workflow::ConditionOperator::Equals,
                value: json!(true),
            }
        ]);

        // Test with condition met
        let variables = HashMap::from([
            ("should_run".to_string(), json!(true))
        ]);
        let execution1 = WorkflowExecution {
            id: 1,
            workflow_id: workflow.id,
            workflow: Some(workflow.clone()),
            trigger_data: None,
            status: ExecutionStatus::Pending,
            current_step: 0,
            started_at: chrono::Utc::now(),
            completed_at: None,
            error_message: None,
            execution_log: Vec::new(),
            variables,
        };
        let result = executor.execute(execution1).await;
        assert!(result.is_ok());

        // Test with condition not met
        let variables = HashMap::from([
            ("should_run".to_string(), json!(false))
        ]);
        let mut execution2 = WorkflowExecution {
            id: 2,
            workflow_id: workflow.id,
            workflow: Some(workflow),
            trigger_data: None,
            status: ExecutionStatus::Pending,
            current_step: 0,
            started_at: chrono::Utc::now(),
            completed_at: None,
            error_message: None,
            execution_log: Vec::new(),
            variables,
        };
        let result = executor.execute(execution2).await;
        assert!(result.is_ok());
    }

    #[tokio::test]
    async fn test_workflow_executor_with_retry() {
        let (_temp_dir, db_path) = setup_test_db();
        let executor = WorkflowExecutor::new(db_path);
        
        let mut workflow = create_test_workflow();
        workflow.steps[0].retry_config = Some(RetryConfig {
            max_attempts: 3,
            delay_ms: 100,
            backoff_multiplier: 2.0,
        });
        
        // Simulate a failing command
        workflow.steps[0].step_type = StepType::Command {
            command: "false".to_string(), // Always fails
            args: vec![],
        };

        let mut execution = WorkflowExecution {
            id: 1,
            workflow_id: workflow.id,
            workflow: Some(workflow),
            trigger_data: None,
            status: ExecutionStatus::Pending,
            current_step: 0,
            started_at: chrono::Utc::now(),
            completed_at: None,
            error_message: None,
            execution_log: Vec::new(),
            variables: HashMap::new(),
        };

        let result = executor.execute(execution).await;
        assert!(result.is_ok());
    }

    #[tokio::test]
    async fn test_template_manager_create() {
        let (_temp_dir, db_path) = setup_test_db();
        let manager = TemplateManager::new(db_path);

        let template = WorkflowTemplate {
            id: 0,
            name: "Test Template".to_string(),
            description: Some("Template description".to_string()),
            category: "Testing".to_string(),
            icon: None,
            template_data: Workflow {
                id: 0,
                name: "Test Template".to_string(),
                description: Some("Template description".to_string()),
                category: "Testing".to_string(),
                trigger: claudia_lib::workflow::WorkflowTrigger::Manual,
                trigger_config: None,
                steps: vec![],
                variables: HashMap::from([
                    ("var1".to_string(), json!("default_value"))
                ]),
                is_active: true,
                is_template: true,
                created_by: None,
                created_at: chrono::Utc::now(),
                updated_at: chrono::Utc::now(),
            },
            variables_schema: None,
            usage_count: 0,
            author: Some("Test Author".to_string()),
            version: None,
            tags: vec!["test".to_string(), "template".to_string()],
            created_at: chrono::Utc::now(),
            updated_at: chrono::Utc::now(),
        };

        let result = manager.create_template(template).await;
        assert!(result.is_ok());
        
        let created = result.unwrap();
        assert!(created.id > 0);
        assert_eq!(created.name, "Test Template");
    }

    #[tokio::test]
    async fn test_template_manager_instantiate() {
        let (_temp_dir, db_path) = setup_test_db();
        let manager = TemplateManager::new(db_path);

        // Create template
        let template = WorkflowTemplate {
            id: 0,
            name: "Parameterized Template".to_string(),
            description: Some("Template with variables".to_string()),
            category: "Testing".to_string(),
            icon: None,
            template_data: Workflow {
                id: 0,
                name: "Parameterized Template".to_string(),
                description: Some("Template with variables".to_string()),
                category: "Testing".to_string(),
                trigger: claudia_lib::workflow::WorkflowTrigger::Manual,
                trigger_config: None,
                steps: vec![
                    WorkflowStep {
                        id: "step1".to_string(),
                        name: "Build {{project_name}}".to_string(),
                        step_type: StepType::Command {
                            command: "echo".to_string(),
                            args: vec!["Building {{project_name}} v{{version}}".to_string()],
                        },
                        config: StepConfig {
                            input_mapping: HashMap::new(),
                            output_mapping: HashMap::new(),
                            error_handling: ErrorHandling::Continue,
                        },
                        conditions: None,
                        retry_config: None,
                        timeout_ms: None,
                        on_success: None,
                        on_failure: None,
                    }
                ],
                variables: HashMap::from([
                    ("project_name".to_string(), json!("{{project_name}}")),
                    ("version".to_string(), json!("{{version}}"))
                ]),
                is_active: true,
                is_template: true,
                created_by: None,
                created_at: chrono::Utc::now(),
                updated_at: chrono::Utc::now(),
            },
            variables_schema: None,
            usage_count: 0,
            author: None,
            version: None,
            tags: vec![],
            created_at: chrono::Utc::now(),
            updated_at: chrono::Utc::now(),
        };
        let created_template = manager.create_template(template).await.unwrap();

        // Instantiate template
        let variables = HashMap::from([
            ("project_name".to_string(), json!("MyApp")),
            ("version".to_string(), json!("1.0.0"))
        ]);
        let workflow = manager.instantiate_template(
            created_template.id,
            "My Workflow".to_string(),
            variables
        ).await.unwrap();

        assert_eq!(workflow.name, "My Workflow");
        assert!(workflow.steps[0].name.contains("MyApp"));
        // Check that template variables were replaced
        let command_args = match &workflow.steps[0].step_type {
            StepType::Command { args, .. } => args,
            _ => panic!("Expected command step"),
        };
        assert!(command_args[0].contains("MyApp"));
        assert!(command_args[0].contains("1.0.0"));
    }

    #[tokio::test]
    async fn test_template_manager_search() {
        let (_temp_dir, db_path) = setup_test_db();
        let manager = TemplateManager::new(db_path);

        // Create multiple templates with different tags
        let template1 = WorkflowTemplate {
            id: 0,
            name: "CI Template".to_string(),
            description: Some("CI/CD template".to_string()),
            category: "DevOps".to_string(),
            icon: None,
            template_data: Workflow {
                id: 0,
                name: "CI Template".to_string(),
                description: Some("CI/CD template".to_string()),
                category: "DevOps".to_string(),
                trigger: claudia_lib::workflow::WorkflowTrigger::Manual,
                trigger_config: None,
                steps: vec![],
                variables: HashMap::new(),
                is_active: true,
                is_template: true,
                created_by: None,
                created_at: chrono::Utc::now(),
                updated_at: chrono::Utc::now(),
            },
            variables_schema: None,
            usage_count: 0,
            author: None,
            version: None,
            tags: vec!["ci".to_string(), "testing".to_string()],
            created_at: chrono::Utc::now(),
            updated_at: chrono::Utc::now(),
        };
        manager.create_template(template1).await.unwrap();

        let template2 = WorkflowTemplate {
            id: 0,
            name: "Deploy Template".to_string(),
            description: Some("Deployment template".to_string()),
            category: "DevOps".to_string(),
            icon: None,
            template_data: Workflow {
                id: 0,
                name: "Deploy Template".to_string(),
                description: Some("Deployment template".to_string()),
                category: "DevOps".to_string(),
                trigger: claudia_lib::workflow::WorkflowTrigger::Manual,
                trigger_config: None,
                steps: vec![],
                variables: HashMap::new(),
                is_active: true,
                is_template: true,
                created_by: None,
                created_at: chrono::Utc::now(),
                updated_at: chrono::Utc::now(),
            },
            variables_schema: None,
            usage_count: 0,
            author: None,
            version: None,
            tags: vec!["deploy".to_string(), "production".to_string()],
            created_at: chrono::Utc::now(),
            updated_at: chrono::Utc::now(),
        };
        manager.create_template(template2).await.unwrap();

        // Search by tags
        let results = manager.search_templates(vec!["ci".to_string()]).await.unwrap();
        assert_eq!(results.len(), 1);
        assert_eq!(results[0].name, "CI Template");

        // List by category
        let results = manager.list_templates(Some("DevOps".to_string())).await.unwrap();
        assert_eq!(results.len(), 2);
    }

    #[tokio::test]
    async fn test_trigger_manager_schedule() {
        let (_temp_dir, db_path) = setup_test_db();
        let manager = TriggerManager::new(db_path);

        let trigger_type = TriggerType::Schedule {
            cron: "*/5 * * * *".to_string(),
        };
        let config = json!({
            "cron": "*/5 * * * *"
        });

        let result = manager.create_trigger(1, trigger_type, config).await;
        assert!(result.is_ok());
        
        let trigger = result.unwrap();
        assert_eq!(trigger.workflow_id, 1);
        assert!(trigger.is_active);
    }

    #[tokio::test]
    async fn test_trigger_manager_file_watch() {
        let (_temp_dir, db_path) = setup_test_db();
        let manager = TriggerManager::new(db_path);

        let trigger_type = TriggerType::FileWatch {
            path: "/tmp/test".to_string(),
            events: vec!["create".to_string(), "modify".to_string()],
        };
        let config = json!({
            "paths": ["/tmp/test"],
            "events": ["create", "modify"]
        });

        let result = manager.create_trigger(1, trigger_type, config).await;
        assert!(result.is_ok());
        
        let trigger = result.unwrap();
        let paths = trigger.trigger_config["paths"].as_array().unwrap();
        assert_eq!(paths.len(), 1);
    }

    #[tokio::test]
    async fn test_workflow_validation() {
        let mut workflow = create_test_workflow();
        
        // Valid workflow
        assert!(validate_workflow(&workflow).unwrap());
        
        // Invalid: duplicate step IDs
        workflow.steps.push(WorkflowStep {
            id: "step1".to_string(), // Duplicate ID
            name: "Duplicate".to_string(),
            step_type: StepType::Command {
                command: "echo".to_string(),
                args: vec![],
            },
            config: StepConfig {
                input_mapping: HashMap::new(),
                output_mapping: HashMap::new(),
                error_handling: ErrorHandling::Continue,
            },
            conditions: None,
            retry_config: None,
            timeout_ms: None,
            on_success: None,
            on_failure: None,
        });
        assert!(!validate_workflow(&workflow).unwrap());
        
        // Invalid: reference to non-existent step
        workflow.steps[0].on_success = Some(vec!["non_existent".to_string()]);
        assert!(!validate_workflow(&workflow).unwrap());
    }

    #[tokio::test]
    async fn test_parallel_execution() {
        let (_temp_dir, db_path) = setup_test_db();
        let executor = WorkflowExecutor::new(db_path);
        
        let workflow = Workflow {
            id: 0,
            name: "Parallel Workflow".to_string(),
            description: None,
            category: "Testing".to_string(),
            trigger: claudia_lib::workflow::WorkflowTrigger::Manual,
            trigger_config: None,
            steps: vec![
                WorkflowStep {
                    id: "parallel_group".to_string(),
                    name: "Parallel Tasks".to_string(),
                    step_type: StepType::Parallel {
                        branches: vec![
                            vec![WorkflowStep {
                                id: "task1".to_string(),
                                name: "Task 1".to_string(),
                                step_type: StepType::Command {
                                    command: "echo".to_string(),
                                    args: vec!["Task 1".to_string()],
                                },
                                config: StepConfig {
                                    input_mapping: HashMap::new(),
                                    output_mapping: HashMap::new(),
                                    error_handling: ErrorHandling::Continue,
                                },
                                conditions: None,
                                retry_config: None,
                                timeout_ms: None,
                                on_success: None,
                                on_failure: None,
                            }],
                            vec![WorkflowStep {
                                id: "task2".to_string(),
                                name: "Task 2".to_string(),
                                step_type: StepType::Command {
                                    command: "echo".to_string(),
                                    args: vec!["Task 2".to_string()],
                                },
                                config: StepConfig {
                                    input_mapping: HashMap::new(),
                                    output_mapping: HashMap::new(),
                                    error_handling: ErrorHandling::Continue,
                                },
                                conditions: None,
                                retry_config: None,
                                timeout_ms: None,
                                on_success: None,
                                on_failure: None,
                            }],
                        ],
                    },
                    config: StepConfig {
                input_mapping: HashMap::new(),
                output_mapping: HashMap::new(),
                error_handling: ErrorHandling::Continue,
            },
                    conditions: None,
                    retry_config: None,
                    timeout_ms: Some(10000),
                    on_success: None,
                    on_failure: None,
                },
            ],
            variables: HashMap::new(),
            is_active: true,
            is_template: false,
            created_by: None,
            created_at: chrono::Utc::now(),
            updated_at: chrono::Utc::now(),
        };

        let execution = WorkflowExecution {
            id: 1,
            workflow_id: workflow.id,
            workflow: Some(workflow),
            trigger_data: None,
            status: ExecutionStatus::Pending,
            current_step: 0,
            started_at: chrono::Utc::now(),
            completed_at: None,
            error_message: None,
            execution_log: Vec::new(),
            variables: HashMap::new(),
        };

        let result = executor.execute(execution).await;
        assert!(result.is_ok());
    }

    // Helper function for validation
    fn validate_workflow(workflow: &Workflow) -> Result<bool, WorkflowError> {
        if workflow.steps.is_empty() {
            return Ok(false);
        }
        
        let mut step_ids = std::collections::HashSet::new();
        for step in &workflow.steps {
            if !step_ids.insert(&step.id) {
                return Ok(false);
            }
        }
        
        for step in &workflow.steps {
            if let Some(on_success) = &step.on_success {
                for next_id in on_success {
                    if !workflow.steps.iter().any(|s| &s.id == next_id) {
                        return Ok(false);
                    }
                }
            }
            if let Some(on_failure) = &step.on_failure {
                for next_id in on_failure {
                    if !workflow.steps.iter().any(|s| &s.id == next_id) {
                        return Ok(false);
                    }
                }
            }
        }
        
        Ok(true)
    }
}
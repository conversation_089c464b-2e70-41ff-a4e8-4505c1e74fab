{"name": "claudia", "private": true, "version": "0.1.0", "license": "AGPL-3.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "tauri": "tauri", "check": "tsc --noEmit && cd src-tauri && cargo check"}, "dependencies": {"@hookform/resolvers": "^3.9.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-select": "^2.1.3", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.3", "@radix-ui/react-toggle": "^1.1.10", "@radix-ui/react-tooltip": "^1.1.5", "@tailwindcss/cli": "^4.1.8", "@tailwindcss/vite": "^4.1.8", "@tanstack/react-virtual": "^3.13.10", "@tauri-apps/api": "^2.1.1", "@tauri-apps/plugin-dialog": "^2.0.2", "@tauri-apps/plugin-global-shortcut": "^2.0.0", "@tauri-apps/plugin-opener": "^2", "@tauri-apps/plugin-shell": "^2.0.1", "@types/canvas-confetti": "^1.9.0", "@types/diff": "^8.0.0", "@types/html2canvas": "^0.5.35", "@types/jspdf": "^1.3.3", "@types/react-syntax-highlighter": "^15.5.13", "@types/recharts": "^1.8.29", "@uiw/react-md-editor": "^4.0.7", "ansi-to-html": "^0.7.2", "canvas-confetti": "^1.9.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^3.6.0", "diff": "^8.0.2", "framer-motion": "^12.0.0-alpha.1", "html2canvas": "^1.4.1", "immer": "^10.1.1", "jspdf": "^3.0.1", "lucide-react": "^0.468.0", "mdast-util-to-string": "^4.0.0", "papaparse": "^5.5.3", "posthog-js": "^1.258.3", "react": "^18.3.1", "react-day-picker": "^9.8.1", "react-dom": "^18.3.1", "react-dropzone": "^14.3.8", "react-hook-form": "^7.54.2", "react-hot-toast": "^2.5.2", "react-markdown": "^9.0.3", "react-router-dom": "^6.26.2", "react-syntax-highlighter": "^15.6.1", "recharts": "^2.15.4", "remark-gfm": "^4.0.0", "remark-parse": "^11.0.0", "tailwind-merge": "^2.6.0", "tailwindcss": "^4.1.8", "unified": "^11.0.5", "unist-util-visit": "^5.0.0", "zod": "^3.24.1", "zustand": "^5.0.6"}, "devDependencies": {"@tauri-apps/cli": "^2.7.1", "@testing-library/react": "^16.3.0", "@types/mdast": "^4.0.4", "@types/node": "^22.15.30", "@types/papaparse": "^5.3.16", "@types/react": "^18.3.1", "@types/react-dom": "^18.3.1", "@types/sharp": "^0.32.0", "@types/unist": "^3.0.3", "@vitejs/plugin-react": "^4.3.4", "@vitest/ui": "^3.2.4", "sharp": "^0.34.2", "typescript": "~5.9.2", "vite": "^6.0.3", "vitest": "^3.2.4"}, "trustedDependencies": ["@parcel/watcher", "@tailwindcss/oxide"]}